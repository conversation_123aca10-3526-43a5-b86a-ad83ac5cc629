<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>DocumentTemplateRepository.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.repository</a> &gt; <span class="el_source">DocumentTemplateRepository.java</span></div><h1>DocumentTemplateRepository.java</h1><pre class="source lang-java linenums">package com.ascentbusiness.dms_svc.repository;

import com.ascentbusiness.dms_svc.entity.DocumentTemplate;
import com.ascentbusiness.dms_svc.enums.TemplateType;
import com.ascentbusiness.dms_svc.enums.TemplateApprovalStatus;
import com.ascentbusiness.dms_svc.enums.TemplateAccessLevel;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Repository interface for DocumentTemplate entities
 */
@Repository
public interface DocumentTemplateRepository extends JpaRepository&lt;DocumentTemplate, Long&gt; {

    /**
     * Find templates by name
     */
    List&lt;DocumentTemplate&gt; findByNameContainingIgnoreCase(String name);

    /**
     * Find templates by category
     */
    List&lt;DocumentTemplate&gt; findByCategory(String category);

    /**
     * Find templates by type
     */
    List&lt;DocumentTemplate&gt; findByTemplateType(TemplateType templateType);

    /**
     * Find active templates
     */
    List&lt;DocumentTemplate&gt; findByIsActiveTrue();

    /**
     * Find published templates
     */
    List&lt;DocumentTemplate&gt; findByApprovalStatusAndIsActiveTrue(TemplateApprovalStatus approvalStatus);

    /**
     * Find public templates
     */
    List&lt;DocumentTemplate&gt; findByIsPublicTrueAndApprovalStatusAndIsActiveTrue(TemplateApprovalStatus approvalStatus);

    /**
     * Find templates by owner
     */
    List&lt;DocumentTemplate&gt; findByOwnerUserId(String ownerUserId);

    /**
     * Find templates by owner with pagination
     */
    Page&lt;DocumentTemplate&gt; findByOwnerUserId(String ownerUserId, Pageable pageable);

    /**
     * Find templates by department
     */
    List&lt;DocumentTemplate&gt; findByOwnerDepartment(String department);

    /**
     * Find templates by access level
     */
    List&lt;DocumentTemplate&gt; findByAccessLevel(TemplateAccessLevel accessLevel);

    /**
     * Find templates accessible to user (public or owned by user)
     */
    @Query(&quot;SELECT dt FROM DocumentTemplate dt WHERE dt.isActive = true AND dt.approvalStatus = 'PUBLISHED' AND (dt.isPublic = true OR dt.ownerUserId = :userId)&quot;)
    List&lt;DocumentTemplate&gt; findAccessibleTemplates(@Param(&quot;userId&quot;) String userId);

    /**
     * Find templates accessible to department
     */
    @Query(&quot;SELECT dt FROM DocumentTemplate dt WHERE dt.isActive = true AND dt.approvalStatus = 'PUBLISHED' AND (dt.isPublic = true OR dt.ownerDepartment = :department OR dt.ownerUserId = :userId)&quot;)
    List&lt;DocumentTemplate&gt; findAccessibleTemplatesByDepartment(@Param(&quot;userId&quot;) String userId, @Param(&quot;department&quot;) String department);

    /**
     * Find templates by name and version
     */
    Optional&lt;DocumentTemplate&gt; findByNameAndVersion(String name, String version);

    /**
     * Check if template name exists
     */
    boolean existsByName(String name);

    /**
     * Check if template name and version combination exists
     */
    boolean existsByNameAndVersion(String name, String version);

    /**
     * Find system templates
     */
    List&lt;DocumentTemplate&gt; findByIsSystemTemplateTrueAndIsActiveTrue();

    /**
     * Find templates by approval status
     */
    List&lt;DocumentTemplate&gt; findByApprovalStatus(TemplateApprovalStatus approvalStatus);

    /**
     * Find templates pending approval
     */
    default List&lt;DocumentTemplate&gt; findPendingApprovalTemplates() {
<span class="nc" id="L115">        return findByApprovalStatus(TemplateApprovalStatus.PENDING_APPROVAL);</span>
    }

    /**
     * Find recently used templates
     */
    List&lt;DocumentTemplate&gt; findByLastUsedDateAfterAndIsActiveTrueOrderByLastUsedDateDesc(LocalDateTime cutoffDate);

    /**
     * Find popular templates (by usage count)
     */
    List&lt;DocumentTemplate&gt; findByIsActiveTrueAndUsageCountGreaterThanOrderByUsageCountDesc(Integer minUsageCount);

    /**
     * Find templates by category and type
     */
    List&lt;DocumentTemplate&gt; findByCategoryAndTemplateTypeAndIsActiveTrue(String category, TemplateType templateType);

    /**
     * Find templates by format
     */
    List&lt;DocumentTemplate&gt; findByTemplateFormatAndIsActiveTrue(String templateFormat);

    /**
     * Find templates created within date range
     */
    List&lt;DocumentTemplate&gt; findByCreatedDateBetween(LocalDateTime startDate, LocalDateTime endDate);

    /**
     * Find templates published within date range
     */
    List&lt;DocumentTemplate&gt; findByPublishedDateBetween(LocalDateTime startDate, LocalDateTime endDate);

    /**
     * Count templates by category
     */
    long countByCategoryAndIsActiveTrue(String category);

    /**
     * Count templates by owner
     */
    long countByOwnerUserIdAndIsActiveTrue(String ownerUserId);

    /**
     * Count templates by approval status
     */
    long countByApprovalStatus(TemplateApprovalStatus approvalStatus);

    /**
     * Find most recently created templates
     */
    List&lt;DocumentTemplate&gt; findTop10ByIsActiveTrueOrderByCreatedDateDesc();

    /**
     * Find most popular templates
     */
    List&lt;DocumentTemplate&gt; findTop10ByIsActiveTrueOrderByUsageCountDesc();

    /**
     * Get template statistics by category
     */
    @Query(&quot;SELECT dt.category, COUNT(dt) FROM DocumentTemplate dt WHERE dt.isActive = true GROUP BY dt.category&quot;)
    List&lt;Object[]&gt; getTemplateStatisticsByCategory();

    /**
     * Get template statistics by type
     */
    @Query(&quot;SELECT dt.templateType, COUNT(dt) FROM DocumentTemplate dt WHERE dt.isActive = true GROUP BY dt.templateType&quot;)
    List&lt;Object[]&gt; getTemplateStatisticsByType();

    /**
     * Get template statistics by approval status
     */
    @Query(&quot;SELECT dt.approvalStatus, COUNT(dt) FROM DocumentTemplate dt GROUP BY dt.approvalStatus&quot;)
    List&lt;Object[]&gt; getTemplateStatisticsByApprovalStatus();

    /**
     * Find templates with no recent usage
     */
    @Query(&quot;SELECT dt FROM DocumentTemplate dt WHERE dt.isActive = true AND (dt.lastUsedDate IS NULL OR dt.lastUsedDate &lt; :cutoffDate)&quot;)
    List&lt;DocumentTemplate&gt; findUnusedTemplates(@Param(&quot;cutoffDate&quot;) LocalDateTime cutoffDate);
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>