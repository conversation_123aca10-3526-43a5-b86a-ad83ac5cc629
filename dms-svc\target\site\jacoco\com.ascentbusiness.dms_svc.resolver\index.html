<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>com.ascentbusiness.dms_svc.resolver</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.source.html" class="el_source">Source Files</a><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <span class="el_package">com.ascentbusiness.dms_svc.resolver</span></div><h1>com.ascentbusiness.dms_svc.resolver</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">19,790 of 19,790</td><td class="ctr2">0%</td><td class="bar">1,228 of 1,228</td><td class="ctr2">0%</td><td class="ctr1">1,533</td><td class="ctr2">1,533</td><td class="ctr1">4,883</td><td class="ctr2">4,883</td><td class="ctr1">907</td><td class="ctr2">907</td><td class="ctr1">53</td><td class="ctr2">53</td></tr></tfoot><tbody><tr><td id="a5"><a href="ConversionGraphQLResolver.html" class="el_class">ConversionGraphQLResolver</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="3,628" alt="3,628"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="312" alt="312"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f0">217</td><td class="ctr2" id="g0">217</td><td class="ctr1" id="h0">950</td><td class="ctr2" id="i0">950</td><td class="ctr1" id="j1">59</td><td class="ctr2" id="k1">59</td><td class="ctr1" id="l0">1</td><td class="ctr2" id="m0">1</td></tr><tr><td id="a8"><a href="DocumentResolver.html" class="el_class">DocumentResolver</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="87" height="10" title="2,647" alt="2,647"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="79" height="10" title="207" alt="207"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f1">213</td><td class="ctr2" id="g1">213</td><td class="ctr1" id="h1">664</td><td class="ctr2" id="i1">664</td><td class="ctr1" id="j0">108</td><td class="ctr2" id="k0">108</td><td class="ctr1" id="l1">1</td><td class="ctr2" id="m1">1</td></tr><tr><td id="a6"><a href="DiagnosticsGraphQLResolver.html" class="el_class">DiagnosticsGraphQLResolver</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="79" height="10" title="2,403" alt="2,403"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="62" height="10" title="163" alt="163"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f3">124</td><td class="ctr2" id="g3">124</td><td class="ctr1" id="h2">663</td><td class="ctr2" id="i2">663</td><td class="ctr1" id="j3">38</td><td class="ctr2" id="k3">38</td><td class="ctr1" id="l2">1</td><td class="ctr2" id="m2">1</td></tr><tr><td id="a12"><a href="DocumentTemplateResolver.html" class="el_class">DocumentTemplateResolver</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="55" height="10" title="1,668" alt="1,668"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="58" height="10" title="151" alt="151"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f2">133</td><td class="ctr2" id="g2">133</td><td class="ctr1" id="h3">414</td><td class="ctr2" id="i3">414</td><td class="ctr1" id="j2">55</td><td class="ctr2" id="k2">55</td><td class="ctr1" id="l3">1</td><td class="ctr2" id="m3">1</td></tr><tr><td id="a0"><a href="AuditGraphQLResolver.html" class="el_class">AuditGraphQLResolver</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="45" height="10" title="1,380" alt="1,380"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="44" height="10" title="115" alt="115"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f4">90</td><td class="ctr2" id="g4">90</td><td class="ctr1" id="h4">338</td><td class="ctr2" id="i4">338</td><td class="ctr1" id="j5">31</td><td class="ctr2" id="k5">31</td><td class="ctr1" id="l4">1</td><td class="ctr2" id="m4">1</td></tr><tr><td id="a35"><a href="TracingGraphQLResolver.html" class="el_class">TracingGraphQLResolver</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="27" height="10" title="829" alt="829"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d5"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="36" alt="36"/></td><td class="ctr2" id="e5">0%</td><td class="ctr1" id="f8">35</td><td class="ctr2" id="g8">35</td><td class="ctr1" id="h5">158</td><td class="ctr2" id="i5">158</td><td class="ctr1" id="j13">17</td><td class="ctr2" id="k13">17</td><td class="ctr1" id="l5">1</td><td class="ctr2" id="m5">1</td></tr><tr><td id="a10"><a href="DocumentRetentionResolver.html" class="el_class">DocumentRetentionResolver</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="19" height="10" title="581" alt="581"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d14"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="14" alt="14"/></td><td class="ctr2" id="e6">0%</td><td class="ctr1" id="f16">22</td><td class="ctr2" id="g16">22</td><td class="ctr1" id="h7">117</td><td class="ctr2" id="i7">117</td><td class="ctr1" id="j19">15</td><td class="ctr2" id="k19">15</td><td class="ctr1" id="l6">1</td><td class="ctr2" id="m6">1</td></tr><tr><td id="a26"><a href="StorageConfigurationResolver.html" class="el_class">StorageConfigurationResolver</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="18" height="10" title="557" alt="557"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d17"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="8" alt="8"/></td><td class="ctr2" id="e7">0%</td><td class="ctr1" id="f17">21</td><td class="ctr2" id="g17">21</td><td class="ctr1" id="h8">108</td><td class="ctr2" id="i8">108</td><td class="ctr1" id="j14">17</td><td class="ctr2" id="k14">17</td><td class="ctr1" id="l7">1</td><td class="ctr2" id="m7">1</td></tr><tr><td id="a36"><a href="WebhookEndpointResolver.html" class="el_class">WebhookEndpointResolver</a></td><td class="bar" id="b8"><img src="../jacoco-resources/redbar.gif" width="18" height="10" title="545" alt="545"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d9"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="24" alt="24"/></td><td class="ctr2" id="e8">0%</td><td class="ctr1" id="f5">42</td><td class="ctr2" id="g5">42</td><td class="ctr1" id="h6">129</td><td class="ctr2" id="i6">129</td><td class="ctr1" id="j7">30</td><td class="ctr2" id="k7">30</td><td class="ctr1" id="l8">1</td><td class="ctr2" id="m8">1</td></tr><tr><td id="a47"><a href="WorkflowInstanceResolver.html" class="el_class">WorkflowInstanceResolver</a></td><td class="bar" id="b9"><img src="../jacoco-resources/redbar.gif" width="14" height="10" title="426" alt="426"/></td><td class="ctr2" id="c9">0%</td><td class="bar" id="d10"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="18" alt="18"/></td><td class="ctr2" id="e9">0%</td><td class="ctr1" id="f6">41</td><td class="ctr2" id="g6">41</td><td class="ctr1" id="h12">91</td><td class="ctr2" id="i12">91</td><td class="ctr1" id="j4">32</td><td class="ctr2" id="k4">32</td><td class="ctr1" id="l9">1</td><td class="ctr2" id="m9">1</td></tr><tr><td id="a28"><a href="SystemEventResolver.html" class="el_class">SystemEventResolver</a></td><td class="bar" id="b10"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="421" alt="421"/></td><td class="ctr2" id="c10">0%</td><td class="bar" id="d7"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="26" alt="26"/></td><td class="ctr2" id="e10">0%</td><td class="ctr1" id="f7">41</td><td class="ctr2" id="g7">41</td><td class="ctr1" id="h10">92</td><td class="ctr2" id="i10">92</td><td class="ctr1" id="j8">28</td><td class="ctr2" id="k8">28</td><td class="ctr1" id="l10">1</td><td class="ctr2" id="m10">1</td></tr><tr><td id="a22"><a href="RetentionPolicyResolver.html" class="el_class">RetentionPolicyResolver</a></td><td class="bar" id="b11"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="416" alt="416"/></td><td class="ctr2" id="c11">0%</td><td class="bar" id="d8"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="26" alt="26"/></td><td class="ctr2" id="e11">0%</td><td class="ctr1" id="f10">27</td><td class="ctr2" id="g10">27</td><td class="ctr1" id="h11">92</td><td class="ctr2" id="i11">92</td><td class="ctr1" id="j21">14</td><td class="ctr2" id="k21">14</td><td class="ctr1" id="l11">1</td><td class="ctr2" id="m11">1</td></tr><tr><td id="a7"><a href="DocumentPermissionResolver.html" class="el_class">DocumentPermissionResolver</a></td><td class="bar" id="b12"><img src="../jacoco-resources/redbar.gif" width="12" height="10" title="389" alt="389"/></td><td class="ctr2" id="c12">0%</td><td class="bar" id="d11"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="18" alt="18"/></td><td class="ctr2" id="e12">0%</td><td class="ctr1" id="f15">23</td><td class="ctr2" id="g15">23</td><td class="ctr1" id="h9">94</td><td class="ctr2" id="i9">94</td><td class="ctr1" id="j22">14</td><td class="ctr2" id="k22">14</td><td class="ctr1" id="l12">1</td><td class="ctr2" id="m12">1</td></tr><tr><td id="a21"><a href="PdfConversionResolver.html" class="el_class">PdfConversionResolver</a></td><td class="bar" id="b13"><img src="../jacoco-resources/redbar.gif" width="12" height="10" title="385" alt="385"/></td><td class="ctr2" id="c13">0%</td><td class="bar" id="d6"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="34" alt="34"/></td><td class="ctr2" id="e13">0%</td><td class="ctr1" id="f14">24</td><td class="ctr2" id="g14">24</td><td class="ctr1" id="h13">83</td><td class="ctr2" id="i13">83</td><td class="ctr1" id="j45">7</td><td class="ctr2" id="k45">7</td><td class="ctr1" id="l13">1</td><td class="ctr2" id="m13">1</td></tr><tr><td id="a20"><a href="MarkdownConversionResolver.html" class="el_class">MarkdownConversionResolver</a></td><td class="bar" id="b14"><img src="../jacoco-resources/redbar.gif" width="11" height="10" title="359" alt="359"/></td><td class="ctr2" id="c14">0%</td><td class="bar" id="d13"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="16" alt="16"/></td><td class="ctr2" id="e14">0%</td><td class="ctr1" id="f28">12</td><td class="ctr2" id="g28">12</td><td class="ctr1" id="h15">74</td><td class="ctr2" id="i15">74</td><td class="ctr1" id="j49">4</td><td class="ctr2" id="k49">4</td><td class="ctr1" id="l14">1</td><td class="ctr2" id="m14">1</td></tr><tr><td id="a41"><a href="WordConversionResolver.html" class="el_class">WordConversionResolver</a></td><td class="bar" id="b15"><img src="../jacoco-resources/redbar.gif" width="11" height="10" title="355" alt="355"/></td><td class="ctr2" id="c15">0%</td><td class="bar" id="d12"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="18" alt="18"/></td><td class="ctr2" id="e15">0%</td><td class="ctr1" id="f25">13</td><td class="ctr2" id="g25">13</td><td class="ctr1" id="h14">75</td><td class="ctr2" id="i14">75</td><td class="ctr1" id="j50">4</td><td class="ctr2" id="k50">4</td><td class="ctr1" id="l15">1</td><td class="ctr2" id="m15">1</td></tr><tr><td id="a42"><a href="WorkflowDefinitionResolver.html" class="el_class">WorkflowDefinitionResolver</a></td><td class="bar" id="b16"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="323" alt="323"/></td><td class="ctr2" id="c16">0%</td><td class="bar" id="d16"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="12" alt="12"/></td><td class="ctr2" id="e16">0%</td><td class="ctr1" id="f11">26</td><td class="ctr2" id="g11">26</td><td class="ctr1" id="h16">72</td><td class="ctr2" id="i16">72</td><td class="ctr1" id="j11">20</td><td class="ctr2" id="k11">20</td><td class="ctr1" id="l16">1</td><td class="ctr2" id="m16">1</td></tr><tr><td id="a19"><a href="LegalHoldResolver.html" class="el_class">LegalHoldResolver</a></td><td class="bar" id="b17"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="288" alt="288"/></td><td class="ctr2" id="c17">0%</td><td class="bar" id="d18"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="8" alt="8"/></td><td class="ctr2" id="e17">0%</td><td class="ctr1" id="f26">13</td><td class="ctr2" id="g26">13</td><td class="ctr1" id="h19">54</td><td class="ctr2" id="i19">54</td><td class="ctr1" id="j27">9</td><td class="ctr2" id="k27">9</td><td class="ctr1" id="l17">1</td><td class="ctr2" id="m17">1</td></tr><tr><td id="a34"><a href="TestCaseGraphQLResolver.html" class="el_class">TestCaseGraphQLResolver</a></td><td class="bar" id="b18"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="286" alt="286"/></td><td class="ctr2" id="c18">0%</td><td class="bar" id="d20"/><td class="ctr2" id="e18">0%</td><td class="ctr1" id="f12">26</td><td class="ctr2" id="g12">26</td><td class="ctr1" id="h18">65</td><td class="ctr2" id="i18">65</td><td class="ctr1" id="j9">25</td><td class="ctr2" id="k9">25</td><td class="ctr1" id="l18">1</td><td class="ctr2" id="m18">1</td></tr><tr><td id="a11"><a href="DocumentShareResolver.html" class="el_class">DocumentShareResolver</a></td><td class="bar" id="b19"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="264" alt="264"/></td><td class="ctr2" id="c19">0%</td><td class="bar" id="d19"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="6" alt="6"/></td><td class="ctr2" id="e19">0%</td><td class="ctr1" id="f19">19</td><td class="ctr2" id="g19">19</td><td class="ctr1" id="h17">68</td><td class="ctr2" id="i17">68</td><td class="ctr1" id="j18">16</td><td class="ctr2" id="k18">16</td><td class="ctr1" id="l19">1</td><td class="ctr2" id="m19">1</td></tr><tr><td id="a23"><a href="SecurityViolationResolver.html" class="el_class">SecurityViolationResolver</a></td><td class="bar" id="b20"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="191" alt="191"/></td><td class="ctr2" id="c20">0%</td><td class="bar" id="d15"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="14" alt="14"/></td><td class="ctr2" id="e20">0%</td><td class="ctr1" id="f18">20</td><td class="ctr2" id="g18">20</td><td class="ctr1" id="h20">42</td><td class="ctr2" id="i20">42</td><td class="ctr1" id="j23">13</td><td class="ctr2" id="k23">13</td><td class="ctr1" id="l20">1</td><td class="ctr2" id="m20">1</td></tr><tr><td id="a37"><a href="WebhookEndpointResolver$WebhookEndpointInput.html" class="el_class">WebhookEndpointResolver.WebhookEndpointInput</a></td><td class="bar" id="b21"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="108" alt="108"/></td><td class="ctr2" id="c21">0%</td><td class="bar" id="d21"/><td class="ctr2" id="e21">n/a</td><td class="ctr1" id="f9">31</td><td class="ctr2" id="g9">31</td><td class="ctr1" id="h21">31</td><td class="ctr2" id="i21">31</td><td class="ctr1" id="j6">31</td><td class="ctr2" id="k6">31</td><td class="ctr1" id="l21">1</td><td class="ctr2" id="m21">1</td></tr><tr><td id="a1"><a href="AuditGraphQLResolver$AuditLogFilterInput.html" class="el_class">AuditGraphQLResolver.AuditLogFilterInput</a></td><td class="bar" id="b22"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="87" alt="87"/></td><td class="ctr2" id="c22">0%</td><td class="bar" id="d22"/><td class="ctr2" id="e22">n/a</td><td class="ctr1" id="f13">25</td><td class="ctr2" id="g13">25</td><td class="ctr1" id="h22">25</td><td class="ctr2" id="i22">25</td><td class="ctr1" id="j10">25</td><td class="ctr2" id="k10">25</td><td class="ctr1" id="l22">1</td><td class="ctr2" id="m22">1</td></tr><tr><td id="a51"><a href="WorkflowInstanceResolver$WorkflowInstancePage$WorkflowInstancePageBuilder.html" class="el_class">WorkflowInstanceResolver.WorkflowInstancePage.WorkflowInstancePageBuilder</a></td><td class="bar" id="b23"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="72" alt="72"/></td><td class="ctr2" id="c23">0%</td><td class="bar" id="d23"/><td class="ctr2" id="e23">n/a</td><td class="ctr1" id="f31">9</td><td class="ctr2" id="g31">9</td><td class="ctr1" id="h23">24</td><td class="ctr2" id="i23">24</td><td class="ctr1" id="j28">9</td><td class="ctr2" id="k28">9</td><td class="ctr1" id="l23">1</td><td class="ctr2" id="m23">1</td></tr><tr><td id="a39"><a href="WebhookEndpointResolver$WebhookEndpointPage$WebhookEndpointPageBuilder.html" class="el_class">WebhookEndpointResolver.WebhookEndpointPage.WebhookEndpointPageBuilder</a></td><td class="bar" id="b24"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="72" alt="72"/></td><td class="ctr2" id="c24">0%</td><td class="bar" id="d24"/><td class="ctr2" id="e24">n/a</td><td class="ctr1" id="f32">9</td><td class="ctr2" id="g32">9</td><td class="ctr1" id="h24">24</td><td class="ctr2" id="i24">24</td><td class="ctr1" id="j29">9</td><td class="ctr2" id="k29">9</td><td class="ctr1" id="l24">1</td><td class="ctr2" id="m24">1</td></tr><tr><td id="a45"><a href="WorkflowDefinitionResolver$WorkflowDefinitionPage$WorkflowDefinitionPageBuilder.html" class="el_class">WorkflowDefinitionResolver.WorkflowDefinitionPage.WorkflowDefinitionPageBuilder</a></td><td class="bar" id="b25"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="72" alt="72"/></td><td class="ctr2" id="c25">0%</td><td class="bar" id="d25"/><td class="ctr2" id="e25">n/a</td><td class="ctr1" id="f33">9</td><td class="ctr2" id="g33">9</td><td class="ctr1" id="h25">24</td><td class="ctr2" id="i25">24</td><td class="ctr1" id="j30">9</td><td class="ctr2" id="k30">9</td><td class="ctr1" id="l25">1</td><td class="ctr2" id="m25">1</td></tr><tr><td id="a33"><a href="SystemEventResolver$SystemEventPage$SystemEventPageBuilder.html" class="el_class">SystemEventResolver.SystemEventPage.SystemEventPageBuilder</a></td><td class="bar" id="b26"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="72" alt="72"/></td><td class="ctr2" id="c26">0%</td><td class="bar" id="d26"/><td class="ctr2" id="e26">n/a</td><td class="ctr1" id="f34">9</td><td class="ctr2" id="g34">9</td><td class="ctr1" id="h26">24</td><td class="ctr2" id="i26">24</td><td class="ctr1" id="j31">9</td><td class="ctr2" id="k31">9</td><td class="ctr1" id="l26">1</td><td class="ctr2" id="m26">1</td></tr><tr><td id="a16"><a href="DocumentTemplateResolver$DocumentTemplatePage$DocumentTemplatePageBuilder.html" class="el_class">DocumentTemplateResolver.DocumentTemplatePage.DocumentTemplatePageBuilder</a></td><td class="bar" id="b27"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="72" alt="72"/></td><td class="ctr2" id="c27">0%</td><td class="bar" id="d27"/><td class="ctr2" id="e27">n/a</td><td class="ctr1" id="f35">9</td><td class="ctr2" id="g35">9</td><td class="ctr1" id="h27">24</td><td class="ctr2" id="i27">24</td><td class="ctr1" id="j32">9</td><td class="ctr2" id="k32">9</td><td class="ctr1" id="l27">1</td><td class="ctr2" id="m27">1</td></tr><tr><td id="a3"><a href="AuditGraphQLResolver$ComplianceAuditFilterInput.html" class="el_class">AuditGraphQLResolver.ComplianceAuditFilterInput</a></td><td class="bar" id="b28"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="66" alt="66"/></td><td class="ctr2" id="c28">0%</td><td class="bar" id="d28"/><td class="ctr2" id="e28">n/a</td><td class="ctr1" id="f20">19</td><td class="ctr2" id="g20">19</td><td class="ctr1" id="h28">19</td><td class="ctr2" id="i28">19</td><td class="ctr1" id="j12">19</td><td class="ctr2" id="k12">19</td><td class="ctr1" id="l28">1</td><td class="ctr2" id="m28">1</td></tr><tr><td id="a27"><a href="StorageConfigurationResolver$StorageConfigurationInput.html" class="el_class">StorageConfigurationResolver.StorageConfigurationInput</a></td><td class="bar" id="b29"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="59" alt="59"/></td><td class="ctr2" id="c29">0%</td><td class="bar" id="d29"/><td class="ctr2" id="e29">n/a</td><td class="ctr1" id="f21">17</td><td class="ctr2" id="g21">17</td><td class="ctr1" id="h30">17</td><td class="ctr2" id="i30">17</td><td class="ctr1" id="j15">17</td><td class="ctr2" id="k15">17</td><td class="ctr1" id="l29">1</td><td class="ctr2" id="m29">1</td></tr><tr><td id="a14"><a href="DocumentTemplateResolver$DocumentTemplateInput.html" class="el_class">DocumentTemplateResolver.DocumentTemplateInput</a></td><td class="bar" id="b30"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="59" alt="59"/></td><td class="ctr2" id="c30">0%</td><td class="bar" id="d30"/><td class="ctr2" id="e30">n/a</td><td class="ctr1" id="f22">17</td><td class="ctr2" id="g22">17</td><td class="ctr1" id="h31">17</td><td class="ctr2" id="i31">17</td><td class="ctr1" id="j16">17</td><td class="ctr2" id="k16">17</td><td class="ctr1" id="l30">1</td><td class="ctr2" id="m30">1</td></tr><tr><td id="a43"><a href="WorkflowDefinitionResolver$WorkflowDefinitionInput.html" class="el_class">WorkflowDefinitionResolver.WorkflowDefinitionInput</a></td><td class="bar" id="b31"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="59" alt="59"/></td><td class="ctr2" id="c31">0%</td><td class="bar" id="d31"/><td class="ctr2" id="e31">n/a</td><td class="ctr1" id="f23">17</td><td class="ctr2" id="g23">17</td><td class="ctr1" id="h32">17</td><td class="ctr2" id="i32">17</td><td class="ctr1" id="j17">17</td><td class="ctr2" id="k17">17</td><td class="ctr1" id="l31">1</td><td class="ctr2" id="m31">1</td></tr><tr><td id="a25"><a href="SecurityViolationResolver$SecurityViolationStats$SecurityViolationStatsBuilder.html" class="el_class">SecurityViolationResolver.SecurityViolationStats.SecurityViolationStatsBuilder</a></td><td class="bar" id="b32"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="54" alt="54"/></td><td class="ctr2" id="c32">0%</td><td class="bar" id="d32"/><td class="ctr2" id="e32">n/a</td><td class="ctr1" id="f48">7</td><td class="ctr2" id="g48">7</td><td class="ctr1" id="h29">18</td><td class="ctr2" id="i29">18</td><td class="ctr1" id="j46">7</td><td class="ctr2" id="k46">7</td><td class="ctr1" id="l32">1</td><td class="ctr2" id="m32">1</td></tr><tr><td id="a31"><a href="SystemEventResolver$SystemEventInput.html" class="el_class">SystemEventResolver.SystemEventInput</a></td><td class="bar" id="b33"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="52" alt="52"/></td><td class="ctr2" id="c33">0%</td><td class="bar" id="d33"/><td class="ctr2" id="e33">n/a</td><td class="ctr1" id="f24">15</td><td class="ctr2" id="g24">15</td><td class="ctr1" id="h33">15</td><td class="ctr2" id="i33">15</td><td class="ctr1" id="j20">15</td><td class="ctr2" id="k20">15</td><td class="ctr1" id="l33">1</td><td class="ctr2" id="m33">1</td></tr><tr><td id="a2"><a href="AuditGraphQLResolver$AuditPaginationInput.html" class="el_class">AuditGraphQLResolver.AuditPaginationInput</a></td><td class="bar" id="b34"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="45" alt="45"/></td><td class="ctr2" id="c34">0%</td><td class="bar" id="d34"/><td class="ctr2" id="e34">n/a</td><td class="ctr1" id="f36">9</td><td class="ctr2" id="g36">9</td><td class="ctr1" id="h34">13</td><td class="ctr2" id="i34">13</td><td class="ctr1" id="j33">9</td><td class="ctr2" id="k33">9</td><td class="ctr1" id="l34">1</td><td class="ctr2" id="m34">1</td></tr><tr><td id="a29"><a href="SystemEventResolver$EventFilterInput.html" class="el_class">SystemEventResolver.EventFilterInput</a></td><td class="bar" id="b35"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="45" alt="45"/></td><td class="ctr2" id="c35">0%</td><td class="bar" id="d35"/><td class="ctr2" id="e35">n/a</td><td class="ctr1" id="f27">13</td><td class="ctr2" id="g27">13</td><td class="ctr1" id="h35">13</td><td class="ctr2" id="i35">13</td><td class="ctr1" id="j24">13</td><td class="ctr2" id="k24">13</td><td class="ctr1" id="l35">1</td><td class="ctr2" id="m35">1</td></tr><tr><td id="a49"><a href="WorkflowInstanceResolver$StartWorkflowInput.html" class="el_class">WorkflowInstanceResolver.StartWorkflowInput</a></td><td class="bar" id="b36"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="38" alt="38"/></td><td class="ctr2" id="c36">0%</td><td class="bar" id="d36"/><td class="ctr2" id="e36">n/a</td><td class="ctr1" id="f29">11</td><td class="ctr2" id="g29">11</td><td class="ctr1" id="h36">11</td><td class="ctr2" id="i36">11</td><td class="ctr1" id="j25">11</td><td class="ctr2" id="k25">11</td><td class="ctr1" id="l36">1</td><td class="ctr2" id="m36">1</td></tr><tr><td id="a13"><a href="DocumentTemplateResolver$CreateDocumentFromTemplateInput.html" class="el_class">DocumentTemplateResolver.CreateDocumentFromTemplateInput</a></td><td class="bar" id="b37"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="38" alt="38"/></td><td class="ctr2" id="c37">0%</td><td class="bar" id="d37"/><td class="ctr2" id="e37">n/a</td><td class="ctr1" id="f30">11</td><td class="ctr2" id="g30">11</td><td class="ctr1" id="h37">11</td><td class="ctr2" id="i37">11</td><td class="ctr1" id="j26">11</td><td class="ctr2" id="k26">11</td><td class="ctr1" id="l37">1</td><td class="ctr2" id="m37">1</td></tr><tr><td id="a52"><a href="WorkflowInstanceResolver$WorkflowPaginationInput.html" class="el_class">WorkflowInstanceResolver.WorkflowPaginationInput</a></td><td class="bar" id="b38"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="31" alt="31"/></td><td class="ctr2" id="c38">0%</td><td class="bar" id="d38"/><td class="ctr2" id="e38">n/a</td><td class="ctr1" id="f37">9</td><td class="ctr2" id="g37">9</td><td class="ctr1" id="h38">9</td><td class="ctr2" id="i38">9</td><td class="ctr1" id="j34">9</td><td class="ctr2" id="k34">9</td><td class="ctr1" id="l38">1</td><td class="ctr2" id="m38">1</td></tr><tr><td id="a40"><a href="WebhookEndpointResolver$WebhookPaginationInput.html" class="el_class">WebhookEndpointResolver.WebhookPaginationInput</a></td><td class="bar" id="b39"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="31" alt="31"/></td><td class="ctr2" id="c39">0%</td><td class="bar" id="d39"/><td class="ctr2" id="e39">n/a</td><td class="ctr1" id="f38">9</td><td class="ctr2" id="g38">9</td><td class="ctr1" id="h39">9</td><td class="ctr2" id="i39">9</td><td class="ctr1" id="j35">9</td><td class="ctr2" id="k35">9</td><td class="ctr1" id="l39">1</td><td class="ctr2" id="m39">1</td></tr><tr><td id="a46"><a href="WorkflowDefinitionResolver$WorkflowPaginationInput.html" class="el_class">WorkflowDefinitionResolver.WorkflowPaginationInput</a></td><td class="bar" id="b40"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="31" alt="31"/></td><td class="ctr2" id="c40">0%</td><td class="bar" id="d40"/><td class="ctr2" id="e40">n/a</td><td class="ctr1" id="f39">9</td><td class="ctr2" id="g39">9</td><td class="ctr1" id="h40">9</td><td class="ctr2" id="i40">9</td><td class="ctr1" id="j36">9</td><td class="ctr2" id="k36">9</td><td class="ctr1" id="l40">1</td><td class="ctr2" id="m40">1</td></tr><tr><td id="a18"><a href="DocumentTemplateResolver$TemplatePaginationInput.html" class="el_class">DocumentTemplateResolver.TemplatePaginationInput</a></td><td class="bar" id="b41"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="31" alt="31"/></td><td class="ctr2" id="c41">0%</td><td class="bar" id="d41"/><td class="ctr2" id="e41">n/a</td><td class="ctr1" id="f40">9</td><td class="ctr2" id="g40">9</td><td class="ctr1" id="h41">9</td><td class="ctr2" id="i41">9</td><td class="ctr1" id="j37">9</td><td class="ctr2" id="k37">9</td><td class="ctr1" id="l41">1</td><td class="ctr2" id="m41">1</td></tr><tr><td id="a30"><a href="SystemEventResolver$EventPaginationInput.html" class="el_class">SystemEventResolver.EventPaginationInput</a></td><td class="bar" id="b42"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="31" alt="31"/></td><td class="ctr2" id="c42">0%</td><td class="bar" id="d42"/><td class="ctr2" id="e42">n/a</td><td class="ctr1" id="f41">9</td><td class="ctr2" id="g41">9</td><td class="ctr1" id="h42">9</td><td class="ctr2" id="i42">9</td><td class="ctr1" id="j38">9</td><td class="ctr2" id="k38">9</td><td class="ctr1" id="l42">1</td><td class="ctr2" id="m42">1</td></tr><tr><td id="a48"><a href="WorkflowInstanceResolver$CompleteTaskInput.html" class="el_class">WorkflowInstanceResolver.CompleteTaskInput</a></td><td class="bar" id="b43"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="31" alt="31"/></td><td class="ctr2" id="c43">0%</td><td class="bar" id="d43"/><td class="ctr2" id="e43">n/a</td><td class="ctr1" id="f42">9</td><td class="ctr2" id="g42">9</td><td class="ctr1" id="h43">9</td><td class="ctr2" id="i43">9</td><td class="ctr1" id="j39">9</td><td class="ctr2" id="k39">9</td><td class="ctr1" id="l43">1</td><td class="ctr2" id="m43">1</td></tr><tr><td id="a44"><a href="WorkflowDefinitionResolver$WorkflowDefinitionPage.html" class="el_class">WorkflowDefinitionResolver.WorkflowDefinitionPage</a></td><td class="bar" id="b44"/><td class="ctr2" id="c44">0%</td><td class="bar" id="d44"/><td class="ctr2" id="e44">n/a</td><td class="ctr1" id="f43">9</td><td class="ctr2" id="g43">9</td><td class="ctr1" id="h44">9</td><td class="ctr2" id="i44">9</td><td class="ctr1" id="j40">9</td><td class="ctr2" id="k40">9</td><td class="ctr1" id="l44">1</td><td class="ctr2" id="m44">1</td></tr><tr><td id="a50"><a href="WorkflowInstanceResolver$WorkflowInstancePage.html" class="el_class">WorkflowInstanceResolver.WorkflowInstancePage</a></td><td class="bar" id="b45"/><td class="ctr2" id="c45">0%</td><td class="bar" id="d45"/><td class="ctr2" id="e45">n/a</td><td class="ctr1" id="f44">9</td><td class="ctr2" id="g44">9</td><td class="ctr1" id="h45">9</td><td class="ctr2" id="i45">9</td><td class="ctr1" id="j41">9</td><td class="ctr2" id="k41">9</td><td class="ctr1" id="l45">1</td><td class="ctr2" id="m45">1</td></tr><tr><td id="a38"><a href="WebhookEndpointResolver$WebhookEndpointPage.html" class="el_class">WebhookEndpointResolver.WebhookEndpointPage</a></td><td class="bar" id="b46"/><td class="ctr2" id="c46">0%</td><td class="bar" id="d46"/><td class="ctr2" id="e46">n/a</td><td class="ctr1" id="f45">9</td><td class="ctr2" id="g45">9</td><td class="ctr1" id="h46">9</td><td class="ctr2" id="i46">9</td><td class="ctr1" id="j42">9</td><td class="ctr2" id="k42">9</td><td class="ctr1" id="l46">1</td><td class="ctr2" id="m46">1</td></tr><tr><td id="a15"><a href="DocumentTemplateResolver$DocumentTemplatePage.html" class="el_class">DocumentTemplateResolver.DocumentTemplatePage</a></td><td class="bar" id="b47"/><td class="ctr2" id="c47">0%</td><td class="bar" id="d47"/><td class="ctr2" id="e47">n/a</td><td class="ctr1" id="f46">9</td><td class="ctr2" id="g46">9</td><td class="ctr1" id="h47">9</td><td class="ctr2" id="i47">9</td><td class="ctr1" id="j43">9</td><td class="ctr2" id="k43">9</td><td class="ctr1" id="l47">1</td><td class="ctr2" id="m47">1</td></tr><tr><td id="a32"><a href="SystemEventResolver$SystemEventPage.html" class="el_class">SystemEventResolver.SystemEventPage</a></td><td class="bar" id="b48"/><td class="ctr2" id="c48">0%</td><td class="bar" id="d48"/><td class="ctr2" id="e48">n/a</td><td class="ctr1" id="f47">9</td><td class="ctr2" id="g47">9</td><td class="ctr1" id="h48">9</td><td class="ctr2" id="i48">9</td><td class="ctr1" id="j44">9</td><td class="ctr2" id="k44">9</td><td class="ctr1" id="l48">1</td><td class="ctr2" id="m48">1</td></tr><tr><td id="a24"><a href="SecurityViolationResolver$SecurityViolationStats.html" class="el_class">SecurityViolationResolver.SecurityViolationStats</a></td><td class="bar" id="b49"/><td class="ctr2" id="c49">0%</td><td class="bar" id="d49"/><td class="ctr2" id="e49">n/a</td><td class="ctr1" id="f49">7</td><td class="ctr2" id="g49">7</td><td class="ctr1" id="h49">7</td><td class="ctr2" id="i49">7</td><td class="ctr1" id="j47">7</td><td class="ctr2" id="k47">7</td><td class="ctr1" id="l49">1</td><td class="ctr2" id="m49">1</td></tr><tr><td id="a17"><a href="DocumentTemplateResolver$FieldValueInput.html" class="el_class">DocumentTemplateResolver.FieldValueInput</a></td><td class="bar" id="b50"/><td class="ctr2" id="c50">0%</td><td class="bar" id="d50"/><td class="ctr2" id="e50">n/a</td><td class="ctr1" id="f50">5</td><td class="ctr2" id="g50">5</td><td class="ctr1" id="h50">5</td><td class="ctr2" id="i50">5</td><td class="ctr1" id="j48">5</td><td class="ctr2" id="k48">5</td><td class="ctr1" id="l50">1</td><td class="ctr2" id="m50">1</td></tr><tr><td id="a4"><a href="AuthResolver.html" class="el_class">AuthResolver</a></td><td class="bar" id="b51"/><td class="ctr2" id="c51">0%</td><td class="bar" id="d51"/><td class="ctr2" id="e51">n/a</td><td class="ctr1" id="f51">2</td><td class="ctr2" id="g51">2</td><td class="ctr1" id="h51">2</td><td class="ctr2" id="i51">2</td><td class="ctr1" id="j51">2</td><td class="ctr2" id="k51">2</td><td class="ctr1" id="l51">1</td><td class="ctr2" id="m51">1</td></tr><tr><td id="a9"><a href="DocumentResolver$1.html" class="el_class">DocumentResolver.new TypeReference() {...}</a></td><td class="bar" id="b52"/><td class="ctr2" id="c52">0%</td><td class="bar" id="d52"/><td class="ctr2" id="e52">n/a</td><td class="ctr1" id="f52">1</td><td class="ctr2" id="g52">1</td><td class="ctr1" id="h52">1</td><td class="ctr2" id="i52">1</td><td class="ctr1" id="j52">1</td><td class="ctr2" id="k52">1</td><td class="ctr1" id="l52">1</td><td class="ctr2" id="m52">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>