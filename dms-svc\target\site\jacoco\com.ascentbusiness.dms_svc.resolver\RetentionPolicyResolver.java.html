<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>RetentionPolicyResolver.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.resolver</a> &gt; <span class="el_source">RetentionPolicyResolver.java</span></div><h1>RetentionPolicyResolver.java</h1><pre class="source lang-java linenums">package com.ascentbusiness.dms_svc.resolver;

import com.ascentbusiness.dms_svc.dto.RetentionPolicyInput;
import com.ascentbusiness.dms_svc.entity.Document;
import com.ascentbusiness.dms_svc.entity.RetentionPolicy;
import com.ascentbusiness.dms_svc.entity.RetentionPolicyAssignment;
import com.ascentbusiness.dms_svc.repository.DocumentRepository;
import com.ascentbusiness.dms_svc.repository.RetentionPolicyAssignmentRepository;
import com.ascentbusiness.dms_svc.service.RetentionPolicyService;
import com.ascentbusiness.dms_svc.util.CorrelationIdUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.graphql.data.method.annotation.Argument;
import org.springframework.graphql.data.method.annotation.MutationMapping;
import org.springframework.graphql.data.method.annotation.QueryMapping;
import org.springframework.graphql.data.method.annotation.SchemaMapping;
import org.springframework.stereotype.Controller;

import java.util.List;
import java.util.Map;

/**
 * GraphQL resolver for retention policy operations
 */
@Controller
<span class="nc" id="L31">public class RetentionPolicyResolver {</span>
    
<span class="nc" id="L33">    private static final Logger logger = LoggerFactory.getLogger(RetentionPolicyResolver.class);</span>
    
    @Autowired
    private RetentionPolicyService retentionPolicyService;
    
    @Autowired
    private RetentionPolicyAssignmentRepository assignmentRepository;
    
    @Autowired
    private DocumentRepository documentRepository;
    
    // Query resolvers
    
    @QueryMapping
    public RetentionPolicy getRetentionPolicy(@Argument Long id) {
<span class="nc" id="L48">        String correlationId = CorrelationIdUtil.getCurrentCorrelationId();</span>
<span class="nc" id="L49">        logger.info(&quot;GraphQL query: getRetentionPolicy(id: {}) [{}]&quot;, id, correlationId);</span>
        
<span class="nc" id="L51">        return retentionPolicyService.getRetentionPolicy(id);</span>
    }
    
    @QueryMapping
    public Map&lt;String, Object&gt; getAllRetentionPolicies(@Argument Map&lt;String, Object&gt; pagination) {
<span class="nc" id="L56">        String correlationId = CorrelationIdUtil.getCurrentCorrelationId();</span>
<span class="nc" id="L57">        logger.info(&quot;GraphQL query: getAllRetentionPolicies [{}]&quot;, correlationId);</span>
        
<span class="nc" id="L59">        Pageable pageable = createPageable(pagination);</span>
<span class="nc" id="L60">        Page&lt;RetentionPolicy&gt; policyPage = retentionPolicyService.getRetentionPolicies(pageable);</span>
        
<span class="nc" id="L62">        return Map.of(</span>
<span class="nc" id="L63">            &quot;content&quot;, policyPage.getContent(),</span>
<span class="nc" id="L64">            &quot;totalElements&quot;, policyPage.getTotalElements(),</span>
<span class="nc" id="L65">            &quot;totalPages&quot;, policyPage.getTotalPages(),</span>
<span class="nc" id="L66">            &quot;size&quot;, policyPage.getSize(),</span>
<span class="nc" id="L67">            &quot;number&quot;, policyPage.getNumber(),</span>
<span class="nc" id="L68">            &quot;first&quot;, policyPage.isFirst(),</span>
<span class="nc" id="L69">            &quot;last&quot;, policyPage.isLast()</span>
        );
    }
    
    @QueryMapping
    public List&lt;RetentionPolicy&gt; getActiveRetentionPolicies() {
<span class="nc" id="L75">        String correlationId = CorrelationIdUtil.getCurrentCorrelationId();</span>
<span class="nc" id="L76">        logger.info(&quot;GraphQL query: getActiveRetentionPolicies [{}]&quot;, correlationId);</span>
        
<span class="nc" id="L78">        return retentionPolicyService.getAllActiveRetentionPolicies();</span>
    }
    
    @QueryMapping
    public Map&lt;String, Object&gt; getDocumentsByRetentionPolicy(@Argument Long policyId, @Argument Map&lt;String, Object&gt; pagination) {
<span class="nc" id="L83">        String correlationId = CorrelationIdUtil.getCurrentCorrelationId();</span>
<span class="nc" id="L84">        logger.info(&quot;GraphQL query: getDocumentsByRetentionPolicy(policyId: {}) [{}]&quot;, policyId, correlationId);</span>
        
<span class="nc" id="L86">        Pageable pageable = createPageable(pagination);</span>
<span class="nc" id="L87">        Page&lt;Document&gt; documentPage = documentRepository.findByRetentionPolicyId(policyId, pageable);</span>
        
<span class="nc" id="L89">        return Map.of(</span>
<span class="nc" id="L90">            &quot;content&quot;, documentPage.getContent(),</span>
<span class="nc" id="L91">            &quot;totalElements&quot;, documentPage.getTotalElements(),</span>
<span class="nc" id="L92">            &quot;totalPages&quot;, documentPage.getTotalPages(),</span>
<span class="nc" id="L93">            &quot;size&quot;, documentPage.getSize(),</span>
<span class="nc" id="L94">            &quot;number&quot;, documentPage.getNumber(),</span>
<span class="nc" id="L95">            &quot;first&quot;, documentPage.isFirst(),</span>
<span class="nc" id="L96">            &quot;last&quot;, documentPage.isLast()</span>
        );
    }
    
    // Mutation resolvers
    
    @MutationMapping
    public RetentionPolicy createRetentionPolicy(@Argument RetentionPolicyInput input) {
<span class="nc" id="L104">        String correlationId = CorrelationIdUtil.getCurrentCorrelationId();</span>
<span class="nc" id="L105">        logger.info(&quot;GraphQL mutation: createRetentionPolicy(name: {}) [{}]&quot;, input.getName(), correlationId);</span>
        
<span class="nc" id="L107">        RetentionPolicy policy = convertInputToEntity(input);</span>
<span class="nc" id="L108">        RetentionPolicy savedPolicy = retentionPolicyService.createRetentionPolicy(policy);</span>
        
        // Create assignments if provided
<span class="nc bnc" id="L111" title="All 4 branches missed.">        if (input.getAssignments() != null &amp;&amp; !input.getAssignments().isEmpty()) {</span>
<span class="nc" id="L112">            createAssignments(savedPolicy, input.getAssignments());</span>
        }
        
<span class="nc" id="L115">        return savedPolicy;</span>
    }
    
    @MutationMapping
    public RetentionPolicy updateRetentionPolicy(@Argument Long id, @Argument RetentionPolicyInput input) {
<span class="nc" id="L120">        String correlationId = CorrelationIdUtil.getCurrentCorrelationId();</span>
<span class="nc" id="L121">        logger.info(&quot;GraphQL mutation: updateRetentionPolicy(id: {}, name: {}) [{}]&quot;, id, input.getName(), correlationId);</span>
        
<span class="nc" id="L123">        RetentionPolicy policy = convertInputToEntity(input);</span>
<span class="nc" id="L124">        RetentionPolicy updatedPolicy = retentionPolicyService.updateRetentionPolicy(id, policy);</span>
        
        // Update assignments if provided
<span class="nc bnc" id="L127" title="All 2 branches missed.">        if (input.getAssignments() != null) {</span>
            // Deactivate existing assignments
<span class="nc" id="L129">            assignmentRepository.deactivateByRetentionPolicyId(id);</span>
            
            // Create new assignments
<span class="nc bnc" id="L132" title="All 2 branches missed.">            if (!input.getAssignments().isEmpty()) {</span>
<span class="nc" id="L133">                createAssignments(updatedPolicy, input.getAssignments());</span>
            }
        }
        
<span class="nc" id="L137">        return updatedPolicy;</span>
    }
    
    @MutationMapping
    public Boolean deleteRetentionPolicy(@Argument Long id) {
<span class="nc" id="L142">        String correlationId = CorrelationIdUtil.getCurrentCorrelationId();</span>
<span class="nc" id="L143">        logger.info(&quot;GraphQL mutation: deleteRetentionPolicy(id: {}) [{}]&quot;, id, correlationId);</span>
        
<span class="nc" id="L145">        retentionPolicyService.deleteRetentionPolicy(id);</span>
<span class="nc" id="L146">        return true;</span>
    }
    
    // Schema mappings for nested fields
    
    @SchemaMapping(typeName = &quot;RetentionPolicy&quot;, field = &quot;assignments&quot;)
    public List&lt;RetentionPolicyAssignment&gt; getRetentionPolicyAssignments(RetentionPolicy retentionPolicy) {
<span class="nc" id="L153">        return assignmentRepository.findByRetentionPolicyId(retentionPolicy.getId());</span>
    }
    
    @SchemaMapping(typeName = &quot;RetentionPolicy&quot;, field = &quot;documentCount&quot;)
    public Long getRetentionPolicyDocumentCount(RetentionPolicy retentionPolicy) {
<span class="nc" id="L158">        return documentRepository.countByRetentionPolicyId(retentionPolicy.getId());</span>
    }
    
    // Helper methods
    
    private RetentionPolicy convertInputToEntity(RetentionPolicyInput input) {
<span class="nc" id="L164">        return RetentionPolicy.builder()</span>
<span class="nc" id="L165">                .name(input.getName())</span>
<span class="nc" id="L166">                .description(input.getDescription())</span>
<span class="nc" id="L167">                .scope(input.getScope())</span>
<span class="nc" id="L168">                .retentionPeriod(input.getRetentionPeriod())</span>
<span class="nc" id="L169">                .retentionPeriodUnit(input.getRetentionPeriodUnit())</span>
<span class="nc" id="L170">                .dispositionAction(input.getDispositionAction())</span>
<span class="nc bnc" id="L171" title="All 2 branches missed.">                .isActive(input.getIsActive() != null ? input.getIsActive() : true)</span>
<span class="nc bnc" id="L172" title="All 2 branches missed.">                .allowLegalHold(input.getAllowLegalHold() != null ? input.getAllowLegalHold() : true)</span>
<span class="nc bnc" id="L173" title="All 2 branches missed.">                .autoApply(input.getAutoApply() != null ? input.getAutoApply() : false)</span>
<span class="nc bnc" id="L174" title="All 2 branches missed.">                .priority(input.getPriority() != null ? input.getPriority() : 0)</span>
<span class="nc" id="L175">                .triggerEvent(input.getTriggerEvent())</span>
<span class="nc" id="L176">                .businessJustification(input.getBusinessJustification())</span>
<span class="nc" id="L177">                .legalBasis(input.getLegalBasis())</span>
<span class="nc" id="L178">                .reviewFrequencyMonths(input.getReviewFrequencyMonths())</span>
<span class="nc" id="L179">                .notificationBeforeDays(input.getNotificationBeforeDays())</span>
<span class="nc" id="L180">                .build();</span>
    }
    
    private void createAssignments(RetentionPolicy policy, List&lt;com.ascentbusiness.dms_svc.dto.RetentionPolicyAssignmentInput&gt; assignmentInputs) {
<span class="nc bnc" id="L184" title="All 2 branches missed.">        for (com.ascentbusiness.dms_svc.dto.RetentionPolicyAssignmentInput assignmentInput : assignmentInputs) {</span>
<span class="nc" id="L185">            RetentionPolicyAssignment assignment = RetentionPolicyAssignment.builder()</span>
<span class="nc" id="L186">                    .retentionPolicy(policy)</span>
<span class="nc" id="L187">                    .assignmentType(assignmentInput.getAssignmentType())</span>
<span class="nc" id="L188">                    .assignmentValue(assignmentInput.getAssignmentValue())</span>
<span class="nc bnc" id="L189" title="All 2 branches missed.">                    .isActive(assignmentInput.getIsActive() != null ? assignmentInput.getIsActive() : true)</span>
<span class="nc bnc" id="L190" title="All 2 branches missed.">                    .priority(assignmentInput.getPriority() != null ? assignmentInput.getPriority() : 0)</span>
<span class="nc" id="L191">                    .conditions(assignmentInput.getConditions())</span>
<span class="nc" id="L192">                    .description(assignmentInput.getDescription())</span>
<span class="nc" id="L193">                    .build();</span>
            
<span class="nc" id="L195">            assignmentRepository.save(assignment);</span>
<span class="nc" id="L196">        }</span>
<span class="nc" id="L197">    }</span>
    
    private Pageable createPageable(Map&lt;String, Object&gt; pagination) {
<span class="nc bnc" id="L200" title="All 2 branches missed.">        if (pagination == null) {</span>
<span class="nc" id="L201">            return PageRequest.of(0, 10, Sort.by(Sort.Direction.DESC, &quot;createdDate&quot;));</span>
        }
        
<span class="nc" id="L204">        int page = (Integer) pagination.getOrDefault(&quot;page&quot;, 0);</span>
<span class="nc" id="L205">        int size = (Integer) pagination.getOrDefault(&quot;size&quot;, 10);</span>
<span class="nc" id="L206">        String sortBy = (String) pagination.getOrDefault(&quot;sortBy&quot;, &quot;createdDate&quot;);</span>
<span class="nc" id="L207">        String sortDirection = (String) pagination.getOrDefault(&quot;sortDirection&quot;, &quot;DESC&quot;);</span>
        
<span class="nc bnc" id="L209" title="All 2 branches missed.">        Sort.Direction direction = &quot;ASC&quot;.equalsIgnoreCase(sortDirection) ? Sort.Direction.ASC : Sort.Direction.DESC;</span>
        
<span class="nc" id="L211">        return PageRequest.of(page, size, Sort.by(direction, sortBy));</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>