<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>ChunkedUploadSessionRepository.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.repository</a> &gt; <span class="el_source">ChunkedUploadSessionRepository.java</span></div><h1>ChunkedUploadSessionRepository.java</h1><pre class="source lang-java linenums">package com.ascentbusiness.dms_svc.repository;

import com.ascentbusiness.dms_svc.entity.ChunkedUploadSession;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Repository interface for ChunkedUploadSession entities.
 * 
 * &lt;p&gt;Provides data access methods for managing chunked upload sessions
 * including session tracking, cleanup operations, and progress monitoring.
 */
@Repository
public interface ChunkedUploadSessionRepository extends JpaRepository&lt;ChunkedUploadSession, Long&gt; {
    
    /**
     * Find an upload session by its unique session ID.
     * 
     * @param sessionId the session identifier
     * @return the upload session if found
     */
    Optional&lt;ChunkedUploadSession&gt; findBySessionId(String sessionId);
    
    /**
     * Find all sessions with a specific status.
     * 
     * @param status the session status
     * @return list of sessions with the specified status
     */
    List&lt;ChunkedUploadSession&gt; findByStatus(String status);
    
    /**
     * Find all sessions created by a specific user.
     * 
     * @param createdBy the user identifier
     * @param pageable pagination information
     * @return page of sessions created by the user
     */
    Page&lt;ChunkedUploadSession&gt; findByCreatedBy(String createdBy, Pageable pageable);
    
    /**
     * Find all sessions with a specific correlation ID.
     * 
     * @param correlationId the correlation identifier
     * @return list of sessions with the correlation ID
     */
    List&lt;ChunkedUploadSession&gt; findByCorrelationId(String correlationId);
    
    /**
     * Find all active sessions (not expired and not completed).
     *
     * @return list of active sessions
     */
    @Query(&quot;SELECT s FROM ChunkedUploadSession s WHERE s.status = 'ACTIVE' &quot; +
           &quot;AND s.expiresAt &gt; CURRENT_TIMESTAMP&quot;)
    List&lt;ChunkedUploadSession&gt; findActiveSessions();

    /**
     * Find active sessions for a specific user.
     *
     * @param userId the user identifier
     * @return list of active sessions for the user
     */
    @Query(&quot;SELECT s FROM ChunkedUploadSession s WHERE s.createdBy = :userId &quot; +
           &quot;AND s.status IN ('ACTIVE', 'PAUSED') AND s.expiresAt &gt; CURRENT_TIMESTAMP&quot;)
    List&lt;ChunkedUploadSession&gt; findActiveSessionsByUserId(@Param(&quot;userId&quot;) String userId);
    
    /**
     * Find all expired sessions.
     * 
     * @return list of expired sessions
     */
    @Query(&quot;SELECT s FROM ChunkedUploadSession s WHERE s.expiresAt &lt;= :currentTime &quot; +
           &quot;AND s.status != 'COMPLETED'&quot;)
    List&lt;ChunkedUploadSession&gt; findExpiredSessions(@Param(&quot;currentTime&quot;) LocalDateTime currentTime);

    /**
     * Find all expired sessions (convenience method using current time).
     * 
     * @return list of expired sessions
     */
    default List&lt;ChunkedUploadSession&gt; findExpiredSessions() {
<span class="nc" id="L92">        return findExpiredSessions(LocalDateTime.now());</span>
    }
    
    /**
     * Find sessions that have been inactive for longer than the specified duration.
     * 
     * @param inactivityThreshold the inactivity threshold
     * @return list of inactive sessions
     */
    @Query(&quot;SELECT s FROM ChunkedUploadSession s WHERE s.status = 'ACTIVE' &quot; +
           &quot;AND s.lastActivityAt &lt; :inactivityThreshold&quot;)
    List&lt;ChunkedUploadSession&gt; findInactiveSessions(@Param(&quot;inactivityThreshold&quot;) LocalDateTime inactivityThreshold);
    
    /**
     * Find sessions created within a specific time range.
     * 
     * @param startDate the start of the time range
     * @param endDate the end of the time range
     * @return list of sessions created in the time range
     */
    @Query(&quot;SELECT s FROM ChunkedUploadSession s WHERE s.createdDate BETWEEN :startDate AND :endDate&quot;)
    List&lt;ChunkedUploadSession&gt; findSessionsCreatedBetween(
            @Param(&quot;startDate&quot;) LocalDateTime startDate,
            @Param(&quot;endDate&quot;) LocalDateTime endDate);
    
    /**
     * Count sessions by status.
     * 
     * @param status the session status
     * @return count of sessions with the specified status
     */
    long countByStatus(String status);
    
    /**
     * Count active sessions for a specific user.
     * 
     * @param createdBy the user identifier
     * @return count of active sessions for the user
     */
    @Query(&quot;SELECT COUNT(s) FROM ChunkedUploadSession s WHERE s.createdBy = :createdBy &quot; +
           &quot;AND s.status = 'ACTIVE' AND s.expiresAt &gt; CURRENT_TIMESTAMP&quot;)
    long countActiveSessionsByUser(@Param(&quot;createdBy&quot;) String createdBy);
    
    /**
     * Find sessions that are ready for completion (all chunks received).
     * 
     * @return list of sessions ready for completion
     */
    @Query(&quot;SELECT s FROM ChunkedUploadSession s WHERE s.status = 'ACTIVE' &quot; +
           &quot;AND s.receivedChunks = s.totalChunks&quot;)
    List&lt;ChunkedUploadSession&gt; findSessionsReadyForCompletion();
    
    /**
     * Find sessions with partial uploads (some chunks received).
     * 
     * @return list of sessions with partial uploads
     */
    @Query(&quot;SELECT s FROM ChunkedUploadSession s WHERE s.status = 'ACTIVE' &quot; +
           &quot;AND s.receivedChunks &gt; 0 AND s.receivedChunks &lt; s.totalChunks&quot;)
    List&lt;ChunkedUploadSession&gt; findPartialUploadSessions();
    
    /**
     * Find sessions that completed within a specific time range.
     * 
     * @param startDate the start of the time range
     * @param endDate the end of the time range
     * @return list of completed sessions
     */
    @Query(&quot;SELECT s FROM ChunkedUploadSession s WHERE s.status = 'COMPLETED' &quot; +
           &quot;AND s.completedAt BETWEEN :startDate AND :endDate&quot;)
    List&lt;ChunkedUploadSession&gt; findCompletedSessionsBetween(
            @Param(&quot;startDate&quot;) LocalDateTime startDate,
            @Param(&quot;endDate&quot;) LocalDateTime endDate);
    
    /**
     * Delete expired sessions older than the specified date.
     * 
     * @param cutoffDate the cutoff date for deletion
     * @return number of deleted sessions
     */
    @Modifying
    @Query(&quot;DELETE FROM ChunkedUploadSession s WHERE s.expiresAt &lt; :cutoffDate &quot; +
           &quot;AND s.status != 'COMPLETED'&quot;)
    int deleteExpiredSessionsOlderThan(@Param(&quot;cutoffDate&quot;) LocalDateTime cutoffDate);
    
    /**
     * Delete completed sessions older than the specified date.
     * 
     * @param cutoffDate the cutoff date for deletion
     * @return number of deleted sessions
     */
    @Modifying
    @Query(&quot;DELETE FROM ChunkedUploadSession s WHERE s.status = 'COMPLETED' &quot; +
           &quot;AND s.completedAt &lt; :cutoffDate&quot;)
    int deleteCompletedSessionsOlderThan(@Param(&quot;cutoffDate&quot;) LocalDateTime cutoffDate);
    
    /**
     * Update session status and completion time.
     * 
     * @param sessionId the session identifier
     * @param status the new status
     * @param completedAt the completion timestamp
     * @return number of updated records
     */
    @Modifying
    @Query(&quot;UPDATE ChunkedUploadSession s SET s.status = :status, s.completedAt = :completedAt &quot; +
           &quot;WHERE s.sessionId = :sessionId&quot;)
    int updateSessionStatus(@Param(&quot;sessionId&quot;) String sessionId,
                           @Param(&quot;status&quot;) String status,
                           @Param(&quot;completedAt&quot;) LocalDateTime completedAt);
    
    /**
     * Update session progress and activity timestamp.
     * 
     * @param sessionId the session identifier
     * @param receivedChunks number of chunks received
     * @param receivedBytes number of bytes received
     * @param progress progress percentage
     * @return number of updated records
     */
    @Modifying
    @Query(&quot;UPDATE ChunkedUploadSession s SET s.receivedChunks = :receivedChunks, &quot; +
           &quot;s.receivedBytes = :receivedBytes, s.progress = :progress, &quot; +
           &quot;s.lastActivityAt = CURRENT_TIMESTAMP WHERE s.sessionId = :sessionId&quot;)
    int updateSessionProgress(@Param(&quot;sessionId&quot;) String sessionId,
                             @Param(&quot;receivedChunks&quot;) Integer receivedChunks,
                             @Param(&quot;receivedBytes&quot;) Long receivedBytes,
                             @Param(&quot;progress&quot;) java.math.BigDecimal progress);
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>