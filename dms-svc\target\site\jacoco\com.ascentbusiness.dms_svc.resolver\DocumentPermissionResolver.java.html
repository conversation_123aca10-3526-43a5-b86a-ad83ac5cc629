<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>DocumentPermissionResolver.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.resolver</a> &gt; <span class="el_source">DocumentPermissionResolver.java</span></div><h1>DocumentPermissionResolver.java</h1><pre class="source lang-java linenums">package com.ascentbusiness.dms_svc.resolver;

import com.ascentbusiness.dms_svc.annotation.RateLimit;
import com.ascentbusiness.dms_svc.dto.*;
import com.ascentbusiness.dms_svc.entity.DocumentPermission;
import com.ascentbusiness.dms_svc.service.DocumentPermissionService;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.graphql.data.method.annotation.Argument;
import org.springframework.graphql.data.method.annotation.MutationMapping;
import org.springframework.graphql.data.method.annotation.QueryMapping;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;

import java.util.ArrayList;
import java.util.List;

/**
 * GraphQL resolver for document permission operations
 */
@Controller
<span class="nc" id="L23">@RequiredArgsConstructor</span>
public class DocumentPermissionResolver {

<span class="nc" id="L26">    private static final Logger logger = LoggerFactory.getLogger(DocumentPermissionResolver.class);</span>

    private final DocumentPermissionService documentPermissionService;

    // Query Operations

    /**
     * Get all permissions for a document
     */
    @QueryMapping
    @PreAuthorize(&quot;hasRole('ADMIN') or @documentPermissionService.canViewPermissions(#documentId, authentication.name)&quot;)
    public List&lt;DocumentPermission&gt; getDocumentPermissions(@Argument Long documentId) {
<span class="nc" id="L38">        logger.info(&quot;Fetching permissions for document: {}&quot;, documentId);</span>
<span class="nc" id="L39">        return documentPermissionService.getDocumentPermissions(documentId);</span>
    }

    /**
     * Get permissions for a specific user
     */
    @QueryMapping
    @PreAuthorize(&quot;hasRole('ADMIN') or #userId == authentication.name&quot;)
    public List&lt;DocumentPermission&gt; getDocumentPermissionsByUser(@Argument String userId) {
<span class="nc" id="L48">        logger.info(&quot;Fetching permissions for user: {}&quot;, userId);</span>
<span class="nc" id="L49">        return documentPermissionService.getUserPermissions(userId);</span>
    }

    /**
     * Get permissions for a specific role
     */
    @QueryMapping
    @PreAuthorize(&quot;hasRole('ADMIN')&quot;)
    public List&lt;DocumentPermission&gt; getDocumentPermissionsByRole(@Argument String roleName) {
<span class="nc" id="L58">        logger.info(&quot;Fetching permissions for role: {}&quot;, roleName);</span>
<span class="nc" id="L59">        return documentPermissionService.getRolePermissions(roleName);</span>
    }

    /**
     * Get specific user permission for a document
     */
    @QueryMapping
    @PreAuthorize(&quot;hasRole('ADMIN') or #userId == authentication.name&quot;)
    public DocumentPermission getUserDocumentPermission(@Argument Long documentId, @Argument String userId) {
<span class="nc" id="L68">        logger.info(&quot;Fetching permission for document {} and user {}&quot;, documentId, userId);</span>
<span class="nc" id="L69">        List&lt;DocumentPermission&gt; permissions = documentPermissionService.getDocumentPermissions(documentId);</span>
<span class="nc" id="L70">        return permissions.stream()</span>
<span class="nc bnc" id="L71" title="All 4 branches missed.">                .filter(p -&gt; userId.equals(p.getUserId()) &amp;&amp; p.getIsActive())</span>
<span class="nc" id="L72">                .findFirst()</span>
<span class="nc" id="L73">                .orElse(null);</span>
    }

    /**
     * Get specific role permission for a document
     */
    @QueryMapping
    @PreAuthorize(&quot;hasRole('ADMIN')&quot;)
    public DocumentPermission getRoleDocumentPermission(@Argument Long documentId, @Argument String roleName) {
<span class="nc" id="L82">        logger.info(&quot;Fetching permission for document {} and role {}&quot;, documentId, roleName);</span>
<span class="nc" id="L83">        List&lt;DocumentPermission&gt; permissions = documentPermissionService.getDocumentPermissions(documentId);</span>
<span class="nc" id="L84">        return permissions.stream()</span>
<span class="nc bnc" id="L85" title="All 4 branches missed.">                .filter(p -&gt; roleName.equals(p.getRoleName()) &amp;&amp; p.getIsActive())</span>
<span class="nc" id="L86">                .findFirst()</span>
<span class="nc" id="L87">                .orElse(null);</span>
    }

    // Mutation Operations

    /**
     * Grant permission to a user or role for a document
     * Only ADMIN users or document creators can grant permissions
     */
    @MutationMapping
    @PreAuthorize(&quot;hasRole('ADMIN') or @documentPermissionService.canGrantPermissions(#input.documentId, authentication.name)&quot;)
    @RateLimit(value = 20, window = 300, type = RateLimit.RateLimitType.PERMISSION_OPERATION,
               message = &quot;Permission grant rate limit exceeded. Please wait before granting more permissions.&quot;)
    public DocumentPermissionResponse grantDocumentPermission(@Argument DocumentPermissionInput input) {
        try {
<span class="nc" id="L102">            logger.info(&quot;Granting permission for document {} to user: {} role: {}&quot;,</span>
<span class="nc" id="L103">                       input.getDocumentId(), input.getUserId(), input.getRoleName());</span>

<span class="nc" id="L105">            input.validateForCreate();</span>
            
<span class="nc" id="L107">            DocumentPermission permission = documentPermissionService.grantPermission(</span>
<span class="nc" id="L108">                input.getDocumentId(),</span>
<span class="nc" id="L109">                input.getUserId(),</span>
<span class="nc" id="L110">                input.getRoleName(),</span>
<span class="nc" id="L111">                input.getPermissionType(),</span>
<span class="nc" id="L112">                input.getExpiresAt(),</span>
<span class="nc" id="L113">                input.getNotes()</span>
            );
            
<span class="nc" id="L116">            return DocumentPermissionResponse.success(</span>
                &quot;Permission granted successfully&quot;, 
                permission
            );
            
<span class="nc" id="L121">        } catch (Exception e) {</span>
<span class="nc" id="L122">            logger.error(&quot;Error granting permission: {}&quot;, e.getMessage(), e);</span>
<span class="nc" id="L123">            return DocumentPermissionResponse.error(&quot;Failed to grant permission: &quot; + e.getMessage());</span>
        }
    }

    /**
     * Grant multiple permissions for a document
     */
    @MutationMapping
    @PreAuthorize(&quot;hasRole('ADMIN') or @documentPermissionService.canGrantPermissions(#input.documentId, authentication.name)&quot;)
    public DocumentPermissionListResponse grantBulkDocumentPermissions(@Argument BulkDocumentPermissionInput input) {
        try {
<span class="nc" id="L134">            logger.info(&quot;Granting bulk permissions for document {}&quot;, input.getDocumentId());</span>
            
<span class="nc" id="L136">            input.validate();</span>
            
<span class="nc" id="L138">            List&lt;DocumentPermission&gt; grantedPermissions = new ArrayList&lt;&gt;();</span>
<span class="nc" id="L139">            List&lt;String&gt; errors = new ArrayList&lt;&gt;();</span>
            
<span class="nc bnc" id="L141" title="All 2 branches missed.">            for (SinglePermissionInput permissionInput : input.getPermissions()) {</span>
                try {
<span class="nc" id="L143">                    DocumentPermission permission = documentPermissionService.grantPermission(</span>
<span class="nc" id="L144">                        input.getDocumentId(),</span>
<span class="nc" id="L145">                        permissionInput.getUserId(),</span>
<span class="nc" id="L146">                        permissionInput.getRoleName(),</span>
<span class="nc" id="L147">                        permissionInput.getPermissionType(),</span>
<span class="nc" id="L148">                        permissionInput.getExpiresAt(),</span>
<span class="nc" id="L149">                        permissionInput.getNotes()</span>
                    );
<span class="nc" id="L151">                    grantedPermissions.add(permission);</span>
<span class="nc" id="L152">                } catch (Exception e) {</span>
<span class="nc bnc" id="L153" title="All 2 branches missed.">                    String target = permissionInput.getUserId() != null ? </span>
<span class="nc" id="L154">                                   &quot;user &quot; + permissionInput.getUserId() : </span>
<span class="nc" id="L155">                                   &quot;role &quot; + permissionInput.getRoleName();</span>
<span class="nc" id="L156">                    errors.add(&quot;Failed to grant permission to &quot; + target + &quot;: &quot; + e.getMessage());</span>
<span class="nc" id="L157">                }</span>
<span class="nc" id="L158">            }</span>
            
<span class="nc bnc" id="L160" title="All 2 branches missed.">            if (errors.isEmpty()) {</span>
<span class="nc" id="L161">                return DocumentPermissionListResponse.success(</span>
                    &quot;All permissions granted successfully&quot;, 
                    grantedPermissions
                );
<span class="nc bnc" id="L165" title="All 2 branches missed.">            } else if (grantedPermissions.isEmpty()) {</span>
<span class="nc" id="L166">                return DocumentPermissionListResponse.error(</span>
<span class="nc" id="L167">                    &quot;Failed to grant any permissions: &quot; + String.join(&quot;; &quot;, errors)</span>
                );
            } else {
<span class="nc" id="L170">                return DocumentPermissionListResponse.success(</span>
<span class="nc" id="L171">                    String.format(&quot;Granted %d permissions with %d errors: %s&quot;, </span>
<span class="nc" id="L172">                                 grantedPermissions.size(), errors.size(), String.join(&quot;; &quot;, errors)),</span>
                    grantedPermissions
                );
            }
            
<span class="nc" id="L177">        } catch (Exception e) {</span>
<span class="nc" id="L178">            logger.error(&quot;Error granting bulk permissions: {}&quot;, e.getMessage(), e);</span>
<span class="nc" id="L179">            return DocumentPermissionListResponse.error(&quot;Failed to grant bulk permissions: &quot; + e.getMessage());</span>
        }
    }

    /**
     * Revoke permission from a user or role for a document
     */
    @MutationMapping
    @PreAuthorize(&quot;hasRole('ADMIN') or @documentPermissionService.canGrantPermissions(#input.documentId, authentication.name)&quot;)
    @RateLimit(value = 25, window = 300, type = RateLimit.RateLimitType.PERMISSION_OPERATION,
               message = &quot;Permission revoke rate limit exceeded.&quot;)
    public DocumentPermissionResponse revokeDocumentPermission(@Argument RevokePermissionInput input) {
        try {
<span class="nc" id="L192">            logger.info(&quot;Revoking permission for document {} from user: {} role: {}&quot;, </span>
<span class="nc" id="L193">                       input.getDocumentId(), input.getUserId(), input.getRoleName());</span>
            
<span class="nc" id="L195">            input.validate();</span>
            
<span class="nc" id="L197">            boolean revoked = documentPermissionService.revokePermission(</span>
<span class="nc" id="L198">                input.getDocumentId(),</span>
<span class="nc" id="L199">                input.getUserId(),</span>
<span class="nc" id="L200">                input.getRoleName()</span>
            );
            
<span class="nc bnc" id="L203" title="All 2 branches missed.">            if (revoked) {</span>
<span class="nc" id="L204">                return DocumentPermissionResponse.success(&quot;Permission revoked successfully&quot;);</span>
            } else {
<span class="nc" id="L206">                return DocumentPermissionResponse.error(&quot;No active permissions found to revoke&quot;);</span>
            }
            
<span class="nc" id="L209">        } catch (Exception e) {</span>
<span class="nc" id="L210">            logger.error(&quot;Error revoking permission: {}&quot;, e.getMessage(), e);</span>
<span class="nc" id="L211">            return DocumentPermissionResponse.error(&quot;Failed to revoke permission: &quot; + e.getMessage());</span>
        }
    }

    /**
     * Update an existing permission
     */
    @MutationMapping
    @PreAuthorize(&quot;hasRole('ADMIN') or @documentPermissionService.canGrantPermissions(#input.documentId, authentication.name)&quot;)
    public DocumentPermissionResponse updateDocumentPermission(@Argument Long permissionId,
                                                              @Argument DocumentPermissionInput input) {
        try {
<span class="nc" id="L223">            logger.info(&quot;Updating permission {} for document {}&quot;, permissionId, input.getDocumentId());</span>

<span class="nc" id="L225">            input.validateForUpdate();</span>
            
<span class="nc" id="L227">            DocumentPermission permission = documentPermissionService.updatePermission(</span>
                permissionId,
<span class="nc" id="L229">                input.getPermissionType(),</span>
<span class="nc" id="L230">                input.getExpiresAt(),</span>
<span class="nc" id="L231">                input.getNotes()</span>
            );
            
<span class="nc" id="L234">            return DocumentPermissionResponse.success(</span>
                &quot;Permission updated successfully&quot;, 
                permission
            );
            
<span class="nc" id="L239">        } catch (Exception e) {</span>
<span class="nc" id="L240">            logger.error(&quot;Error updating permission: {}&quot;, e.getMessage(), e);</span>
<span class="nc" id="L241">            return DocumentPermissionResponse.error(&quot;Failed to update permission: &quot; + e.getMessage());</span>
        }
    }

    /**
     * Expire a permission immediately
     */
    @MutationMapping
    @PreAuthorize(&quot;hasRole('ADMIN')&quot;)
    public DocumentPermissionResponse expireDocumentPermission(@Argument Long permissionId) {
        try {
<span class="nc" id="L252">            logger.info(&quot;Expiring permission {}&quot;, permissionId);</span>
            
<span class="nc" id="L254">            DocumentPermission permission = documentPermissionService.expirePermission(permissionId);</span>
            
<span class="nc" id="L256">            return DocumentPermissionResponse.success(</span>
                &quot;Permission expired successfully&quot;, 
                permission
            );
            
<span class="nc" id="L261">        } catch (Exception e) {</span>
<span class="nc" id="L262">            logger.error(&quot;Error expiring permission: {}&quot;, e.getMessage(), e);</span>
<span class="nc" id="L263">            return DocumentPermissionResponse.error(&quot;Failed to expire permission: &quot; + e.getMessage());</span>
        }
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>