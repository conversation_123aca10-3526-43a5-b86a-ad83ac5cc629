<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>VirusScanException.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.exception</a> &gt; <span class="el_source">VirusScanException.java</span></div><h1>VirusScanException.java</h1><pre class="source lang-java linenums">package com.ascentbusiness.dms_svc.exception;

import com.ascentbusiness.dms_svc.dto.VirusScanResponse;
import com.ascentbusiness.dms_svc.enums.VirusScannerType;

import java.util.Map;

/**
 * Exception thrown when virus scanning operations fail or detect threats.
 * 
 * &lt;p&gt;This exception is thrown in the following scenarios:
 * &lt;ul&gt;
 *   &lt;li&gt;Virus or malware is detected in a file&lt;/li&gt;
 *   &lt;li&gt;File has suspicious characteristics&lt;/li&gt;
 *   &lt;li&gt;Virus scanner is unavailable or not configured&lt;/li&gt;
 *   &lt;li&gt;Scan operation fails due to technical issues&lt;/li&gt;
 *   &lt;li&gt;Scan operation times out&lt;/li&gt;
 * &lt;/ul&gt;
 * 
 * &lt;p&gt;The exception includes detailed information about the scan operation,
 * including the scan response (if available), scanner type, and file information
 * for comprehensive error handling and audit logging.
 * 
 * <AUTHOR> Development Team
 * @since 1.0.0
 */
public class VirusScanException extends DmsBusinessException {
    
    private final String fileName;
    private final VirusScannerType scannerType;
    private final VirusScanResponse scanResponse;
    
    /**
     * Creates a virus scan exception with basic information.
     * 
     * @param message the error message
     * @param fileName the name of the file being scanned
     * @param scannerType the type of scanner used
     */
    public VirusScanException(String message, String fileName, VirusScannerType scannerType) {
<span class="nc" id="L41">        super(message, &quot;VIRUS_SCAN_FAILED&quot;, createErrorDetails(fileName, scannerType, null));</span>
<span class="nc" id="L42">        this.fileName = fileName;</span>
<span class="nc" id="L43">        this.scannerType = scannerType;</span>
<span class="nc" id="L44">        this.scanResponse = null;</span>
<span class="nc" id="L45">    }</span>
    
    /**
     * Creates a virus scan exception with cause.
     * 
     * @param message the error message
     * @param cause the underlying cause
     * @param fileName the name of the file being scanned
     * @param scannerType the type of scanner used
     */
    public VirusScanException(String message, Throwable cause, String fileName, VirusScannerType scannerType) {
<span class="nc" id="L56">        super(message, &quot;VIRUS_SCAN_FAILED&quot;, createErrorDetails(fileName, scannerType, null), cause);</span>
<span class="nc" id="L57">        this.fileName = fileName;</span>
<span class="nc" id="L58">        this.scannerType = scannerType;</span>
<span class="nc" id="L59">        this.scanResponse = null;</span>
<span class="nc" id="L60">    }</span>
    
    /**
     * Creates a virus scan exception with scan response details.
     * 
     * @param message the error message
     * @param scanResponse the scan response containing detailed results
     */
    public VirusScanException(String message, VirusScanResponse scanResponse) {
<span class="nc" id="L69">        super(message, determineErrorCode(scanResponse), createErrorDetails(scanResponse));</span>
<span class="nc bnc" id="L70" title="All 2 branches missed.">        this.fileName = scanResponse != null ? scanResponse.getFileName() : null;</span>
<span class="nc bnc" id="L71" title="All 2 branches missed.">        this.scannerType = scanResponse != null ? scanResponse.getScannerType() : null;</span>
<span class="nc" id="L72">        this.scanResponse = scanResponse;</span>
<span class="nc" id="L73">    }</span>
    
    /**
     * Creates a virus scan exception for infected files.
     * 
     * @param fileName the name of the infected file
     * @param threats the list of detected threats
     * @param scanResponse the scan response
     * @return the virus scan exception
     */
    public static VirusScanException infected(String fileName, java.util.List&lt;String&gt; threats, 
                                            VirusScanResponse scanResponse) {
<span class="nc" id="L85">        String message = String.format(&quot;File '%s' is infected with: %s&quot;, </span>
<span class="nc" id="L86">                fileName, String.join(&quot;, &quot;, threats));</span>
<span class="nc" id="L87">        return new VirusScanException(message, scanResponse);</span>
    }
    
    /**
     * Creates a virus scan exception for suspicious files.
     * 
     * @param fileName the name of the suspicious file
     * @param scanResponse the scan response
     * @return the virus scan exception
     */
    public static VirusScanException suspicious(String fileName, VirusScanResponse scanResponse) {
<span class="nc" id="L98">        String message = String.format(&quot;File '%s' has suspicious characteristics and cannot be uploaded&quot;, fileName);</span>
<span class="nc" id="L99">        return new VirusScanException(message, scanResponse);</span>
    }
    
    /**
     * Creates a virus scan exception for scanner unavailability.
     * 
     * @param scannerType the unavailable scanner type
     * @param fileName the name of the file
     * @return the virus scan exception
     */
    public static VirusScanException scannerUnavailable(VirusScannerType scannerType, String fileName) {
<span class="nc" id="L110">        String message = String.format(&quot;Virus scanner %s is not available for scanning file '%s'&quot;, </span>
                scannerType, fileName);
<span class="nc" id="L112">        return new VirusScanException(message, fileName, scannerType);</span>
    }
    
    /**
     * Creates a virus scan exception for scan timeouts.
     * 
     * @param fileName the name of the file
     * @param scannerType the scanner type
     * @param timeoutMs the timeout value in milliseconds
     * @return the virus scan exception
     */
    public static VirusScanException timeout(String fileName, VirusScannerType scannerType, long timeoutMs) {
<span class="nc" id="L124">        String message = String.format(&quot;Virus scan timed out after %d ms for file '%s' using scanner %s&quot;, </span>
<span class="nc" id="L125">                timeoutMs, fileName, scannerType);</span>
<span class="nc" id="L126">        return new VirusScanException(message, fileName, scannerType);</span>
    }
    
    /**
     * Gets the name of the file that was being scanned.
     * 
     * @return the file name
     */
    public String getFileName() {
<span class="nc" id="L135">        return fileName;</span>
    }
    
    /**
     * Gets the type of scanner that was used.
     * 
     * @return the scanner type
     */
    public VirusScannerType getScannerType() {
<span class="nc" id="L144">        return scannerType;</span>
    }
    
    /**
     * Gets the scan response if available.
     * 
     * @return the scan response, or null if not available
     */
    public VirusScanResponse getScanResponse() {
<span class="nc" id="L153">        return scanResponse;</span>
    }
    
    /**
     * Checks if this exception represents a virus detection.
     * 
     * @return true if a virus was detected, false otherwise
     */
    public boolean isVirusDetected() {
<span class="nc bnc" id="L162" title="All 4 branches missed.">        return scanResponse != null &amp;&amp; scanResponse.hasThreats();</span>
    }
    
    /**
     * Gets the detected threats if any.
     * 
     * @return list of detected threats, or empty list if none
     */
    public java.util.List&lt;String&gt; getDetectedThreats() {
<span class="nc bnc" id="L171" title="All 2 branches missed.">        return scanResponse != null ? scanResponse.getDetectedThreats() : java.util.List.of();</span>
    }
    
    /**
     * Determines the appropriate error code based on the scan response.
     * 
     * @param scanResponse the scan response
     * @return the error code
     */
    private static String determineErrorCode(VirusScanResponse scanResponse) {
<span class="nc bnc" id="L181" title="All 2 branches missed.">        if (scanResponse == null) {</span>
<span class="nc" id="L182">            return &quot;VIRUS_SCAN_FAILED&quot;;</span>
        }
        
<span class="nc bnc" id="L185" title="All 6 branches missed.">        switch (scanResponse.getResult()) {</span>
            case INFECTED:
<span class="nc" id="L187">                return &quot;VIRUS_DETECTED&quot;;</span>
            case SUSPICIOUS:
<span class="nc" id="L189">                return &quot;SUSPICIOUS_FILE&quot;;</span>
            case ERROR:
<span class="nc" id="L191">                return &quot;VIRUS_SCAN_ERROR&quot;;</span>
            case UNAVAILABLE:
<span class="nc" id="L193">                return &quot;SCANNER_UNAVAILABLE&quot;;</span>
            case TIMEOUT:
<span class="nc" id="L195">                return &quot;SCAN_TIMEOUT&quot;;</span>
            default:
<span class="nc" id="L197">                return &quot;VIRUS_SCAN_FAILED&quot;;</span>
        }
    }
    
    /**
     * Creates error details map from basic information.
     * 
     * @param fileName the file name
     * @param scannerType the scanner type
     * @param scanResponse the scan response
     * @return the error details map
     */
    private static Map&lt;String, Object&gt; createErrorDetails(String fileName, VirusScannerType scannerType, 
                                                         VirusScanResponse scanResponse) {
<span class="nc" id="L211">        Map&lt;String, Object&gt; details = new java.util.HashMap&lt;&gt;();</span>
        
<span class="nc bnc" id="L213" title="All 2 branches missed.">        if (fileName != null) {</span>
<span class="nc" id="L214">            details.put(&quot;fileName&quot;, fileName);</span>
        }
        
<span class="nc bnc" id="L217" title="All 2 branches missed.">        if (scannerType != null) {</span>
<span class="nc" id="L218">            details.put(&quot;scannerType&quot;, scannerType.name());</span>
        }
        
<span class="nc bnc" id="L221" title="All 2 branches missed.">        if (scanResponse != null) {</span>
<span class="nc" id="L222">            details.put(&quot;scanResult&quot;, scanResponse.getResult().name());</span>
<span class="nc" id="L223">            details.put(&quot;scanId&quot;, scanResponse.getScanId());</span>
            
<span class="nc bnc" id="L225" title="All 2 branches missed.">            if (scanResponse.hasThreats()) {</span>
<span class="nc" id="L226">                details.put(&quot;detectedThreats&quot;, scanResponse.getDetectedThreats());</span>
            }
            
<span class="nc bnc" id="L229" title="All 2 branches missed.">            if (scanResponse.getErrorMessage() != null) {</span>
<span class="nc" id="L230">                details.put(&quot;scanErrorMessage&quot;, scanResponse.getErrorMessage());</span>
            }
        }
        
<span class="nc" id="L234">        return details;</span>
    }
    
    /**
     * Creates error details map from scan response.
     * 
     * @param scanResponse the scan response
     * @return the error details map
     */
    private static Map&lt;String, Object&gt; createErrorDetails(VirusScanResponse scanResponse) {
<span class="nc" id="L244">        return createErrorDetails(</span>
<span class="nc bnc" id="L245" title="All 2 branches missed.">                scanResponse != null ? scanResponse.getFileName() : null,</span>
<span class="nc bnc" id="L246" title="All 2 branches missed.">                scanResponse != null ? scanResponse.getScannerType() : null,</span>
                scanResponse
        );
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>