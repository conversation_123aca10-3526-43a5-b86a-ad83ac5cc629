<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>WebhookDeliveryRepository.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.repository</a> &gt; <span class="el_source">WebhookDeliveryRepository.java</span></div><h1>WebhookDeliveryRepository.java</h1><pre class="source lang-java linenums">package com.ascentbusiness.dms_svc.repository;

import com.ascentbusiness.dms_svc.entity.WebhookDelivery;
import com.ascentbusiness.dms_svc.enums.DeliveryStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Repository interface for WebhookDelivery entities
 */
@Repository
public interface WebhookDeliveryRepository extends JpaRepository&lt;WebhookDelivery, Long&gt; {

    /**
     * Find deliveries by webhook endpoint
     */
    List&lt;WebhookDelivery&gt; findByWebhookEndpointId(Long webhookEndpointId);

    /**
     * Find deliveries by webhook endpoint with pagination
     */
    Page&lt;WebhookDelivery&gt; findByWebhookEndpointId(Long webhookEndpointId, Pageable pageable);

    /**
     * Find deliveries by system event
     */
    List&lt;WebhookDelivery&gt; findBySystemEventId(Long systemEventId);

    /**
     * Find deliveries by status
     */
    List&lt;WebhookDelivery&gt; findByDeliveryStatus(DeliveryStatus status);

    /**
     * Find pending deliveries
     */
    List&lt;WebhookDelivery&gt; findByDeliveryStatusAndScheduledDateBefore(DeliveryStatus status, LocalDateTime currentTime);

    /**
     * Find failed deliveries that can be retried
     */
    @Query(&quot;SELECT wd FROM WebhookDelivery wd WHERE wd.deliveryStatus = 'FAILED' AND wd.deliveryAttempt &lt; wd.webhookEndpoint.maxRetries&quot;)
    List&lt;WebhookDelivery&gt; findRetryableFailedDeliveries();

    /**
     * Find deliveries by correlation ID
     */
    List&lt;WebhookDelivery&gt; findByCorrelationId(String correlationId);

    /**
     * Find deliveries within date range
     */
    List&lt;WebhookDelivery&gt; findByScheduledDateBetween(LocalDateTime startDate, LocalDateTime endDate);

    /**
     * Find deliveries attempted within date range
     */
    List&lt;WebhookDelivery&gt; findByAttemptedDateBetween(LocalDateTime startDate, LocalDateTime endDate);

    /**
     * Find deliveries completed within date range
     */
    List&lt;WebhookDelivery&gt; findByCompletedDateBetween(LocalDateTime startDate, LocalDateTime endDate);

    /**
     * Find successful deliveries (convenience method)
     */
    default List&lt;WebhookDelivery&gt; findSuccessfulDeliveries() {
<span class="nc" id="L77">        return findByDeliveryStatus(DeliveryStatus.SUCCESS);</span>
    }

    /**
     * Find failed deliveries (convenience method)
     */
    default List&lt;WebhookDelivery&gt; findFailedDeliveries() {
<span class="nc" id="L84">        return findByDeliveryStatus(DeliveryStatus.FAILED);</span>
    }

    /**
     * Find deliveries with slow response times
     */
    List&lt;WebhookDelivery&gt; findByDurationMsGreaterThan(Integer thresholdMs);

    /**
     * Find deliveries with fast response times
     */
    List&lt;WebhookDelivery&gt; findByDurationMsLessThan(Integer thresholdMs);

    /**
     * Count deliveries by status
     */
    long countByDeliveryStatus(DeliveryStatus status);

    /**
     * Count deliveries by webhook endpoint
     */
    long countByWebhookEndpointId(Long webhookEndpointId);

    /**
     * Count deliveries by webhook endpoint and status
     */
    long countByWebhookEndpointIdAndDeliveryStatus(Long webhookEndpointId, DeliveryStatus status);

    /**
     * Get delivery statistics by status
     */
    @Query(&quot;SELECT wd.deliveryStatus, COUNT(wd) FROM WebhookDelivery wd GROUP BY wd.deliveryStatus&quot;)
    List&lt;Object[]&gt; getDeliveryStatisticsByStatus();

    /**
     * Get delivery statistics by webhook endpoint
     */
    @Query(&quot;SELECT wd.webhookEndpoint.id, wd.webhookEndpoint.name, COUNT(wd), SUM(CASE WHEN wd.deliveryStatus = 'SUCCESS' THEN 1 ELSE 0 END) FROM WebhookDelivery wd GROUP BY wd.webhookEndpoint.id, wd.webhookEndpoint.name ORDER BY COUNT(wd) DESC&quot;)
    List&lt;Object[]&gt; getDeliveryStatisticsByEndpoint();

    /**
     * Get daily delivery counts for the last 30 days
     */
    @Query(&quot;SELECT DATE(wd.scheduledDate), COUNT(wd) FROM WebhookDelivery wd WHERE wd.scheduledDate &gt;= :cutoffDate GROUP BY DATE(wd.scheduledDate) ORDER BY DATE(wd.scheduledDate)&quot;)
    List&lt;Object[]&gt; getDailyDeliveryCounts(@Param(&quot;cutoffDate&quot;) LocalDateTime cutoffDate);

    /**
     * Get hourly delivery counts for the last 24 hours
     */
    @Query(&quot;SELECT HOUR(wd.scheduledDate), COUNT(wd) FROM WebhookDelivery wd WHERE wd.scheduledDate &gt;= :cutoffDate GROUP BY HOUR(wd.scheduledDate) ORDER BY HOUR(wd.scheduledDate)&quot;)
    List&lt;Object[]&gt; getHourlyDeliveryCounts(@Param(&quot;cutoffDate&quot;) LocalDateTime cutoffDate);

    /**
     * Get average response time by webhook endpoint
     */
    @Query(&quot;SELECT wd.webhookEndpoint.id, wd.webhookEndpoint.name, AVG(wd.durationMs) FROM WebhookDelivery wd WHERE wd.durationMs IS NOT NULL GROUP BY wd.webhookEndpoint.id, wd.webhookEndpoint.name&quot;)
    List&lt;Object[]&gt; getAverageResponseTimeByEndpoint();

    /**
     * Find most recent delivery
     */
    Optional&lt;WebhookDelivery&gt; findTopByOrderByScheduledDateDesc();

    /**
     * Find most recent deliveries
     */
    List&lt;WebhookDelivery&gt; findTop10ByOrderByScheduledDateDesc();

    /**
     * Find deliveries by HTTP status code
     */
    List&lt;WebhookDelivery&gt; findByHttpStatusCode(Integer statusCode);

    /**
     * Find deliveries with HTTP client errors (4xx)
     */
    @Query(&quot;SELECT wd FROM WebhookDelivery wd WHERE wd.httpStatusCode &gt;= 400 AND wd.httpStatusCode &lt; 500&quot;)
    List&lt;WebhookDelivery&gt; findDeliveriesWithClientErrors();

    /**
     * Find deliveries with HTTP server errors (5xx)
     */
    @Query(&quot;SELECT wd FROM WebhookDelivery wd WHERE wd.httpStatusCode &gt;= 500 AND wd.httpStatusCode &lt; 600&quot;)
    List&lt;WebhookDelivery&gt; findDeliveriesWithServerErrors();

    /**
     * Find deliveries by delivery attempt
     */
    List&lt;WebhookDelivery&gt; findByDeliveryAttempt(Integer attempt);

    /**
     * Find deliveries with multiple attempts
     */
    List&lt;WebhookDelivery&gt; findByDeliveryAttemptGreaterThan(Integer attempt);

    /**
     * Find overdue deliveries (scheduled but not attempted)
     */
    @Query(&quot;SELECT wd FROM WebhookDelivery wd WHERE wd.deliveryStatus = 'PENDING' AND wd.scheduledDate &lt; :cutoffTime AND wd.attemptedDate IS NULL&quot;)
    List&lt;WebhookDelivery&gt; findOverdueDeliveries(@Param(&quot;cutoffTime&quot;) LocalDateTime cutoffTime);
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>