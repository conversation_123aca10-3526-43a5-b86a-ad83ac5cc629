<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>ElasticsearchConfig.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.config</a> &gt; <span class="el_source">ElasticsearchConfig.java</span></div><h1>ElasticsearchConfig.java</h1><pre class="source lang-java linenums">package com.ascentbusiness.dms_svc.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.elasticsearch.client.ClientConfiguration;
import org.springframework.data.elasticsearch.client.elc.ElasticsearchConfiguration;

/**
 * Elasticsearch Configuration for Document Management System
 *
 * Configures Elasticsearch client and repositories for advanced document search capabilities.
 * Supports both local development and production environments with security.
 */
@Configuration
@ConditionalOnProperty(name = &quot;elasticsearch.enabled&quot;, havingValue = &quot;true&quot;, matchIfMissing = false)
<span class="nc" id="L17">public class ElasticsearchConfig extends ElasticsearchConfiguration {</span>

    @Value(&quot;${elasticsearch.host:localhost}&quot;)
    private String elasticsearchHost;

    @Value(&quot;${elasticsearch.port:9200}&quot;)
    private int elasticsearchPort;

    @Value(&quot;${elasticsearch.protocol:http}&quot;)
    private String elasticsearchProtocol;

    @Value(&quot;${elasticsearch.username:}&quot;)
    private String elasticsearchUsername;

    @Value(&quot;${elasticsearch.password:}&quot;)
    private String elasticsearchPassword;

    @Value(&quot;${elasticsearch.connection-timeout:10000}&quot;)
    private int connectionTimeout;

    @Value(&quot;${elasticsearch.socket-timeout:30000}&quot;)
    private int socketTimeout;

    @Override
    public ClientConfiguration clientConfiguration() {
        // Create basic configuration
<span class="nc" id="L43">        ClientConfiguration.MaybeSecureClientConfigurationBuilder builder = ClientConfiguration.builder()</span>
<span class="nc" id="L44">                .connectedTo(elasticsearchHost + &quot;:&quot; + elasticsearchPort);</span>

        // Add authentication if credentials are provided
<span class="nc bnc" id="L47" title="All 6 branches missed.">        if (elasticsearchUsername != null &amp;&amp; !elasticsearchUsername.trim().isEmpty() &amp;&amp;</span>
<span class="nc bnc" id="L48" title="All 2 branches missed.">            elasticsearchPassword != null &amp;&amp; !elasticsearchPassword.trim().isEmpty()) {</span>
<span class="nc" id="L49">            builder.withBasicAuth(elasticsearchUsername, elasticsearchPassword);</span>
        }

<span class="nc" id="L52">        return builder.build();</span>
    }


}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>