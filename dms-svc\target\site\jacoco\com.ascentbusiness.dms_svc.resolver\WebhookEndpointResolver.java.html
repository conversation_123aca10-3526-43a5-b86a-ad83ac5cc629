<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>WebhookEndpointResolver.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.resolver</a> &gt; <span class="el_source">WebhookEndpointResolver.java</span></div><h1>WebhookEndpointResolver.java</h1><pre class="source lang-java linenums">package com.ascentbusiness.dms_svc.resolver;

import com.ascentbusiness.dms_svc.entity.WebhookEndpoint;
import com.ascentbusiness.dms_svc.entity.WebhookDelivery;
import com.ascentbusiness.dms_svc.enums.EventType;
import com.ascentbusiness.dms_svc.enums.WebhookAuthType;
import com.ascentbusiness.dms_svc.service.WebhookEndpointService;
import com.ascentbusiness.dms_svc.security.UserContext;
import com.ascentbusiness.dms_svc.dto.WebhookEndpointStatistics;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.graphql.data.method.annotation.Argument;
import org.springframework.graphql.data.method.annotation.MutationMapping;
import org.springframework.graphql.data.method.annotation.QueryMapping;
import org.springframework.graphql.data.method.annotation.SchemaMapping;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;

import java.util.List;

/**
 * GraphQL resolver for WebhookEndpoint operations
 */
@Controller
<span class="nc" id="L32">@RequiredArgsConstructor</span>
<span class="nc" id="L33">@Slf4j</span>
public class WebhookEndpointResolver {

    private final WebhookEndpointService webhookEndpointService;
    private final UserContext userContext;

    // ===== QUERIES =====

    @QueryMapping
    @PreAuthorize(&quot;hasAnyRole('ADMIN', 'MANAGER', 'USER')&quot;)
    public WebhookEndpoint getWebhookEndpoint(@Argument Long id) {
<span class="nc" id="L44">        log.info(&quot;Getting webhook endpoint with ID: {}&quot;, id);</span>
<span class="nc" id="L45">        return webhookEndpointService.getWebhookEndpointById(id);</span>
    }

    @QueryMapping
    @PreAuthorize(&quot;hasAnyRole('ADMIN', 'MANAGER', 'USER')&quot;)
    public WebhookEndpointPage getWebhookEndpoints(@Argument WebhookPaginationInput pagination) {
<span class="nc" id="L51">        log.info(&quot;Getting webhook endpoints with pagination: {}&quot;, pagination);</span>
        
<span class="nc" id="L53">        Pageable pageable = createPageable(pagination);</span>
<span class="nc" id="L54">        Page&lt;WebhookEndpoint&gt; page = webhookEndpointService.getWebhookEndpointsByCreator(userContext.getUserId(), pageable);</span>
        
<span class="nc" id="L56">        return WebhookEndpointPage.builder()</span>
<span class="nc" id="L57">                .content(page.getContent())</span>
<span class="nc" id="L58">                .totalElements((int) page.getTotalElements())</span>
<span class="nc" id="L59">                .totalPages(page.getTotalPages())</span>
<span class="nc" id="L60">                .size(page.getSize())</span>
<span class="nc" id="L61">                .number(page.getNumber())</span>
<span class="nc" id="L62">                .first(page.isFirst())</span>
<span class="nc" id="L63">                .last(page.isLast())</span>
<span class="nc" id="L64">                .build();</span>
    }

    @QueryMapping
    @PreAuthorize(&quot;hasAnyRole('ADMIN', 'MANAGER')&quot;)
    public List&lt;WebhookEndpoint&gt; getAllActiveWebhookEndpoints() {
<span class="nc" id="L70">        log.info(&quot;Getting all active webhook endpoints&quot;);</span>
<span class="nc" id="L71">        return webhookEndpointService.getAllActiveWebhookEndpoints();</span>
    }

    @QueryMapping
    @PreAuthorize(&quot;hasAnyRole('ADMIN', 'MANAGER', 'USER')&quot;)
    public List&lt;WebhookEndpoint&gt; searchWebhookEndpoints(@Argument String name) {
<span class="nc" id="L77">        log.info(&quot;Searching webhook endpoints with name: {}&quot;, name);</span>
<span class="nc" id="L78">        return webhookEndpointService.searchWebhookEndpoints(name);</span>
    }

    @QueryMapping
    @PreAuthorize(&quot;hasAnyRole('ADMIN', 'MANAGER', 'USER')&quot;)
    public List&lt;WebhookEndpoint&gt; getWebhookEndpointsForEventType(@Argument EventType eventType) {
<span class="nc" id="L84">        log.info(&quot;Getting webhook endpoints for event type: {}&quot;, eventType);</span>
<span class="nc" id="L85">        return webhookEndpointService.getWebhookEndpointsForEventType(eventType.name());</span>
    }

    @QueryMapping
    @PreAuthorize(&quot;hasAnyRole('ADMIN', 'MANAGER')&quot;)
    public List&lt;WebhookEndpoint&gt; getWebhookEndpointsWithHighFailureRate(@Argument Double threshold) {
<span class="nc" id="L91">        log.info(&quot;Getting webhook endpoints with high failure rate: {}&quot;, threshold);</span>
<span class="nc bnc" id="L92" title="All 2 branches missed.">        double actualThreshold = threshold != null ? threshold : 0.5;</span>
<span class="nc" id="L93">        return webhookEndpointService.getWebhookEndpointsWithHighFailureRate(actualThreshold);</span>
    }

    @QueryMapping
    @PreAuthorize(&quot;hasAnyRole('ADMIN', 'MANAGER')&quot;)
    public List&lt;WebhookEndpoint&gt; getUnusedWebhookEndpoints(@Argument Integer daysBack) {
<span class="nc" id="L99">        log.info(&quot;Getting unused webhook endpoints for {} days back&quot;, daysBack);</span>
<span class="nc bnc" id="L100" title="All 2 branches missed.">        int actualDaysBack = daysBack != null ? daysBack : 30;</span>
<span class="nc" id="L101">        return webhookEndpointService.getUnusedWebhookEndpoints(actualDaysBack);</span>
    }

    @QueryMapping
    @PreAuthorize(&quot;hasAnyRole('ADMIN', 'MANAGER')&quot;)
    public WebhookEndpointService.WebhookStatistics getWebhookStatistics() {
<span class="nc" id="L107">        log.info(&quot;Getting webhook statistics&quot;);</span>
<span class="nc" id="L108">        return webhookEndpointService.getWebhookStatistics();</span>
    }

    // ===== ENHANCED WEBHOOK MANAGEMENT OPERATIONS =====
    // These operations implement the webhook-management-schema.graphqls operations

    @QueryMapping
    @PreAuthorize(&quot;hasAnyRole('ADMIN', 'MANAGER')&quot;)
    public WebhookEndpointStatistics getWebhookEndpointStatistics() {
<span class="nc" id="L117">        log.info(&quot;Getting enhanced webhook endpoint statistics&quot;);</span>

        try {
<span class="nc" id="L120">            WebhookEndpointService.WebhookStatistics stats = webhookEndpointService.getWebhookStatistics();</span>
<span class="nc bnc" id="L121" title="All 2 branches missed.">            if (stats == null) {</span>
<span class="nc" id="L122">                log.warn(&quot;WebhookStatistics is null, returning default values&quot;);</span>
<span class="nc" id="L123">                return createDefaultWebhookStatistics();</span>
            }

            // Get average response time with null safety
<span class="nc" id="L127">            Double avgResponseTime = webhookEndpointService.getAverageResponseTime();</span>
<span class="nc bnc" id="L128" title="All 2 branches missed.">            float avgResponseTimeFloat = avgResponseTime != null ? avgResponseTime.floatValue() : 0.0f;</span>

            // Get last delivery time with null safety
<span class="nc" id="L131">            LocalDateTime lastDeliveryTime = webhookEndpointService.getLastDeliveryTime();</span>
<span class="nc" id="L132">            OffsetDateTime lastDeliveryOffsetTime = convertToOffsetDateTime(lastDeliveryTime);</span>

            // Get endpoint counts with null safety
<span class="nc" id="L135">            long healthyCount = webhookEndpointService.getHealthyEndpointsCount();</span>
<span class="nc" id="L136">            long unhealthyCount = webhookEndpointService.getUnhealthyEndpointsCount();</span>

<span class="nc" id="L138">            return WebhookEndpointStatistics.builder()</span>
<span class="nc" id="L139">                    .totalEndpoints((int) stats.getTotalActive())</span>
<span class="nc" id="L140">                    .activeEndpoints((int) stats.getTotalActive())</span>
<span class="nc" id="L141">                    .verifiedEndpoints((int) stats.getTotalVerified())</span>
<span class="nc" id="L142">                    .totalDeliveries(stats.getTotalSuccesses() + stats.getTotalFailures())</span>
<span class="nc" id="L143">                    .successfulDeliveries(stats.getTotalSuccesses())</span>
<span class="nc" id="L144">                    .failedDeliveries(stats.getTotalFailures())</span>
<span class="nc" id="L145">                    .averageResponseTime(avgResponseTimeFloat)</span>
<span class="nc" id="L146">                    .successRate(calculateSuccessRate(stats.getTotalSuccesses(), stats.getTotalFailures()))</span>
<span class="nc" id="L147">                    .lastDeliveryTime(lastDeliveryOffsetTime)</span>
<span class="nc" id="L148">                    .healthyEndpoints((int) healthyCount)</span>
<span class="nc" id="L149">                    .unhealthyEndpoints((int) unhealthyCount)</span>
<span class="nc" id="L150">                    .build();</span>
<span class="nc" id="L151">        } catch (Exception e) {</span>
<span class="nc" id="L152">            log.error(&quot;Error getting webhook endpoint statistics&quot;, e);</span>
<span class="nc" id="L153">            return createDefaultWebhookStatistics();</span>
        }
    }

    private WebhookEndpointStatistics createDefaultWebhookStatistics() {
<span class="nc" id="L158">        return WebhookEndpointStatistics.builder()</span>
<span class="nc" id="L159">                .totalEndpoints(0)</span>
<span class="nc" id="L160">                .activeEndpoints(0)</span>
<span class="nc" id="L161">                .verifiedEndpoints(0)</span>
<span class="nc" id="L162">                .totalDeliveries(0L)</span>
<span class="nc" id="L163">                .successfulDeliveries(0L)</span>
<span class="nc" id="L164">                .failedDeliveries(0L)</span>
<span class="nc" id="L165">                .averageResponseTime(0.0f)</span>
<span class="nc" id="L166">                .successRate(0.0f)</span>
<span class="nc" id="L167">                .lastDeliveryTime(null)</span>
<span class="nc" id="L168">                .healthyEndpoints(0)</span>
<span class="nc" id="L169">                .unhealthyEndpoints(0)</span>
<span class="nc" id="L170">                .build();</span>
    }

    @QueryMapping
    @PreAuthorize(&quot;hasAnyRole('ADMIN', 'MANAGER')&quot;)
    public List&lt;WebhookDelivery&gt; getFailedWebhookDeliveries() {
<span class="nc" id="L176">        log.info(&quot;Getting failed webhook deliveries&quot;);</span>
<span class="nc" id="L177">        return webhookEndpointService.getFailedWebhookDeliveries();</span>
    }

    @QueryMapping
    @PreAuthorize(&quot;hasAnyRole('ADMIN', 'MANAGER', 'USER')&quot;)
    public List&lt;WebhookDelivery&gt; getWebhookDeliveriesForEndpoint(@Argument Long webhookEndpointId) {
<span class="nc" id="L183">        log.info(&quot;Getting webhook deliveries for endpoint ID: {}&quot;, webhookEndpointId);</span>
<span class="nc" id="L184">        return webhookEndpointService.getWebhookDeliveriesForEndpoint(webhookEndpointId);</span>
    }

    @QueryMapping
    @PreAuthorize(&quot;hasAnyRole('ADMIN', 'MANAGER')&quot;)
    public List&lt;WebhookDelivery&gt; getWebhookDeliveriesForEvent(@Argument Long systemEventId) {
<span class="nc" id="L190">        log.info(&quot;Getting webhook deliveries for event ID: {}&quot;, systemEventId);</span>
<span class="nc" id="L191">        return webhookEndpointService.getWebhookDeliveriesForEvent(systemEventId);</span>
    }

    private float calculateSuccessRate(long successes, long failures) {
<span class="nc" id="L195">        long total = successes + failures;</span>
<span class="nc bnc" id="L196" title="All 2 branches missed.">        return total &gt; 0 ? (float) successes / total * 100.0f : 0.0f;</span>
    }

    // ===== MUTATIONS =====

    @MutationMapping
    @PreAuthorize(&quot;hasAnyRole('ADMIN', 'MANAGER', 'USER')&quot;)
    public WebhookEndpoint createWebhookEndpoint(@Argument WebhookEndpointInput input) {
<span class="nc" id="L204">        log.info(&quot;Creating webhook endpoint: {}&quot;, input.getName());</span>
        
<span class="nc" id="L206">        WebhookEndpoint endpoint = WebhookEndpoint.builder()</span>
<span class="nc" id="L207">                .name(input.getName())</span>
<span class="nc" id="L208">                .description(input.getDescription())</span>
<span class="nc" id="L209">                .url(input.getUrl())</span>
<span class="nc" id="L210">                .httpMethod(input.getHttpMethod())</span>
<span class="nc" id="L211">                .contentType(input.getContentType())</span>
<span class="nc" id="L212">                .timeoutSeconds(input.getTimeoutSeconds())</span>
<span class="nc" id="L213">                .authType(input.getAuthType())</span>
<span class="nc" id="L214">                .maxRetries(input.getMaxRetries())</span>
<span class="nc" id="L215">                .rateLimitPerMinute(input.getRateLimitPerMinute())</span>
<span class="nc" id="L216">                .build();</span>
        
<span class="nc" id="L218">        return webhookEndpointService.createWebhookEndpoint(endpoint, userContext.getUserId());</span>
    }

    @MutationMapping
    @PreAuthorize(&quot;hasAnyRole('ADMIN', 'MANAGER', 'USER')&quot;)
    public WebhookEndpoint updateWebhookEndpoint(@Argument Long id, @Argument WebhookEndpointInput input) {
<span class="nc" id="L224">        log.info(&quot;Updating webhook endpoint ID: {}&quot;, id);</span>
        
<span class="nc" id="L226">        WebhookEndpoint endpoint = WebhookEndpoint.builder()</span>
<span class="nc" id="L227">                .name(input.getName())</span>
<span class="nc" id="L228">                .description(input.getDescription())</span>
<span class="nc" id="L229">                .url(input.getUrl())</span>
<span class="nc" id="L230">                .httpMethod(input.getHttpMethod())</span>
<span class="nc" id="L231">                .contentType(input.getContentType())</span>
<span class="nc" id="L232">                .timeoutSeconds(input.getTimeoutSeconds())</span>
<span class="nc" id="L233">                .authType(input.getAuthType())</span>
<span class="nc" id="L234">                .maxRetries(input.getMaxRetries())</span>
<span class="nc" id="L235">                .rateLimitPerMinute(input.getRateLimitPerMinute())</span>
<span class="nc" id="L236">                .build();</span>
        
<span class="nc" id="L238">        return webhookEndpointService.updateWebhookEndpoint(id, endpoint, userContext.getUserId());</span>
    }

    @MutationMapping
    @PreAuthorize(&quot;hasAnyRole('ADMIN', 'MANAGER', 'USER')&quot;)
    public WebhookEndpoint verifyWebhookEndpoint(@Argument String verificationToken) {
<span class="nc" id="L244">        log.info(&quot;Verifying webhook endpoint with token: {}&quot;, verificationToken);</span>
<span class="nc" id="L245">        return webhookEndpointService.verifyWebhookEndpoint(verificationToken);</span>
    }

    @MutationMapping
    @PreAuthorize(&quot;hasAnyRole('ADMIN', 'MANAGER', 'USER')&quot;)
    public WebhookEndpoint activateWebhookEndpoint(@Argument Long id) {
<span class="nc" id="L251">        log.info(&quot;Activating webhook endpoint ID: {}&quot;, id);</span>
<span class="nc" id="L252">        return webhookEndpointService.activateWebhookEndpoint(id, userContext.getUserId());</span>
    }

    @MutationMapping
    @PreAuthorize(&quot;hasAnyRole('ADMIN', 'MANAGER', 'USER')&quot;)
    public WebhookEndpoint deactivateWebhookEndpoint(@Argument Long id) {
<span class="nc" id="L258">        log.info(&quot;Deactivating webhook endpoint ID: {}&quot;, id);</span>
<span class="nc" id="L259">        return webhookEndpointService.deactivateWebhookEndpoint(id, userContext.getUserId());</span>
    }

    @MutationMapping
    @PreAuthorize(&quot;hasRole('ADMIN')&quot;)
    public Boolean deleteWebhookEndpoint(@Argument Long id) {
<span class="nc" id="L265">        log.info(&quot;Deleting webhook endpoint ID: {}&quot;, id);</span>
<span class="nc" id="L266">        webhookEndpointService.deactivateWebhookEndpoint(id, userContext.getUserId());</span>
<span class="nc" id="L267">        return true;</span>
    }

    // ===== FIELD RESOLVERS =====

    @SchemaMapping(typeName = &quot;WebhookEndpoint&quot;, field = &quot;deliveries&quot;)
    public List&lt;WebhookDelivery&gt; getDeliveries(WebhookEndpoint webhookEndpoint) {
<span class="nc bnc" id="L274" title="All 2 branches missed.">        return webhookEndpoint.getDeliveries() != null ? </span>
<span class="nc" id="L275">               webhookEndpoint.getDeliveries().stream().toList() : List.of();</span>
    }

    @SchemaMapping(typeName = &quot;WebhookEndpoint&quot;, field = &quot;hasAuthentication&quot;)
    public Boolean getHasAuthentication(WebhookEndpoint webhookEndpoint) {
<span class="nc" id="L280">        return webhookEndpoint.hasAuthentication();</span>
    }

    @SchemaMapping(typeName = &quot;WebhookEndpoint&quot;, field = &quot;successRate&quot;)
    public Double getSuccessRate(WebhookEndpoint webhookEndpoint) {
<span class="nc" id="L285">        return webhookEndpoint.getSuccessRate();</span>
    }

    @SchemaMapping(typeName = &quot;WebhookEndpoint&quot;, field = &quot;totalDeliveries&quot;)
    public Integer getTotalDeliveries(WebhookEndpoint webhookEndpoint) {
<span class="nc" id="L290">        return webhookEndpoint.getTotalDeliveries();</span>
    }

    @SchemaMapping(typeName = &quot;WebhookEndpoint&quot;, field = &quot;isHealthy&quot;)
    public Boolean getIsHealthy(WebhookEndpoint webhookEndpoint) {
<span class="nc" id="L295">        return webhookEndpoint.isHealthy();</span>
    }

    @SchemaMapping(typeName = &quot;WebhookEndpoint&quot;, field = &quot;hasRecentFailures&quot;)
    public Boolean getHasRecentFailures(WebhookEndpoint webhookEndpoint) {
<span class="nc" id="L300">        return webhookEndpoint.hasRecentFailures();</span>
    }

    // ===== HELPER METHODS =====

    private Pageable createPageable(WebhookPaginationInput pagination) {
<span class="nc bnc" id="L306" title="All 2 branches missed.">        if (pagination == null) {</span>
<span class="nc" id="L307">            return PageRequest.of(0, 10, Sort.by(Sort.Direction.DESC, &quot;createdDate&quot;));</span>
        }
        
<span class="nc bnc" id="L310" title="All 2 branches missed.">        Sort.Direction direction = &quot;ASC&quot;.equalsIgnoreCase(pagination.getSortDirection()) ? </span>
<span class="nc" id="L311">                                  Sort.Direction.ASC : Sort.Direction.DESC;</span>
<span class="nc bnc" id="L312" title="All 2 branches missed.">        Sort sort = Sort.by(direction, pagination.getSortBy() != null ? pagination.getSortBy() : &quot;createdDate&quot;);</span>
        
<span class="nc" id="L314">        return PageRequest.of(</span>
<span class="nc bnc" id="L315" title="All 2 branches missed.">                pagination.getPage() != null ? pagination.getPage() : 0,</span>
<span class="nc bnc" id="L316" title="All 2 branches missed.">                pagination.getSize() != null ? pagination.getSize() : 10,</span>
                sort
        );
    }

    // ===== INPUT/OUTPUT CLASSES =====

<span class="nc" id="L323">    public static class WebhookEndpointInput {</span>
        private String name;
        private String description;
        private String url;
        private String httpMethod;
        private String contentType;
        private Integer timeoutSeconds;
        private WebhookAuthType authType;
        private String authConfig;
        private String customHeaders;
        private List&lt;EventType&gt; eventTypes;
        private Integer maxRetries;
        private Integer retryDelaySeconds;
        private Boolean exponentialBackoff;
        private Integer rateLimitPerMinute;
        private Integer rateLimitPerHour;
        
        // Getters and setters
<span class="nc" id="L341">        public String getName() { return name; }</span>
<span class="nc" id="L342">        public void setName(String name) { this.name = name; }</span>
<span class="nc" id="L343">        public String getDescription() { return description; }</span>
<span class="nc" id="L344">        public void setDescription(String description) { this.description = description; }</span>
<span class="nc" id="L345">        public String getUrl() { return url; }</span>
<span class="nc" id="L346">        public void setUrl(String url) { this.url = url; }</span>
<span class="nc" id="L347">        public String getHttpMethod() { return httpMethod; }</span>
<span class="nc" id="L348">        public void setHttpMethod(String httpMethod) { this.httpMethod = httpMethod; }</span>
<span class="nc" id="L349">        public String getContentType() { return contentType; }</span>
<span class="nc" id="L350">        public void setContentType(String contentType) { this.contentType = contentType; }</span>
<span class="nc" id="L351">        public Integer getTimeoutSeconds() { return timeoutSeconds; }</span>
<span class="nc" id="L352">        public void setTimeoutSeconds(Integer timeoutSeconds) { this.timeoutSeconds = timeoutSeconds; }</span>
<span class="nc" id="L353">        public WebhookAuthType getAuthType() { return authType; }</span>
<span class="nc" id="L354">        public void setAuthType(WebhookAuthType authType) { this.authType = authType; }</span>
<span class="nc" id="L355">        public String getAuthConfig() { return authConfig; }</span>
<span class="nc" id="L356">        public void setAuthConfig(String authConfig) { this.authConfig = authConfig; }</span>
<span class="nc" id="L357">        public String getCustomHeaders() { return customHeaders; }</span>
<span class="nc" id="L358">        public void setCustomHeaders(String customHeaders) { this.customHeaders = customHeaders; }</span>
<span class="nc" id="L359">        public List&lt;EventType&gt; getEventTypes() { return eventTypes; }</span>
<span class="nc" id="L360">        public void setEventTypes(List&lt;EventType&gt; eventTypes) { this.eventTypes = eventTypes; }</span>
<span class="nc" id="L361">        public Integer getMaxRetries() { return maxRetries; }</span>
<span class="nc" id="L362">        public void setMaxRetries(Integer maxRetries) { this.maxRetries = maxRetries; }</span>
<span class="nc" id="L363">        public Integer getRetryDelaySeconds() { return retryDelaySeconds; }</span>
<span class="nc" id="L364">        public void setRetryDelaySeconds(Integer retryDelaySeconds) { this.retryDelaySeconds = retryDelaySeconds; }</span>
<span class="nc" id="L365">        public Boolean getExponentialBackoff() { return exponentialBackoff; }</span>
<span class="nc" id="L366">        public void setExponentialBackoff(Boolean exponentialBackoff) { this.exponentialBackoff = exponentialBackoff; }</span>
<span class="nc" id="L367">        public Integer getRateLimitPerMinute() { return rateLimitPerMinute; }</span>
<span class="nc" id="L368">        public void setRateLimitPerMinute(Integer rateLimitPerMinute) { this.rateLimitPerMinute = rateLimitPerMinute; }</span>
<span class="nc" id="L369">        public Integer getRateLimitPerHour() { return rateLimitPerHour; }</span>
<span class="nc" id="L370">        public void setRateLimitPerHour(Integer rateLimitPerHour) { this.rateLimitPerHour = rateLimitPerHour; }</span>
    }

<span class="nc" id="L373">    public static class WebhookPaginationInput {</span>
        private Integer page;
        private Integer size;
        private String sortBy;
        private String sortDirection;
        
        // Getters and setters
<span class="nc" id="L380">        public Integer getPage() { return page; }</span>
<span class="nc" id="L381">        public void setPage(Integer page) { this.page = page; }</span>
<span class="nc" id="L382">        public Integer getSize() { return size; }</span>
<span class="nc" id="L383">        public void setSize(Integer size) { this.size = size; }</span>
<span class="nc" id="L384">        public String getSortBy() { return sortBy; }</span>
<span class="nc" id="L385">        public void setSortBy(String sortBy) { this.sortBy = sortBy; }</span>
<span class="nc" id="L386">        public String getSortDirection() { return sortDirection; }</span>
<span class="nc" id="L387">        public void setSortDirection(String sortDirection) { this.sortDirection = sortDirection; }</span>
    }



    private OffsetDateTime convertToOffsetDateTime(LocalDateTime localDateTime) {
<span class="nc bnc" id="L393" title="All 2 branches missed.">        return localDateTime != null ? localDateTime.atOffset(ZoneOffset.UTC) : null;</span>
    }

<span class="nc" id="L396">    public static class WebhookEndpointPage {</span>
        private List&lt;WebhookEndpoint&gt; content;
        private Integer totalElements;
        private Integer totalPages;
        private Integer size;
        private Integer number;
        private Boolean first;
        private Boolean last;

        public static WebhookEndpointPageBuilder builder() {
<span class="nc" id="L406">            return new WebhookEndpointPageBuilder();</span>
        }

<span class="nc" id="L409">        public static class WebhookEndpointPageBuilder {</span>
            private List&lt;WebhookEndpoint&gt; content;
            private Integer totalElements;
            private Integer totalPages;
            private Integer size;
            private Integer number;
            private Boolean first;
            private Boolean last;

            public WebhookEndpointPageBuilder content(List&lt;WebhookEndpoint&gt; content) {
<span class="nc" id="L419">                this.content = content;</span>
<span class="nc" id="L420">                return this;</span>
            }

            public WebhookEndpointPageBuilder totalElements(Integer totalElements) {
<span class="nc" id="L424">                this.totalElements = totalElements;</span>
<span class="nc" id="L425">                return this;</span>
            }

            public WebhookEndpointPageBuilder totalPages(Integer totalPages) {
<span class="nc" id="L429">                this.totalPages = totalPages;</span>
<span class="nc" id="L430">                return this;</span>
            }

            public WebhookEndpointPageBuilder size(Integer size) {
<span class="nc" id="L434">                this.size = size;</span>
<span class="nc" id="L435">                return this;</span>
            }

            public WebhookEndpointPageBuilder number(Integer number) {
<span class="nc" id="L439">                this.number = number;</span>
<span class="nc" id="L440">                return this;</span>
            }

            public WebhookEndpointPageBuilder first(Boolean first) {
<span class="nc" id="L444">                this.first = first;</span>
<span class="nc" id="L445">                return this;</span>
            }

            public WebhookEndpointPageBuilder last(Boolean last) {
<span class="nc" id="L449">                this.last = last;</span>
<span class="nc" id="L450">                return this;</span>
            }

            public WebhookEndpointPage build() {
<span class="nc" id="L454">                WebhookEndpointPage page = new WebhookEndpointPage();</span>
<span class="nc" id="L455">                page.content = this.content;</span>
<span class="nc" id="L456">                page.totalElements = this.totalElements;</span>
<span class="nc" id="L457">                page.totalPages = this.totalPages;</span>
<span class="nc" id="L458">                page.size = this.size;</span>
<span class="nc" id="L459">                page.number = this.number;</span>
<span class="nc" id="L460">                page.first = this.first;</span>
<span class="nc" id="L461">                page.last = this.last;</span>
<span class="nc" id="L462">                return page;</span>
            }
        }

        // Getters
<span class="nc" id="L467">        public List&lt;WebhookEndpoint&gt; getContent() { return content; }</span>
<span class="nc" id="L468">        public Integer getTotalElements() { return totalElements; }</span>
<span class="nc" id="L469">        public Integer getTotalPages() { return totalPages; }</span>
<span class="nc" id="L470">        public Integer getSize() { return size; }</span>
<span class="nc" id="L471">        public Integer getNumber() { return number; }</span>
<span class="nc" id="L472">        public Boolean getFirst() { return first; }</span>
<span class="nc" id="L473">        public Boolean getLast() { return last; }</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>