<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>WebhookEndpointStatistics.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.dto</a> &gt; <span class="el_source">WebhookEndpointStatistics.java</span></div><h1>WebhookEndpointStatistics.java</h1><pre class="source lang-java linenums">/*
 * Copyright (c) 2024 Ascent Business Technology
 * All rights reserved.
 */

package com.ascentbusiness.dms_svc.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.OffsetDateTime;

/**
 * DTO for webhook endpoint statistics
 */
<span class="nc bnc" id="L18" title="All 94 branches missed.">@Data</span>
<span class="nc" id="L19">@Builder</span>
<span class="nc" id="L20">@NoArgsConstructor</span>
<span class="nc" id="L21">@AllArgsConstructor</span>
public class WebhookEndpointStatistics {
    
<span class="nc" id="L24">    private Integer totalEndpoints;</span>
<span class="nc" id="L25">    private Integer activeEndpoints;</span>
<span class="nc" id="L26">    private Integer verifiedEndpoints;</span>
<span class="nc" id="L27">    private Long totalDeliveries;</span>
<span class="nc" id="L28">    private Long successfulDeliveries;</span>
<span class="nc" id="L29">    private Long failedDeliveries;</span>
<span class="nc" id="L30">    private Float averageResponseTime;</span>
<span class="nc" id="L31">    private Float successRate;</span>
<span class="nc" id="L32">    private OffsetDateTime lastDeliveryTime;</span>
<span class="nc" id="L33">    private Integer healthyEndpoints;</span>
<span class="nc" id="L34">    private Integer unhealthyEndpoints;</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>