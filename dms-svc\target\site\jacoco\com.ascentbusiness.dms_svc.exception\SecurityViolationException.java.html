<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>SecurityViolationException.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.exception</a> &gt; <span class="el_source">SecurityViolationException.java</span></div><h1>SecurityViolationException.java</h1><pre class="source lang-java linenums">package com.ascentbusiness.dms_svc.exception;

import com.ascentbusiness.dms_svc.enums.SecurityViolationType;
import com.ascentbusiness.dms_svc.enums.ViolationSeverity;
import lombok.Getter;

/**
 * Exception thrown when a security violation is detected during operations.
 * Contains detailed information about the violation for audit logging.
 */
@Getter
public class SecurityViolationException extends RuntimeException {
    
<span class="nc" id="L14">    private final SecurityViolationType violationType;</span>
<span class="nc" id="L15">    private final ViolationSeverity severity;</span>
<span class="nc" id="L16">    private final String violationDetails;</span>
<span class="nc" id="L17">    private final Long documentId;</span>
<span class="nc" id="L18">    private final String attemptedAction;</span>
    
    public SecurityViolationException(SecurityViolationType violationType, 
                                    ViolationSeverity severity, 
                                    String violationDetails) {
<span class="nc" id="L23">        super(violationDetails);</span>
<span class="nc" id="L24">        this.violationType = violationType;</span>
<span class="nc" id="L25">        this.severity = severity;</span>
<span class="nc" id="L26">        this.violationDetails = violationDetails;</span>
<span class="nc" id="L27">        this.documentId = null;</span>
<span class="nc" id="L28">        this.attemptedAction = null;</span>
<span class="nc" id="L29">    }</span>
    
    public SecurityViolationException(SecurityViolationType violationType, 
                                    ViolationSeverity severity, 
                                    String violationDetails,
                                    Long documentId,
                                    String attemptedAction) {
<span class="nc" id="L36">        super(violationDetails);</span>
<span class="nc" id="L37">        this.violationType = violationType;</span>
<span class="nc" id="L38">        this.severity = severity;</span>
<span class="nc" id="L39">        this.violationDetails = violationDetails;</span>
<span class="nc" id="L40">        this.documentId = documentId;</span>
<span class="nc" id="L41">        this.attemptedAction = attemptedAction;</span>
<span class="nc" id="L42">    }</span>
    
    public SecurityViolationException(SecurityViolationType violationType, 
                                    ViolationSeverity severity, 
                                    String violationDetails,
                                    Throwable cause) {
<span class="nc" id="L48">        super(violationDetails, cause);</span>
<span class="nc" id="L49">        this.violationType = violationType;</span>
<span class="nc" id="L50">        this.severity = severity;</span>
<span class="nc" id="L51">        this.violationDetails = violationDetails;</span>
<span class="nc" id="L52">        this.documentId = null;</span>
<span class="nc" id="L53">        this.attemptedAction = null;</span>
<span class="nc" id="L54">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>