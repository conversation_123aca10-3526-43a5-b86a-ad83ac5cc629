<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>VirusScanException</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.html" class="el_package">com.ascentbusiness.dms_svc.exception</a> &gt; <span class="el_class">VirusScanException</span></div><h1>VirusScanException</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">265 of 265</td><td class="ctr2">0%</td><td class="bar">32 of 32</td><td class="ctr2">0%</td><td class="ctr1">33</td><td class="ctr2">33</td><td class="ctr1">55</td><td class="ctr2">55</td><td class="ctr1">15</td><td class="ctr2">15</td></tr></tfoot><tbody><tr><td id="a0"><a href="VirusScanException.java.html#L211" class="el_method">createErrorDetails(String, VirusScannerType, VirusScanResponse)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="54" alt="54"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="10" alt="10"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f1">6</td><td class="ctr2" id="g1">6</td><td class="ctr1" id="h0">13</td><td class="ctr2" id="i0">13</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a14"><a href="VirusScanException.java.html#L69" class="el_method">VirusScanException(String, VirusScanResponse)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="60" height="10" title="27" alt="27"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="48" height="10" title="4" alt="4"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f2">3</td><td class="ctr2" id="g2">3</td><td class="ctr1" id="h2">5</td><td class="ctr2" id="i2">5</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a11"><a href="VirusScanException.java.html#L124" class="el_method">timeout(String, VirusScannerType, long)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="55" height="10" title="25" alt="25"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d6"/><td class="ctr2" id="e6">n/a</td><td class="ctr1" id="f6">1</td><td class="ctr2" id="g6">1</td><td class="ctr1" id="h5">3</td><td class="ctr2" id="i5">3</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a2"><a href="VirusScanException.java.html#L181" class="el_method">determineErrorCode(VirusScanResponse)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="48" height="10" title="22" alt="22"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="96" height="10" title="8" alt="8"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f0">7</td><td class="ctr2" id="g0">7</td><td class="ctr1" id="h1">9</td><td class="ctr2" id="i1">9</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a7"><a href="VirusScanException.java.html#L85" class="el_method">infected(String, List, VirusScanResponse)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="46" height="10" title="21" alt="21"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d7"/><td class="ctr2" id="e7">n/a</td><td class="ctr1" id="f7">1</td><td class="ctr2" id="g7">1</td><td class="ctr1" id="h6">3</td><td class="ctr2" id="i6">3</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a9"><a href="VirusScanException.java.html#L110" class="el_method">scannerUnavailable(VirusScannerType, String)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="44" height="10" title="20" alt="20"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d8"/><td class="ctr2" id="e8">n/a</td><td class="ctr1" id="f8">1</td><td class="ctr2" id="g8">1</td><td class="ctr1" id="h8">2</td><td class="ctr2" id="i8">2</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a13"><a href="VirusScanException.java.html#L56" class="el_method">VirusScanException(String, Throwable, String, VirusScannerType)</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="42" height="10" title="19" alt="19"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d9"/><td class="ctr2" id="e9">n/a</td><td class="ctr1" id="f9">1</td><td class="ctr2" id="g9">1</td><td class="ctr1" id="h3">5</td><td class="ctr2" id="i3">5</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a12"><a href="VirusScanException.java.html#L41" class="el_method">VirusScanException(String, String, VirusScannerType)</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="40" height="10" title="18" alt="18"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d10"/><td class="ctr2" id="e10">n/a</td><td class="ctr1" id="f10">1</td><td class="ctr2" id="g10">1</td><td class="ctr1" id="h4">5</td><td class="ctr2" id="i4">5</td><td class="ctr1" id="j7">1</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a10"><a href="VirusScanException.java.html#L98" class="el_method">suspicious(String, VirusScanResponse)</a></td><td class="bar" id="b8"><img src="../jacoco-resources/redbar.gif" width="33" height="10" title="15" alt="15"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d11"/><td class="ctr2" id="e11">n/a</td><td class="ctr1" id="f11">1</td><td class="ctr2" id="g11">1</td><td class="ctr1" id="h9">2</td><td class="ctr2" id="i9">2</td><td class="ctr1" id="j8">1</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a1"><a href="VirusScanException.java.html#L244" class="el_method">createErrorDetails(VirusScanResponse)</a></td><td class="bar" id="b9"><img src="../jacoco-resources/redbar.gif" width="33" height="10" title="15" alt="15"/></td><td class="ctr2" id="c9">0%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="48" height="10" title="4" alt="4"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f3">3</td><td class="ctr2" id="g3">3</td><td class="ctr1" id="h7">3</td><td class="ctr2" id="i7">3</td><td class="ctr1" id="j9">1</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a8"><a href="VirusScanException.java.html#L162" class="el_method">isVirusDetected()</a></td><td class="bar" id="b10"><img src="../jacoco-resources/redbar.gif" width="24" height="10" title="11" alt="11"/></td><td class="ctr2" id="c10">0%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="48" height="10" title="4" alt="4"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f4">3</td><td class="ctr2" id="g4">3</td><td class="ctr1" id="h10">1</td><td class="ctr2" id="i10">1</td><td class="ctr1" id="j10">1</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a3"><a href="VirusScanException.java.html#L171" class="el_method">getDetectedThreats()</a></td><td class="bar" id="b11"><img src="../jacoco-resources/redbar.gif" width="20" height="10" title="9" alt="9"/></td><td class="ctr2" id="c11">0%</td><td class="bar" id="d5"><img src="../jacoco-resources/redbar.gif" width="24" height="10" title="2" alt="2"/></td><td class="ctr2" id="e5">0%</td><td class="ctr1" id="f5">2</td><td class="ctr2" id="g5">2</td><td class="ctr1" id="h11">1</td><td class="ctr2" id="i11">1</td><td class="ctr1" id="j11">1</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a4"><a href="VirusScanException.java.html#L135" class="el_method">getFileName()</a></td><td class="bar" id="b12"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="3" alt="3"/></td><td class="ctr2" id="c12">0%</td><td class="bar" id="d12"/><td class="ctr2" id="e12">n/a</td><td class="ctr1" id="f12">1</td><td class="ctr2" id="g12">1</td><td class="ctr1" id="h12">1</td><td class="ctr2" id="i12">1</td><td class="ctr1" id="j12">1</td><td class="ctr2" id="k12">1</td></tr><tr><td id="a5"><a href="VirusScanException.java.html#L144" class="el_method">getScannerType()</a></td><td class="bar" id="b13"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="3" alt="3"/></td><td class="ctr2" id="c13">0%</td><td class="bar" id="d13"/><td class="ctr2" id="e13">n/a</td><td class="ctr1" id="f13">1</td><td class="ctr2" id="g13">1</td><td class="ctr1" id="h13">1</td><td class="ctr2" id="i13">1</td><td class="ctr1" id="j13">1</td><td class="ctr2" id="k13">1</td></tr><tr><td id="a6"><a href="VirusScanException.java.html#L153" class="el_method">getScanResponse()</a></td><td class="bar" id="b14"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="3" alt="3"/></td><td class="ctr2" id="c14">0%</td><td class="bar" id="d14"/><td class="ctr2" id="e14">n/a</td><td class="ctr1" id="f14">1</td><td class="ctr2" id="g14">1</td><td class="ctr1" id="h14">1</td><td class="ctr2" id="i14">1</td><td class="ctr1" id="j14">1</td><td class="ctr2" id="k14">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>