<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>WordConversionResolver.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.resolver</a> &gt; <span class="el_source">WordConversionResolver.java</span></div><h1>WordConversionResolver.java</h1><pre class="source lang-java linenums">package com.ascentbusiness.dms_svc.resolver;

import com.ascentbusiness.dms_svc.dto.WordConversionResult;
import com.ascentbusiness.dms_svc.enums.AuditAction;
import com.ascentbusiness.dms_svc.enums.VirusScannerType;
import com.ascentbusiness.dms_svc.exception.DmsBusinessException;
import com.ascentbusiness.dms_svc.exception.WordConversionException;
import com.ascentbusiness.dms_svc.security.UserContext;
import com.ascentbusiness.dms_svc.service.AuditService;
import com.ascentbusiness.dms_svc.service.WordToPdfConversionService;
import com.ascentbusiness.dms_svc.util.CorrelationIdUtil;
import com.ascentbusiness.dms_svc.util.SecurityValidationUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.graphql.data.method.annotation.Argument;
import org.springframework.graphql.data.method.annotation.MutationMapping;
import org.springframework.stereotype.Controller;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

/**
 * GraphQL resolver for Word to PDF conversion operations.
 * 
 * This resolver provides GraphQL mutations for converting Word documents to PDF format
 * with comprehensive virus scanning, audit logging, and error handling.
 */
@Controller
<span class="nc" id="L30">public class WordConversionResolver {</span>

<span class="nc" id="L32">    private static final Logger logger = LoggerFactory.getLogger(WordConversionResolver.class);</span>

    @Autowired
    private WordToPdfConversionService wordConversionService;

    @Autowired
    private AuditService auditService;

    @Autowired
    private UserContext userContext;

    /**
     * Convert Word document to PDF from multipart file upload.
     * 
     * @param input the conversion input containing file and scanner type
     * @return conversion result with download path
     */
    @MutationMapping
    public WordConversionResult convertWordToPdfMultipart(@Argument Map&lt;String, Object&gt; input) {
<span class="nc" id="L51">        String correlationId = CorrelationIdUtil.getCurrentCorrelationId();</span>
<span class="nc" id="L52">        logger.info(&quot;GraphQL mutation: convertWordToPdfMultipart() [{}]&quot;, correlationId);</span>

        try {
            // Extract parameters from input
<span class="nc" id="L56">            MultipartFile file = (MultipartFile) input.get(&quot;file&quot;);</span>
<span class="nc" id="L57">            String scannerTypeStr = (String) input.get(&quot;scannerType&quot;);</span>
            
            // Get user ID from context
<span class="nc" id="L60">            String userId = userContext.getUserId();</span>
            
            // Parse scanner type
<span class="nc" id="L63">            VirusScannerType scannerType = null;</span>
<span class="nc bnc" id="L64" title="All 4 branches missed.">            if (scannerTypeStr != null &amp;&amp; !scannerTypeStr.trim().isEmpty()) {</span>
                try {
<span class="nc" id="L66">                    scannerType = VirusScannerType.valueOf(scannerTypeStr.toUpperCase());</span>
<span class="nc" id="L67">                } catch (IllegalArgumentException e) {</span>
<span class="nc" id="L68">                    logger.warn(&quot;Invalid scanner type provided: {} [{}]&quot;, scannerTypeStr, correlationId);</span>
<span class="nc" id="L69">                    throw new DmsBusinessException(&quot;Invalid scanner type: &quot; + scannerTypeStr, &quot;INVALID_SCANNER_TYPE&quot;, Map.of(&quot;scannerType&quot;, scannerTypeStr));</span>
<span class="nc" id="L70">                }</span>
            }

            // Validate file
<span class="nc bnc" id="L74" title="All 4 branches missed.">            if (file == null || file.isEmpty()) {</span>
<span class="nc" id="L75">                throw new DmsBusinessException(&quot;File is required for Word to PDF conversion&quot;, &quot;FILE_REQUIRED&quot;, Map.of());</span>
            }

<span class="nc" id="L78">            logger.info(&quot;Converting Word document to PDF via multipart upload: {} (scanner: {}) [{}]&quot;, </span>
<span class="nc" id="L79">                       SecurityValidationUtil.sanitizeForLogging(file.getOriginalFilename()), scannerType, correlationId);</span>

            // Perform conversion
<span class="nc" id="L82">            WordConversionResult result = wordConversionService.convertWordToPdfFromMultipart(file, userId, scannerType);</span>

            // Log successful conversion
<span class="nc" id="L85">            auditService.logAudit(AuditAction.CONVERSION_FILE_DOWNLOADED, null, userId,</span>
<span class="nc" id="L86">                    String.format(&quot;Word to PDF conversion file ready for download: %s -&gt; %s (session: %s)&quot;, </span>
<span class="nc" id="L87">                                SecurityValidationUtil.sanitizeForLogging(result.getOriginalFileName()),</span>
<span class="nc" id="L88">                                SecurityValidationUtil.sanitizeForLogging(result.getConvertedFileName()),</span>
<span class="nc" id="L89">                                result.getSessionId()));</span>

<span class="nc" id="L91">            logger.info(&quot;Word to PDF conversion completed successfully via multipart upload: {} -&gt; {} [{}]&quot;, </span>
<span class="nc" id="L92">                       SecurityValidationUtil.sanitizeForLogging(file.getOriginalFilename()),</span>
<span class="nc" id="L93">                       SecurityValidationUtil.sanitizeForLogging(result.getConvertedFileName()),</span>
                       correlationId);

<span class="nc" id="L96">            return result;</span>

<span class="nc" id="L98">        } catch (WordConversionException e) {</span>
<span class="nc" id="L99">            logger.error(&quot;Word to PDF conversion failed via multipart upload [{}]&quot;, correlationId, e);</span>
<span class="nc" id="L100">            throw e;</span>
<span class="nc" id="L101">        } catch (DmsBusinessException e) {</span>
            // Re-throw DmsBusinessException with original message to preserve specific error details
<span class="nc" id="L103">            logger.error(&quot;Business error during Word to PDF conversion via multipart upload [{}]&quot;, correlationId, e);</span>
<span class="nc" id="L104">            throw e;</span>
<span class="nc" id="L105">        } catch (Exception e) {</span>
<span class="nc" id="L106">            logger.error(&quot;Unexpected error during Word to PDF conversion via multipart upload [{}]&quot;, correlationId, e);</span>
<span class="nc" id="L107">            throw new DmsBusinessException(&quot;Word to PDF conversion failed: &quot; + e.getMessage(), &quot;WORD_CONVERSION_FAILED&quot;, Map.of(&quot;error&quot;, e.getMessage()), e);</span>
        }
    }

    /**
     * Convert Word document to PDF from file path.
     * 
     * @param input the conversion input containing file path and scanner type
     * @return conversion result with download path
     */
    @MutationMapping
    public WordConversionResult convertWordToPdfFromPath(@Argument Map&lt;String, Object&gt; input) {
<span class="nc" id="L119">        String correlationId = CorrelationIdUtil.getCurrentCorrelationId();</span>
<span class="nc" id="L120">        logger.info(&quot;GraphQL mutation: convertWordToPdfFromPath() [{}]&quot;, correlationId);</span>

        try {
            // Extract parameters from input
<span class="nc" id="L124">            String filePath = (String) input.get(&quot;filePath&quot;);</span>
<span class="nc" id="L125">            String scannerTypeStr = (String) input.get(&quot;scannerType&quot;);</span>
            
            // Get user ID from context
<span class="nc" id="L128">            String userId = userContext.getUserId();</span>
            
            // Parse scanner type
<span class="nc" id="L131">            VirusScannerType scannerType = null;</span>
<span class="nc bnc" id="L132" title="All 4 branches missed.">            if (scannerTypeStr != null &amp;&amp; !scannerTypeStr.trim().isEmpty()) {</span>
                try {
<span class="nc" id="L134">                    scannerType = VirusScannerType.valueOf(scannerTypeStr.toUpperCase());</span>
<span class="nc" id="L135">                } catch (IllegalArgumentException e) {</span>
<span class="nc" id="L136">                    logger.warn(&quot;Invalid scanner type provided: {} [{}]&quot;, scannerTypeStr, correlationId);</span>
<span class="nc" id="L137">                    throw new DmsBusinessException(&quot;Invalid scanner type: &quot; + scannerTypeStr, &quot;INVALID_SCANNER_TYPE&quot;, Map.of(&quot;scannerType&quot;, scannerTypeStr));</span>
<span class="nc" id="L138">                }</span>
            }

            // Validate file path
<span class="nc bnc" id="L142" title="All 4 branches missed.">            if (filePath == null || filePath.trim().isEmpty()) {</span>
<span class="nc" id="L143">                throw new DmsBusinessException(&quot;File path is required for Word to PDF conversion&quot;, &quot;FILE_PATH_REQUIRED&quot;, Map.of());</span>
            }

<span class="nc" id="L146">            logger.info(&quot;Converting Word document to PDF via file path: {} (scanner: {}) [{}]&quot;, </span>
<span class="nc" id="L147">                       SecurityValidationUtil.sanitizeForLogging(filePath), scannerType, correlationId);</span>

            // Perform conversion
<span class="nc" id="L150">            WordConversionResult result = wordConversionService.convertWordToPdfFromPath(filePath, userId, scannerType);</span>

            // Log successful conversion
<span class="nc" id="L153">            auditService.logAudit(AuditAction.CONVERSION_FILE_DOWNLOADED, null, userId,</span>
<span class="nc" id="L154">                    String.format(&quot;Word to PDF conversion file ready for download: %s -&gt; %s (session: %s)&quot;, </span>
<span class="nc" id="L155">                                SecurityValidationUtil.sanitizeForLogging(result.getOriginalFileName()),</span>
<span class="nc" id="L156">                                SecurityValidationUtil.sanitizeForLogging(result.getConvertedFileName()),</span>
<span class="nc" id="L157">                                result.getSessionId()));</span>

<span class="nc" id="L159">            logger.info(&quot;Word to PDF conversion completed successfully via file path: {} -&gt; {} [{}]&quot;, </span>
<span class="nc" id="L160">                       SecurityValidationUtil.sanitizeForLogging(filePath),</span>
<span class="nc" id="L161">                       SecurityValidationUtil.sanitizeForLogging(result.getConvertedFileName()),</span>
                       correlationId);

<span class="nc" id="L164">            return result;</span>

<span class="nc" id="L166">        } catch (WordConversionException e) {</span>
<span class="nc" id="L167">            logger.error(&quot;Word to PDF conversion failed via file path [{}]&quot;, correlationId, e);</span>
<span class="nc" id="L168">            throw e;</span>
<span class="nc" id="L169">        } catch (DmsBusinessException e) {</span>
            // Re-throw DmsBusinessException with original message to preserve specific error details
<span class="nc" id="L171">            logger.error(&quot;Business error during Word to PDF conversion via file path [{}]&quot;, correlationId, e);</span>
<span class="nc" id="L172">            throw e;</span>
<span class="nc" id="L173">        } catch (Exception e) {</span>
<span class="nc" id="L174">            logger.error(&quot;Unexpected error during Word to PDF conversion via file path [{}]&quot;, correlationId, e);</span>
            // Handle null correlationId to avoid NullPointerException
<span class="nc bnc" id="L176" title="All 2 branches missed.">            Map&lt;String, Object&gt; errorDetails = correlationId != null ?</span>
<span class="nc" id="L177">                Map.of(&quot;correlationId&quot;, correlationId) :</span>
<span class="nc" id="L178">                Map.of();</span>
<span class="nc" id="L179">            throw new DmsBusinessException(&quot;Word to PDF conversion failed: &quot; + e.getMessage(), &quot;WORD_CONVERSION_ERROR&quot;, errorDetails, e);</span>
        }
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>