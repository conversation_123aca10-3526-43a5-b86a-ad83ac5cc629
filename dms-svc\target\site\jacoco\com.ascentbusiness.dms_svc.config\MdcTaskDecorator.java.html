<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>MdcTaskDecorator.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.config</a> &gt; <span class="el_source">MdcTaskDecorator.java</span></div><h1>MdcTaskDecorator.java</h1><pre class="source lang-java linenums">package com.ascentbusiness.dms_svc.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.core.task.TaskDecorator;
import org.springframework.lang.NonNull;

import java.util.Map;

/**
 * TaskDecorator that propagates MDC (Mapped Diagnostic Context) to async threads.
 * 
 * This decorator ensures that correlation IDs and other MDC properties
 * are available in async operations by copying the MDC from the calling
 * thread to the async execution thread.
 */
<span class="nc" id="L18">public class MdcTaskDecorator implements TaskDecorator {</span>

<span class="nc" id="L20">    private static final Logger logger = LoggerFactory.getLogger(MdcTaskDecorator.class);</span>

    @Override
    @NonNull
    public Runnable decorate(@NonNull Runnable runnable) {
        // Capture the current MDC context from the calling thread
<span class="nc" id="L26">        Map&lt;String, String&gt; contextMap = MDC.getCopyOfContextMap();</span>
        
<span class="nc" id="L28">        return () -&gt; {</span>
            try {
                // Set the captured MDC context in the async thread
<span class="nc bnc" id="L31" title="All 2 branches missed.">                if (contextMap != null) {</span>
<span class="nc" id="L32">                    MDC.setContextMap(contextMap);</span>
<span class="nc" id="L33">                    logger.debug(&quot;MDC context propagated to async thread: {}&quot;, contextMap);</span>
                } else {
<span class="nc" id="L35">                    logger.debug(&quot;No MDC context to propagate to async thread&quot;);</span>
                }
                
                // Execute the original runnable
<span class="nc" id="L39">                runnable.run();</span>
                
            } finally {
                // Always clear MDC after execution to prevent memory leaks
<span class="nc" id="L43">                MDC.clear();</span>
<span class="nc" id="L44">                logger.debug(&quot;MDC context cleared from async thread&quot;);</span>
            }
<span class="nc" id="L46">        };</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>