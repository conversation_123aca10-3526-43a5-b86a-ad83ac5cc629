<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>TracingGraphQLResolver</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.html" class="el_package">com.ascentbusiness.dms_svc.resolver</a> &gt; <span class="el_class">TracingGraphQLResolver</span></div><h1>TracingGraphQLResolver</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">829 of 829</td><td class="ctr2">0%</td><td class="bar">36 of 36</td><td class="ctr2">0%</td><td class="ctr1">35</td><td class="ctr2">35</td><td class="ctr1">158</td><td class="ctr2">158</td><td class="ctr1">17</td><td class="ctr2">17</td></tr></tfoot><tbody><tr><td id="a6"><a href="TracingGraphQLResolver.java.html#L125" class="el_method">lambda$testNestedSpans$4(List)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="224" alt="224"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="12" alt="12"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f0">7</td><td class="ctr2" id="g0">7</td><td class="ctr1" id="h0">35</td><td class="ctr2" id="i0">35</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a9"><a href="TracingGraphQLResolver.java.html#L291" class="el_method">lambda$testTracingPerformance$8(Integer)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="57" height="10" title="108" alt="108"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="40" height="10" title="4" alt="4"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f3">3</td><td class="ctr2" id="g3">3</td><td class="ctr1" id="h2">20</td><td class="ctr2" id="i2">20</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a2"><a href="TracingGraphQLResolver.java.html#L247" class="el_method">lambda$createCustomSpan$6(Map, Integer, String)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="55" height="10" title="103" alt="103"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="100" height="10" title="10" alt="10"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f1">6</td><td class="ctr2" id="g1">6</td><td class="ctr1" id="h1">21</td><td class="ctr2" id="i1">21</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a3"><a href="TracingGraphQLResolver.java.html#L211" class="el_method">lambda$getCurrentTracingContext$5()</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="41" height="10" title="78" alt="78"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="60" height="10" title="6" alt="6"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f2">4</td><td class="ctr2" id="g2">4</td><td class="ctr1" id="h5">12</td><td class="ctr2" id="i5">12</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a10"><a href="TracingGraphQLResolver.java.html#L90" class="el_method">lambda$testTracingWithError$1()</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="36" height="10" title="68" alt="68"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d6"/><td class="ctr2" id="e6">n/a</td><td class="ctr1" id="f6">1</td><td class="ctr2" id="g6">1</td><td class="ctr1" id="h4">14</td><td class="ctr2" id="i4">14</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a7"><a href="TracingGraphQLResolver.java.html#L50" class="el_method">lambda$testTracing$0()</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="34" height="10" title="65" alt="65"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d7"/><td class="ctr2" id="e7">n/a</td><td class="ctr1" id="f7">1</td><td class="ctr2" id="g7">1</td><td class="ctr1" id="h3">17</td><td class="ctr2" id="i3">17</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a4"><a href="TracingGraphQLResolver.java.html#L141" class="el_method">lambda$testNestedSpans$2(String, int)</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="21" height="10" title="40" alt="40"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="20" height="10" title="2" alt="2"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f4">2</td><td class="ctr2" id="g4">2</td><td class="ctr1" id="h6">8</td><td class="ctr2" id="i6">8</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a5"><a href="TracingGraphQLResolver.java.html#L166" class="el_method">lambda$testNestedSpans$3(int)</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="16" height="10" title="30" alt="30"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d8"/><td class="ctr2" id="e8">n/a</td><td class="ctr1" id="f8">1</td><td class="ctr2" id="g8">1</td><td class="ctr1" id="h7">8</td><td class="ctr2" id="i7">8</td><td class="ctr1" id="j7">1</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a12"><a href="TracingGraphQLResolver.java.html#L118" class="el_method">testNestedSpans(Map)</a></td><td class="bar" id="b8"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="26" alt="26"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d5"><img src="../jacoco-resources/redbar.gif" width="20" height="10" title="2" alt="2"/></td><td class="ctr2" id="e5">0%</td><td class="ctr1" id="f5">2</td><td class="ctr2" id="g5">2</td><td class="ctr1" id="h9">4</td><td class="ctr2" id="i9">4</td><td class="ctr1" id="j8">1</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a8"><a href="TracingGraphQLResolver.java.html#L302" class="el_method">lambda$testTracingPerformance$7(int)</a></td><td class="bar" id="b9"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="19" alt="19"/></td><td class="ctr2" id="c9">0%</td><td class="bar" id="d9"/><td class="ctr2" id="e9">n/a</td><td class="ctr1" id="f9">1</td><td class="ctr2" id="g9">1</td><td class="ctr1" id="h8">7</td><td class="ctr2" id="i8">7</td><td class="ctr1" id="j9">1</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a0"><a href="TracingGraphQLResolver.java.html#L244" class="el_method">createCustomSpan(String, Map, Integer)</a></td><td class="bar" id="b10"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="15" alt="15"/></td><td class="ctr2" id="c10">0%</td><td class="bar" id="d10"/><td class="ctr2" id="e10">n/a</td><td class="ctr1" id="f10">1</td><td class="ctr2" id="g10">1</td><td class="ctr1" id="h10">2</td><td class="ctr2" id="i10">2</td><td class="ctr1" id="j10">1</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a14"><a href="TracingGraphQLResolver.java.html#L288" class="el_method">testTracingPerformance(Integer)</a></td><td class="bar" id="b11"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="13" alt="13"/></td><td class="ctr2" id="c11">0%</td><td class="bar" id="d11"/><td class="ctr2" id="e11">n/a</td><td class="ctr1" id="f11">1</td><td class="ctr2" id="g11">1</td><td class="ctr1" id="h11">2</td><td class="ctr2" id="i11">2</td><td class="ctr1" id="j11">1</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a13"><a href="TracingGraphQLResolver.java.html#L47" class="el_method">testTracing()</a></td><td class="bar" id="b12"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="11" alt="11"/></td><td class="ctr2" id="c12">0%</td><td class="bar" id="d12"/><td class="ctr2" id="e12">n/a</td><td class="ctr1" id="f12">1</td><td class="ctr2" id="g12">1</td><td class="ctr1" id="h12">2</td><td class="ctr2" id="i12">2</td><td class="ctr1" id="j12">1</td><td class="ctr2" id="k12">1</td></tr><tr><td id="a15"><a href="TracingGraphQLResolver.java.html#L85" class="el_method">testTracingWithError()</a></td><td class="bar" id="b13"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="11" alt="11"/></td><td class="ctr2" id="c13">0%</td><td class="bar" id="d13"/><td class="ctr2" id="e13">n/a</td><td class="ctr1" id="f13">1</td><td class="ctr2" id="g13">1</td><td class="ctr1" id="h13">2</td><td class="ctr2" id="i13">2</td><td class="ctr1" id="j13">1</td><td class="ctr2" id="k13">1</td></tr><tr><td id="a1"><a href="TracingGraphQLResolver.java.html#L208" class="el_method">getCurrentTracingContext()</a></td><td class="bar" id="b14"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="11" alt="11"/></td><td class="ctr2" id="c14">0%</td><td class="bar" id="d14"/><td class="ctr2" id="e14">n/a</td><td class="ctr1" id="f14">1</td><td class="ctr2" id="g14">1</td><td class="ctr1" id="h14">2</td><td class="ctr2" id="i14">2</td><td class="ctr1" id="j14">1</td><td class="ctr2" id="k14">1</td></tr><tr><td id="a11"><a href="TracingGraphQLResolver.java.html#L30" class="el_method">static {...}</a></td><td class="bar" id="b15"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="4" alt="4"/></td><td class="ctr2" id="c15">0%</td><td class="bar" id="d15"/><td class="ctr2" id="e15">n/a</td><td class="ctr1" id="f15">1</td><td class="ctr2" id="g15">1</td><td class="ctr1" id="h15">1</td><td class="ctr2" id="i15">1</td><td class="ctr1" id="j15">1</td><td class="ctr2" id="k15">1</td></tr><tr><td id="a16"><a href="TracingGraphQLResolver.java.html#L31" class="el_method">TracingGraphQLResolver()</a></td><td class="bar" id="b16"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c16">0%</td><td class="bar" id="d16"/><td class="ctr2" id="e16">n/a</td><td class="ctr1" id="f16">1</td><td class="ctr2" id="g16">1</td><td class="ctr1" id="h16">1</td><td class="ctr2" id="i16">1</td><td class="ctr1" id="j16">1</td><td class="ctr2" id="k16">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>