<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>MarkdownConversionException.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.exception</a> &gt; <span class="el_source">MarkdownConversionException.java</span></div><h1>MarkdownConversionException.java</h1><pre class="source lang-java linenums">package com.ascentbusiness.dms_svc.exception;

/**
 * Exception thrown when Markdown to Word conversion operations fail.
 */
public class MarkdownConversionException extends RuntimeException {
    
    private final String sessionId;
    private final String fileName;
    
    public MarkdownConversionException(String message, String sessionId) {
<span class="nc" id="L12">        super(message);</span>
<span class="nc" id="L13">        this.sessionId = sessionId;</span>
<span class="nc" id="L14">        this.fileName = null;</span>
<span class="nc" id="L15">    }</span>
    
    public MarkdownConversionException(String message, Throwable cause, String sessionId) {
<span class="nc" id="L18">        super(message, cause);</span>
<span class="nc" id="L19">        this.sessionId = sessionId;</span>
<span class="nc" id="L20">        this.fileName = null;</span>
<span class="nc" id="L21">    }</span>
    
    public MarkdownConversionException(String message, Throwable cause, String sessionId, String fileName) {
<span class="nc" id="L24">        super(message, cause);</span>
<span class="nc" id="L25">        this.sessionId = sessionId;</span>
<span class="nc" id="L26">        this.fileName = fileName;</span>
<span class="nc" id="L27">    }</span>
    
    public String getSessionId() {
<span class="nc" id="L30">        return sessionId;</span>
    }
    
    public String getFileName() {
<span class="nc" id="L34">        return fileName;</span>
    }
    
    @Override
    public String getMessage() {
<span class="nc" id="L39">        StringBuilder sb = new StringBuilder(super.getMessage());</span>
<span class="nc bnc" id="L40" title="All 2 branches missed.">        if (sessionId != null) {</span>
<span class="nc" id="L41">            sb.append(&quot; (session: &quot;).append(sessionId).append(&quot;)&quot;);</span>
        }
<span class="nc bnc" id="L43" title="All 2 branches missed.">        if (fileName != null) {</span>
<span class="nc" id="L44">            sb.append(&quot; (file: &quot;).append(fileName).append(&quot;)&quot;);</span>
        }
<span class="nc" id="L46">        return sb.toString();</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>