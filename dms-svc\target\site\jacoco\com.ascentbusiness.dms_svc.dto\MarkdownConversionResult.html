<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>MarkdownConversionResult</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.html" class="el_package">com.ascentbusiness.dms_svc.dto</a> &gt; <span class="el_class">MarkdownConversionResult</span></div><h1>MarkdownConversionResult</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">559 of 559</td><td class="ctr2">0%</td><td class="bar">90 of 90</td><td class="ctr2">0%</td><td class="ctr1">78</td><td class="ctr2">78</td><td class="ctr1">17</td><td class="ctr2">17</td><td class="ctr1">33</td><td class="ctr2">33</td></tr></tfoot><tbody><tr><td id="a2"><a href="MarkdownConversionResult.java.html#L11" class="el_method">equals(Object)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="204" alt="204"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="68" alt="68"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f0">35</td><td class="ctr2" id="g0">35</td><td class="ctr1" id="h0">1</td><td class="ctr2" id="i0">1</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a14"><a href="MarkdownConversionResult.java.html#L11" class="el_method">hashCode()</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="107" height="10" title="182" alt="182"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="38" height="10" title="22" alt="22"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f1">12</td><td class="ctr2" id="g1">12</td><td class="ctr1" id="h1">1</td><td class="ctr2" id="i1">1</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a18"><a href="MarkdownConversionResult.java.html#L14" class="el_method">MarkdownConversionResult(String, String, String, String, long, VirusScanResponse, boolean, String, String, OffsetDateTime, long, boolean, String)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="24" height="10" title="42" alt="42"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d2"/><td class="ctr2" id="e2">n/a</td><td class="ctr1" id="f2">1</td><td class="ctr2" id="g2">1</td><td class="ctr1" id="h2">1</td><td class="ctr2" id="i2">1</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a32"><a href="MarkdownConversionResult.java.html#L11" class="el_method">toString()</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="17" height="10" title="30" alt="30"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d3"/><td class="ctr2" id="e3">n/a</td><td class="ctr1" id="f3">1</td><td class="ctr2" id="g3">1</td><td class="ctr1" id="h3">1</td><td class="ctr2" id="i3">1</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a0"><a href="MarkdownConversionResult.java.html#L12" class="el_method">builder()</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="4" alt="4"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d4"/><td class="ctr2" id="e4">n/a</td><td class="ctr1" id="f4">1</td><td class="ctr2" id="g4">1</td><td class="ctr1" id="h4">1</td><td class="ctr2" id="i4">1</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a28"><a href="MarkdownConversionResult.java.html#L11" class="el_method">setSessionId(String)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="4" alt="4"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d5"/><td class="ctr2" id="e5">n/a</td><td class="ctr1" id="f5">1</td><td class="ctr2" id="g5">1</td><td class="ctr1" id="h5">1</td><td class="ctr2" id="i5">1</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a26"><a href="MarkdownConversionResult.java.html#L11" class="el_method">setOriginalFileName(String)</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="4" alt="4"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d6"/><td class="ctr2" id="e6">n/a</td><td class="ctr1" id="f6">1</td><td class="ctr2" id="g6">1</td><td class="ctr1" id="h6">1</td><td class="ctr2" id="i6">1</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a21"><a href="MarkdownConversionResult.java.html#L11" class="el_method">setConvertedFileName(String)</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="4" alt="4"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d7"/><td class="ctr2" id="e7">n/a</td><td class="ctr1" id="f7">1</td><td class="ctr2" id="g7">1</td><td class="ctr1" id="h7">1</td><td class="ctr2" id="i7">1</td><td class="ctr1" id="j7">1</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a22"><a href="MarkdownConversionResult.java.html#L11" class="el_method">setDownloadPath(String)</a></td><td class="bar" id="b8"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="4" alt="4"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d8"/><td class="ctr2" id="e8">n/a</td><td class="ctr1" id="f8">1</td><td class="ctr2" id="g8">1</td><td class="ctr1" id="h8">1</td><td class="ctr2" id="i8">1</td><td class="ctr1" id="j8">1</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a24"><a href="MarkdownConversionResult.java.html#L11" class="el_method">setFileSize(long)</a></td><td class="bar" id="b9"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="4" alt="4"/></td><td class="ctr2" id="c9">0%</td><td class="bar" id="d9"/><td class="ctr2" id="e9">n/a</td><td class="ctr1" id="f9">1</td><td class="ctr2" id="g9">1</td><td class="ctr1" id="h9">1</td><td class="ctr2" id="i9">1</td><td class="ctr1" id="j9">1</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a31"><a href="MarkdownConversionResult.java.html#L11" class="el_method">setVirusScanResponse(VirusScanResponse)</a></td><td class="bar" id="b10"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="4" alt="4"/></td><td class="ctr2" id="c10">0%</td><td class="bar" id="d10"/><td class="ctr2" id="e10">n/a</td><td class="ctr1" id="f10">1</td><td class="ctr2" id="g10">1</td><td class="ctr1" id="h10">1</td><td class="ctr2" id="i10">1</td><td class="ctr1" id="j10">1</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a29"><a href="MarkdownConversionResult.java.html#L11" class="el_method">setSuccess(boolean)</a></td><td class="bar" id="b11"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="4" alt="4"/></td><td class="ctr2" id="c11">0%</td><td class="bar" id="d11"/><td class="ctr2" id="e11">n/a</td><td class="ctr1" id="f11">1</td><td class="ctr2" id="g11">1</td><td class="ctr1" id="h11">1</td><td class="ctr2" id="i11">1</td><td class="ctr1" id="j11">1</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a25"><a href="MarkdownConversionResult.java.html#L11" class="el_method">setMessage(String)</a></td><td class="bar" id="b12"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="4" alt="4"/></td><td class="ctr2" id="c12">0%</td><td class="bar" id="d12"/><td class="ctr2" id="e12">n/a</td><td class="ctr1" id="f12">1</td><td class="ctr2" id="g12">1</td><td class="ctr1" id="h12">1</td><td class="ctr2" id="i12">1</td><td class="ctr1" id="j12">1</td><td class="ctr2" id="k12">1</td></tr><tr><td id="a23"><a href="MarkdownConversionResult.java.html#L11" class="el_method">setErrorDetails(String)</a></td><td class="bar" id="b13"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="4" alt="4"/></td><td class="ctr2" id="c13">0%</td><td class="bar" id="d13"/><td class="ctr2" id="e13">n/a</td><td class="ctr1" id="f13">1</td><td class="ctr2" id="g13">1</td><td class="ctr1" id="h13">1</td><td class="ctr2" id="i13">1</td><td class="ctr1" id="j13">1</td><td class="ctr2" id="k13">1</td></tr><tr><td id="a19"><a href="MarkdownConversionResult.java.html#L11" class="el_method">setCompletedAt(OffsetDateTime)</a></td><td class="bar" id="b14"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="4" alt="4"/></td><td class="ctr2" id="c14">0%</td><td class="bar" id="d14"/><td class="ctr2" id="e14">n/a</td><td class="ctr1" id="f14">1</td><td class="ctr2" id="g14">1</td><td class="ctr1" id="h14">1</td><td class="ctr2" id="i14">1</td><td class="ctr1" id="j14">1</td><td class="ctr2" id="k14">1</td></tr><tr><td id="a27"><a href="MarkdownConversionResult.java.html#L11" class="el_method">setProcessingTimeMs(long)</a></td><td class="bar" id="b15"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="4" alt="4"/></td><td class="ctr2" id="c15">0%</td><td class="bar" id="d15"/><td class="ctr2" id="e15">n/a</td><td class="ctr1" id="f15">1</td><td class="ctr2" id="g15">1</td><td class="ctr1" id="h15">1</td><td class="ctr2" id="i15">1</td><td class="ctr1" id="j15">1</td><td class="ctr2" id="k15">1</td></tr><tr><td id="a30"><a href="MarkdownConversionResult.java.html#L11" class="el_method">setUsedPandoc(boolean)</a></td><td class="bar" id="b16"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="4" alt="4"/></td><td class="ctr2" id="c16">0%</td><td class="bar" id="d16"/><td class="ctr2" id="e16">n/a</td><td class="ctr1" id="f16">1</td><td class="ctr2" id="g16">1</td><td class="ctr1" id="h16">1</td><td class="ctr2" id="i16">1</td><td class="ctr1" id="j16">1</td><td class="ctr2" id="k16">1</td></tr><tr><td id="a20"><a href="MarkdownConversionResult.java.html#L11" class="el_method">setConversionMethod(String)</a></td><td class="bar" id="b17"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="4" alt="4"/></td><td class="ctr2" id="c17">0%</td><td class="bar" id="d17"/><td class="ctr2" id="e17">n/a</td><td class="ctr1" id="f17">1</td><td class="ctr2" id="g17">1</td><td class="ctr1" id="h17">1</td><td class="ctr2" id="i17">1</td><td class="ctr1" id="j17">1</td><td class="ctr2" id="k17">1</td></tr><tr><td id="a12"><a href="MarkdownConversionResult.java.html#L20" class="el_method">getSessionId()</a></td><td class="bar" id="b18"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c18">0%</td><td class="bar" id="d18"/><td class="ctr2" id="e18">n/a</td><td class="ctr1" id="f18">1</td><td class="ctr2" id="g18">1</td><td class="ctr1" id="h18">1</td><td class="ctr2" id="i18">1</td><td class="ctr1" id="j18">1</td><td class="ctr2" id="k18">1</td></tr><tr><td id="a10"><a href="MarkdownConversionResult.java.html#L25" class="el_method">getOriginalFileName()</a></td><td class="bar" id="b19"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c19">0%</td><td class="bar" id="d19"/><td class="ctr2" id="e19">n/a</td><td class="ctr1" id="f19">1</td><td class="ctr2" id="g19">1</td><td class="ctr1" id="h19">1</td><td class="ctr2" id="i19">1</td><td class="ctr1" id="j19">1</td><td class="ctr2" id="k19">1</td></tr><tr><td id="a5"><a href="MarkdownConversionResult.java.html#L30" class="el_method">getConvertedFileName()</a></td><td class="bar" id="b20"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c20">0%</td><td class="bar" id="d20"/><td class="ctr2" id="e20">n/a</td><td class="ctr1" id="f20">1</td><td class="ctr2" id="g20">1</td><td class="ctr1" id="h20">1</td><td class="ctr2" id="i20">1</td><td class="ctr1" id="j20">1</td><td class="ctr2" id="k20">1</td></tr><tr><td id="a6"><a href="MarkdownConversionResult.java.html#L35" class="el_method">getDownloadPath()</a></td><td class="bar" id="b21"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c21">0%</td><td class="bar" id="d21"/><td class="ctr2" id="e21">n/a</td><td class="ctr1" id="f21">1</td><td class="ctr2" id="g21">1</td><td class="ctr1" id="h21">1</td><td class="ctr2" id="i21">1</td><td class="ctr1" id="j21">1</td><td class="ctr2" id="k21">1</td></tr><tr><td id="a8"><a href="MarkdownConversionResult.java.html#L40" class="el_method">getFileSize()</a></td><td class="bar" id="b22"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c22">0%</td><td class="bar" id="d22"/><td class="ctr2" id="e22">n/a</td><td class="ctr1" id="f22">1</td><td class="ctr2" id="g22">1</td><td class="ctr1" id="h22">1</td><td class="ctr2" id="i22">1</td><td class="ctr1" id="j22">1</td><td class="ctr2" id="k22">1</td></tr><tr><td id="a13"><a href="MarkdownConversionResult.java.html#L45" class="el_method">getVirusScanResponse()</a></td><td class="bar" id="b23"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c23">0%</td><td class="bar" id="d23"/><td class="ctr2" id="e23">n/a</td><td class="ctr1" id="f23">1</td><td class="ctr2" id="g23">1</td><td class="ctr1" id="h23">1</td><td class="ctr2" id="i23">1</td><td class="ctr1" id="j23">1</td><td class="ctr2" id="k23">1</td></tr><tr><td id="a15"><a href="MarkdownConversionResult.java.html#L50" class="el_method">isSuccess()</a></td><td class="bar" id="b24"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c24">0%</td><td class="bar" id="d24"/><td class="ctr2" id="e24">n/a</td><td class="ctr1" id="f24">1</td><td class="ctr2" id="g24">1</td><td class="ctr1" id="h24">1</td><td class="ctr2" id="i24">1</td><td class="ctr1" id="j24">1</td><td class="ctr2" id="k24">1</td></tr><tr><td id="a9"><a href="MarkdownConversionResult.java.html#L55" class="el_method">getMessage()</a></td><td class="bar" id="b25"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c25">0%</td><td class="bar" id="d25"/><td class="ctr2" id="e25">n/a</td><td class="ctr1" id="f25">1</td><td class="ctr2" id="g25">1</td><td class="ctr1" id="h25">1</td><td class="ctr2" id="i25">1</td><td class="ctr1" id="j25">1</td><td class="ctr2" id="k25">1</td></tr><tr><td id="a7"><a href="MarkdownConversionResult.java.html#L60" class="el_method">getErrorDetails()</a></td><td class="bar" id="b26"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c26">0%</td><td class="bar" id="d26"/><td class="ctr2" id="e26">n/a</td><td class="ctr1" id="f26">1</td><td class="ctr2" id="g26">1</td><td class="ctr1" id="h26">1</td><td class="ctr2" id="i26">1</td><td class="ctr1" id="j26">1</td><td class="ctr2" id="k26">1</td></tr><tr><td id="a3"><a href="MarkdownConversionResult.java.html#L65" class="el_method">getCompletedAt()</a></td><td class="bar" id="b27"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c27">0%</td><td class="bar" id="d27"/><td class="ctr2" id="e27">n/a</td><td class="ctr1" id="f27">1</td><td class="ctr2" id="g27">1</td><td class="ctr1" id="h27">1</td><td class="ctr2" id="i27">1</td><td class="ctr1" id="j27">1</td><td class="ctr2" id="k27">1</td></tr><tr><td id="a11"><a href="MarkdownConversionResult.java.html#L70" class="el_method">getProcessingTimeMs()</a></td><td class="bar" id="b28"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c28">0%</td><td class="bar" id="d28"/><td class="ctr2" id="e28">n/a</td><td class="ctr1" id="f28">1</td><td class="ctr2" id="g28">1</td><td class="ctr1" id="h28">1</td><td class="ctr2" id="i28">1</td><td class="ctr1" id="j28">1</td><td class="ctr2" id="k28">1</td></tr><tr><td id="a16"><a href="MarkdownConversionResult.java.html#L75" class="el_method">isUsedPandoc()</a></td><td class="bar" id="b29"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c29">0%</td><td class="bar" id="d29"/><td class="ctr2" id="e29">n/a</td><td class="ctr1" id="f29">1</td><td class="ctr2" id="g29">1</td><td class="ctr1" id="h29">1</td><td class="ctr2" id="i29">1</td><td class="ctr1" id="j29">1</td><td class="ctr2" id="k29">1</td></tr><tr><td id="a4"><a href="MarkdownConversionResult.java.html#L80" class="el_method">getConversionMethod()</a></td><td class="bar" id="b30"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c30">0%</td><td class="bar" id="d30"/><td class="ctr2" id="e30">n/a</td><td class="ctr1" id="f30">1</td><td class="ctr2" id="g30">1</td><td class="ctr1" id="h30">1</td><td class="ctr2" id="i30">1</td><td class="ctr1" id="j30">1</td><td class="ctr2" id="k30">1</td></tr><tr><td id="a1"><a href="MarkdownConversionResult.java.html#L11" class="el_method">canEqual(Object)</a></td><td class="bar" id="b31"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c31">0%</td><td class="bar" id="d31"/><td class="ctr2" id="e31">n/a</td><td class="ctr1" id="f31">1</td><td class="ctr2" id="g31">1</td><td class="ctr1" id="h31">1</td><td class="ctr2" id="i31">1</td><td class="ctr1" id="j31">1</td><td class="ctr2" id="k31">1</td></tr><tr><td id="a17"><a href="MarkdownConversionResult.java.html#L13" class="el_method">MarkdownConversionResult()</a></td><td class="bar" id="b32"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c32">0%</td><td class="bar" id="d32"/><td class="ctr2" id="e32">n/a</td><td class="ctr1" id="f32">1</td><td class="ctr2" id="g32">1</td><td class="ctr1" id="h32">1</td><td class="ctr2" id="i32">1</td><td class="ctr1" id="j32">1</td><td class="ctr2" id="k32">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>