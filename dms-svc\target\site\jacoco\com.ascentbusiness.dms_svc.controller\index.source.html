<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>com.ascentbusiness.dms_svc.controller</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.html" class="el_class">Classes</a><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <span class="el_package">com.ascentbusiness.dms_svc.controller</span></div><h1>com.ascentbusiness.dms_svc.controller</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">1,740 of 1,740</td><td class="ctr2">0%</td><td class="bar">40 of 40</td><td class="ctr2">0%</td><td class="ctr1">117</td><td class="ctr2">117</td><td class="ctr1">412</td><td class="ctr2">412</td><td class="ctr1">97</td><td class="ctr2">97</td><td class="ctr1">9</td><td class="ctr2">9</td></tr></tfoot><tbody><tr><td id="a2"><a href="GraphQLMetricsController.java.html" class="el_source">GraphQLMetricsController.java</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="847" alt="847"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="16" alt="16"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f0">73</td><td class="ctr2" id="g0">73</td><td class="ctr1" id="h0">196</td><td class="ctr2" id="i0">196</td><td class="ctr1" id="j0">65</td><td class="ctr2" id="k0">65</td><td class="ctr1" id="l0">5</td><td class="ctr2" id="m0">5</td></tr><tr><td id="a1"><a href="DocumentDownloadController.java.html" class="el_source">DocumentDownloadController.java</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="86" height="10" title="609" alt="609"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="105" height="10" title="14" alt="14"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f2">18</td><td class="ctr2" id="g2">18</td><td class="ctr1" id="h1">134</td><td class="ctr2" id="i1">134</td><td class="ctr1" id="j2">11</td><td class="ctr2" id="k2">11</td><td class="ctr1" id="l2">1</td><td class="ctr2" id="m2">1</td></tr><tr><td id="a0"><a href="AuthController.java.html" class="el_source">AuthController.java</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="40" height="10" title="284" alt="284"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="75" height="10" title="10" alt="10"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f1">26</td><td class="ctr2" id="g1">26</td><td class="ctr1" id="h2">82</td><td class="ctr2" id="i2">82</td><td class="ctr1" id="j1">21</td><td class="ctr2" id="k1">21</td><td class="ctr1" id="l1">3</td><td class="ctr2" id="m1">3</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>