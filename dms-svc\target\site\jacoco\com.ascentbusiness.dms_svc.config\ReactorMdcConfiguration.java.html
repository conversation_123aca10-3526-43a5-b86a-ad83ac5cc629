<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>ReactorMdcConfiguration.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.config</a> &gt; <span class="el_source">ReactorMdcConfiguration.java</span></div><h1>ReactorMdcConfiguration.java</h1><pre class="source lang-java linenums">package com.ascentbusiness.dms_svc.config;

import org.slf4j.MDC;
import org.springframework.context.annotation.Configuration;
import reactor.core.publisher.Hooks;
import reactor.core.publisher.Operators;
import reactor.util.context.Context;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import java.util.Map;

/**
 * Configuration to enable automatic MDC (Mapped Diagnostic Context) propagation
 * in reactive streams. This ensures that correlation IDs and other MDC values
 * are properly propagated throughout the entire reactive chain.
 */
@Configuration
<span class="nc" id="L19">public class ReactorMdcConfiguration {</span>

    private static final String MDC_CONTEXT_KEY = &quot;MDC_CONTEXT&quot;;

    @PostConstruct
    public void enableMdcPropagation() {
        // Enable automatic MDC propagation for all reactive operations
<span class="nc" id="L26">        Hooks.onEachOperator(MDC_CONTEXT_KEY, Operators.lift((scannable, coreSubscriber) -&gt; {</span>
<span class="nc" id="L27">            return new MdcPropagatingSubscriber&lt;&gt;(coreSubscriber);</span>
        }));
<span class="nc" id="L29">    }</span>

    @PreDestroy
    public void disableMdcPropagation() {
        // Clean up the hook when the application shuts down
<span class="nc" id="L34">        Hooks.resetOnEachOperator(MDC_CONTEXT_KEY);</span>
<span class="nc" id="L35">    }</span>

    /**
     * Custom subscriber that propagates MDC context throughout the reactive chain
     */
    private static class MdcPropagatingSubscriber&lt;T&gt; implements reactor.core.CoreSubscriber&lt;T&gt; {
        private final reactor.core.CoreSubscriber&lt;T&gt; actual;
        private final Map&lt;String, String&gt; mdcContext;

<span class="nc" id="L44">        public MdcPropagatingSubscriber(reactor.core.CoreSubscriber&lt;T&gt; actual) {</span>
<span class="nc" id="L45">            this.actual = actual;</span>
            // Capture the current MDC context
<span class="nc" id="L47">            this.mdcContext = MDC.getCopyOfContextMap();</span>
<span class="nc" id="L48">        }</span>

        @Override
        public void onSubscribe(org.reactivestreams.Subscription s) {
            // Set MDC context when subscription starts
<span class="nc bnc" id="L53" title="All 2 branches missed.">            if (mdcContext != null) {</span>
<span class="nc" id="L54">                MDC.setContextMap(mdcContext);</span>
            }
<span class="nc" id="L56">            actual.onSubscribe(s);</span>
<span class="nc" id="L57">        }</span>

        @Override
        public void onNext(T t) {
            // Set MDC context for each emission
<span class="nc bnc" id="L62" title="All 2 branches missed.">            if (mdcContext != null) {</span>
<span class="nc" id="L63">                MDC.setContextMap(mdcContext);</span>
            }
<span class="nc" id="L65">            actual.onNext(t);</span>
<span class="nc" id="L66">        }</span>

        @Override
        public void onError(Throwable t) {
            // Set MDC context for error handling
<span class="nc bnc" id="L71" title="All 2 branches missed.">            if (mdcContext != null) {</span>
<span class="nc" id="L72">                MDC.setContextMap(mdcContext);</span>
            }
<span class="nc" id="L74">            actual.onError(t);</span>
<span class="nc" id="L75">        }</span>

        @Override
        public void onComplete() {
            // Set MDC context for completion
<span class="nc bnc" id="L80" title="All 2 branches missed.">            if (mdcContext != null) {</span>
<span class="nc" id="L81">                MDC.setContextMap(mdcContext);</span>
            }
<span class="nc" id="L83">            actual.onComplete();</span>
<span class="nc" id="L84">        }</span>

        @Override
        public Context currentContext() {
            // Store MDC context in the reactive context for downstream access
<span class="nc" id="L89">            Context context = actual.currentContext();</span>
<span class="nc bnc" id="L90" title="All 2 branches missed.">            if (mdcContext != null) {</span>
<span class="nc" id="L91">                context = context.put(MDC_CONTEXT_KEY, mdcContext);</span>
            }
<span class="nc" id="L93">            return context;</span>
        }
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>