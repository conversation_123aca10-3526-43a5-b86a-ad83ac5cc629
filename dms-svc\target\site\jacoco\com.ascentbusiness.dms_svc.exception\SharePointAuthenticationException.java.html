<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>SharePointAuthenticationException.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.exception</a> &gt; <span class="el_source">SharePointAuthenticationException.java</span></div><h1>SharePointAuthenticationException.java</h1><pre class="source lang-java linenums">package com.ascentbusiness.dms_svc.exception;

/**
 * Exception thrown when SharePoint authentication fails
 */
public class SharePointAuthenticationException extends SharePointException {
    
    public SharePointAuthenticationException(String message) {
<span class="nc" id="L9">        super(message);</span>
<span class="nc" id="L10">    }</span>
    
    public SharePointAuthenticationException(String message, Throwable cause) {
<span class="nc" id="L13">        super(message, cause);</span>
<span class="nc" id="L14">    }</span>
    
    public SharePointAuthenticationException(Throwable cause) {
<span class="nc" id="L17">        super(cause);</span>
<span class="nc" id="L18">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>