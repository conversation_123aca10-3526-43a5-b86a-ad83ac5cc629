/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

body {
  margin: 0px;
  padding: 0px;
}
table {
  padding:0px;
  width: 100%;
  margin-left: -2px;
  margin-right: -2px;
}
acronym {
  cursor: help;
  border-bottom: 1px dotted #feb;
}
table.bodyTable th, table.bodyTable td {
  padding: 2px 4px 2px 4px;
  vertical-align: top;
}
div.clear {
  clear:both;
  visibility: hidden;
}
div.clear hr {
  display: none;
}
#bannerLeft, #bannerRight {
  font-size: xx-large;
  font-weight: bold;
}
#bannerLeft img, #bannerRight img {
  margin: 0px;
}
.xleft, #bannerLeft img {
  float:left;
}
.xright, #bannerRight {
  float:right;
}
#banner {
  padding: 0px;
}
#breadcrumbs {
  padding: 3px 10px 3px 10px;
}
#leftColumn {
 width: 170px;
 float:left;
 overflow: auto;
}
#bodyColumn {
  margin-right: 1.5em;
  margin-left: 197px;
}
#legend {
  padding: 8px 0 8px 0;
}
#navcolumn {
  padding: 8px 4px 0 8px;
}
#navcolumn h5 {
  margin: 0;
  padding: 0;
  font-size: small;
}
#navcolumn ul {
  margin: 0;
  padding: 0;
  font-size: small;
}
#navcolumn li {
  list-style-type: none;
  background-image: none;
  background-repeat: no-repeat;
  background-position: 0 0.4em;
  padding-left: 16px;
  list-style-position: outside;
  line-height: 1.2em;
  font-size: smaller;
}
#navcolumn li.expanded {
  background-image: url(../images/expanded.gif);
}
#navcolumn li.collapsed {
  background-image: url(../images/collapsed.gif);
}
#navcolumn li.none {
  text-indent: -1em;
  margin-left: 1em;
}
#poweredBy {
  text-align: center;
}
#navcolumn img {
  margin-top: 10px;
  margin-bottom: 3px;
}
#poweredBy img {
  display:block;
  margin: 20px 0 20px 17px;
}
#search img {
    margin: 0px;
    display: block;
}
#search #q, #search #btnG {
    border: 1px solid #999;
    margin-bottom:10px;
}
#search form {
    margin: 0px;
}
#lastPublished {
  font-size: x-small;
}
.navSection {
  margin-bottom: 2px;
  padding: 8px;
}
.navSectionHead {
  font-weight: bold;
  font-size: x-small;
}
.section {
  padding: 4px;
}
#footer {
  padding: 3px 10px 3px 10px;
  font-size: x-small;
}
#breadcrumbs {
  font-size: x-small;
  margin: 0pt;
}
.source {
  padding: 12px;
  margin: 1em 7px 1em 7px;
}
.source pre {
  margin: 0px;
  padding: 0px;
}
#navcolumn img.imageLink, .imageLink {
  padding-left: 0px;
  padding-bottom: 0px;
  padding-top: 0px;
  padding-right: 2px;
  border: 0px;
  margin: 0px;
}
