<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>DuplicateFileException.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.exception</a> &gt; <span class="el_source">DuplicateFileException.java</span></div><h1>DuplicateFileException.java</h1><pre class="source lang-java linenums">package com.ascentbusiness.dms_svc.exception;

import java.util.Map;

/**
 * Exception thrown when attempting to upload a duplicate file without override permission.
 * This exception provides detailed information about the existing file to help users
 * understand the conflict and make informed decisions.
 * Extends DmsBusinessException with specific error handling for duplicate file scenarios.
 */
public class DuplicateFileException extends DmsBusinessException {

    public DuplicateFileException(String message) {
<span class="nc" id="L14">        super(message, &quot;DUPLICATE_FILE&quot;);</span>
<span class="nc" id="L15">    }</span>

    public DuplicateFileException(String message, Throwable cause) {
<span class="nc" id="L18">        super(message, &quot;DUPLICATE_FILE&quot;, Map.of(), cause);</span>
<span class="nc" id="L19">    }</span>

    public DuplicateFileException(String fileName, String existingDocumentId) {
<span class="nc" id="L22">        super(String.format(&quot;File '%s' already exists. Document ID: %s. Use overrideFile=true to create a new version.&quot;,</span>
                           fileName, existingDocumentId),
              &quot;DUPLICATE_FILE&quot;,
<span class="nc" id="L25">              Map.of(&quot;fileName&quot;, fileName, &quot;existingDocumentId&quot;, existingDocumentId));</span>
<span class="nc" id="L26">    }</span>

    public DuplicateFileException(String fileName, String existingDocumentId, String uploadDate) {
<span class="nc" id="L29">        super(String.format(&quot;File '%s' with identical content already exists (uploaded on %s). Set overrideFile=true if you want to create a new version.&quot;,</span>
                           fileName, uploadDate),
              &quot;DUPLICATE_FILE&quot;,
<span class="nc" id="L32">              Map.of(&quot;fileName&quot;, fileName, &quot;existingDocumentId&quot;, existingDocumentId, &quot;uploadDate&quot;, uploadDate));</span>
<span class="nc" id="L33">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>