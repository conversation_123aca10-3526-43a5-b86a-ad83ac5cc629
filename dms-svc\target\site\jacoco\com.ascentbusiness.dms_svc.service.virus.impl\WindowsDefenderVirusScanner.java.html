<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>WindowsDefenderVirusScanner.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.service.virus.impl</a> &gt; <span class="el_source">WindowsDefenderVirusScanner.java</span></div><h1>WindowsDefenderVirusScanner.java</h1><pre class="source lang-java linenums">package com.ascentbusiness.dms_svc.service.virus.impl;

import com.ascentbusiness.dms_svc.enums.VirusScanResult;
import com.ascentbusiness.dms_svc.enums.VirusScannerType;
import com.ascentbusiness.dms_svc.service.virus.AbstractVirusScanner;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardOpenOption;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * Windows Defender virus scanner implementation.
 * 
 * &lt;p&gt;This scanner integrates with Microsoft Windows Defender through the
 * Windows Defender command-line utility (MpCmdRun.exe). Windows Defender
 * is Microsoft's built-in antivirus solution available on Windows systems.
 * 
 * &lt;p&gt;The implementation creates temporary files for scanning since Windows
 * Defender command-line tool requires file paths rather than streaming content.
 * Temporary files are securely deleted after scanning.
 * 
 * &lt;p&gt;Configuration properties:
 * &lt;ul&gt;
 *   &lt;li&gt;dms.virus-scanner.windows-defender.enabled - Enable Windows Defender scanner&lt;/li&gt;
 *   &lt;li&gt;dms.virus-scanner.windows-defender.executable-path - Path to MpCmdRun.exe&lt;/li&gt;
 *   &lt;li&gt;dms.virus-scanner.windows-defender.timeout - Scan timeout in seconds (default: 60)&lt;/li&gt;
 *   &lt;li&gt;dms.virus-scanner.windows-defender.temp-dir - Temporary directory for scan files&lt;/li&gt;
 * &lt;/ul&gt;
 * 
 * &lt;p&gt;&lt;strong&gt;Note:&lt;/strong&gt; This scanner only works on Windows systems with
 * Windows Defender installed and enabled.
 * 
 * <AUTHOR> Development Team
 * @since 1.0.0
 */
@Service
@ConditionalOnProperty(name = &quot;dms.virus-scanner.windows-defender.enabled&quot;, havingValue = &quot;true&quot;, matchIfMissing = false)
<span class="nc" id="L48">public class WindowsDefenderVirusScanner extends AbstractVirusScanner {</span>
    
    @Value(&quot;${dms.virus-scanner.windows-defender.executable-path:C:\\Program Files\\Windows Defender\\MpCmdRun.exe}&quot;)
    private String executablePath;
    
    @Value(&quot;${dms.virus-scanner.windows-defender.timeout:60}&quot;)
    private int scanTimeoutSeconds;
    
    @Value(&quot;${dms.virus-scanner.windows-defender.temp-dir:${java.io.tmpdir}}&quot;)
    private String tempDirectory;
    
    @Value(&quot;${dms.virus-scanner.windows-defender.max-file-size:104857600}&quot;) // 100MB
    private long maxFileSize;
    
<span class="nc" id="L62">    private List&lt;String&gt; lastDetectedThreats = new ArrayList&lt;&gt;();</span>
<span class="nc" id="L63">    private String scannerVersion = &quot;Unknown&quot;;</span>
    
    @Override
    public VirusScannerType getScannerType() {
<span class="nc" id="L67">        return VirusScannerType.WINDOWS_DEFENDER;</span>
    }
    
    @Override
    public boolean isAvailable() {
        // Check if we're on Windows
<span class="nc bnc" id="L73" title="All 2 branches missed.">        if (!isWindowsSystem()) {</span>
<span class="nc" id="L74">            logger.debug(&quot;Windows Defender scanner not available - not running on Windows&quot;);</span>
<span class="nc" id="L75">            return false;</span>
        }
        
        // Check if executable exists
<span class="nc" id="L79">        File executable = new File(executablePath);</span>
<span class="nc bnc" id="L80" title="All 4 branches missed.">        if (!executable.exists() || !executable.canExecute()) {</span>
<span class="nc" id="L81">            logger.debug(&quot;Windows Defender executable not found or not executable: {}&quot;, executablePath);</span>
<span class="nc" id="L82">            return false;</span>
        }
        
        try {
            // Try to get version information
<span class="nc" id="L87">            scannerVersion = getWindowsDefenderVersion();</span>
<span class="nc bnc" id="L88" title="All 4 branches missed.">            return scannerVersion != null &amp;&amp; !scannerVersion.isEmpty();</span>
<span class="nc" id="L89">        } catch (Exception e) {</span>
<span class="nc" id="L90">            logger.warn(&quot;Windows Defender availability check failed: {}&quot;, e.getMessage());</span>
<span class="nc" id="L91">            return false;</span>
        }
    }
    
    @Override
    protected VirusScanResult performScan(byte[] fileContent, String fileName, String scanId) throws Exception {
<span class="nc" id="L97">        lastDetectedThreats.clear();</span>
        
        // Create temporary file for scanning
<span class="nc" id="L100">        Path tempFile = createTempFile(fileContent, fileName, scanId);</span>
        
        try {
            // Execute Windows Defender scan
<span class="nc" id="L104">            ProcessBuilder processBuilder = new ProcessBuilder(</span>
                executablePath,
                &quot;-Scan&quot;,
                &quot;-ScanType&quot;, &quot;3&quot;, // Custom scan
<span class="nc" id="L108">                &quot;-File&quot;, tempFile.toString(),</span>
                &quot;-DisableRemediation&quot; // Don't automatically clean threats
            );
            
<span class="nc" id="L112">            processBuilder.redirectErrorStream(true);</span>
<span class="nc" id="L113">            Process process = processBuilder.start();</span>
            
            // Wait for process completion with timeout
<span class="nc" id="L116">            boolean finished = process.waitFor(scanTimeoutSeconds, TimeUnit.SECONDS);</span>
            
<span class="nc bnc" id="L118" title="All 2 branches missed.">            if (!finished) {</span>
<span class="nc" id="L119">                process.destroyForcibly();</span>
<span class="nc" id="L120">                throw new RuntimeException(&quot;Windows Defender scan timed out&quot;);</span>
            }
            
            // Read output
<span class="nc" id="L124">            String output = readProcessOutput(process);</span>
<span class="nc" id="L125">            int exitCode = process.exitValue();</span>
            
<span class="nc" id="L127">            return parseWindowsDefenderResult(output, exitCode, fileName);</span>
            
        } finally {
            // Clean up temporary file
            try {
<span class="nc" id="L132">                Files.deleteIfExists(tempFile);</span>
<span class="nc" id="L133">            } catch (IOException e) {</span>
<span class="nc" id="L134">                logger.warn(&quot;Failed to delete temporary scan file: {}&quot;, tempFile, e);</span>
<span class="nc" id="L135">            }</span>
        }
    }
    
    @Override
    protected List&lt;String&gt; getDetectedThreats() {
<span class="nc" id="L141">        return new ArrayList&lt;&gt;(lastDetectedThreats);</span>
    }
    
    @Override
    protected String getScannerSpecificInfo() {
<span class="nc" id="L146">        return String.format(&quot;Windows Defender Executable: %s, Version: %s, Timeout: %ds&quot;, </span>
<span class="nc" id="L147">                           executablePath, scannerVersion, scanTimeoutSeconds);</span>
    }
    
    @Override
    public String getScannerInfo() {
<span class="nc" id="L152">        return scannerVersion;</span>
    }
    
    @Override
    public long getMaxFileSize() {
<span class="nc" id="L157">        return maxFileSize;</span>
    }
    
    @Override
    public long getScanTimeoutMs() {
<span class="nc" id="L162">        return scanTimeoutSeconds * 1000L;</span>
    }
    
    /**
     * Checks if the current system is Windows.
     * 
     * @return true if running on Windows, false otherwise
     */
    private boolean isWindowsSystem() {
<span class="nc" id="L171">        String osName = System.getProperty(&quot;os.name&quot;);</span>
<span class="nc bnc" id="L172" title="All 4 branches missed.">        return osName != null &amp;&amp; osName.toLowerCase().contains(&quot;windows&quot;);</span>
    }
    
    /**
     * Gets Windows Defender version information.
     * 
     * @return version string or null if unavailable
     */
    private String getWindowsDefenderVersion() {
        try {
<span class="nc" id="L182">            ProcessBuilder processBuilder = new ProcessBuilder(executablePath, &quot;-h&quot;);</span>
<span class="nc" id="L183">            processBuilder.redirectErrorStream(true);</span>
<span class="nc" id="L184">            Process process = processBuilder.start();</span>
            
<span class="nc" id="L186">            boolean finished = process.waitFor(10, TimeUnit.SECONDS);</span>
<span class="nc bnc" id="L187" title="All 2 branches missed.">            if (!finished) {</span>
<span class="nc" id="L188">                process.destroyForcibly();</span>
<span class="nc" id="L189">                return null;</span>
            }
            
<span class="nc" id="L192">            String output = readProcessOutput(process);</span>
            
            // Extract version from help output
<span class="nc" id="L195">            String[] lines = output.split(&quot;\n&quot;);</span>
<span class="nc bnc" id="L196" title="All 2 branches missed.">            for (String line : lines) {</span>
<span class="nc bnc" id="L197" title="All 2 branches missed.">                if (line.toLowerCase().contains(&quot;version&quot;)) {</span>
<span class="nc" id="L198">                    return line.trim();</span>
                }
            }
            
<span class="nc" id="L202">            return &quot;Windows Defender (version unknown)&quot;;</span>
            
<span class="nc" id="L204">        } catch (Exception e) {</span>
<span class="nc" id="L205">            logger.debug(&quot;Failed to get Windows Defender version: {}&quot;, e.getMessage());</span>
<span class="nc" id="L206">            return null;</span>
        }
    }
    
    /**
     * Creates a temporary file with the content to be scanned.
     * 
     * @param fileContent the content to write
     * @param fileName the original file name
     * @param scanId the scan identifier
     * @return path to the temporary file
     * @throws IOException if file creation fails
     */
    private Path createTempFile(byte[] fileContent, String fileName, String scanId) throws IOException {
        // Create temp directory if it doesn't exist
<span class="nc" id="L221">        Path tempDir = Path.of(tempDirectory);</span>
<span class="nc" id="L222">        Files.createDirectories(tempDir);</span>
        
        // Create temporary file with scan ID prefix
<span class="nc" id="L225">        String tempFileName = String.format(&quot;dms_scan_%s_%s&quot;, scanId, fileName);</span>
<span class="nc" id="L226">        Path tempFile = tempDir.resolve(tempFileName);</span>
        
        // Write content to temporary file
<span class="nc" id="L229">        Files.write(tempFile, fileContent, StandardOpenOption.CREATE, StandardOpenOption.WRITE);</span>
        
<span class="nc" id="L231">        logger.debug(&quot;Created temporary scan file: {}&quot;, tempFile);</span>
<span class="nc" id="L232">        return tempFile;</span>
    }
    
    /**
     * Reads output from a process.
     * 
     * @param process the process to read from
     * @return the process output as a string
     * @throws IOException if reading fails
     */
    private String readProcessOutput(Process process) throws IOException {
<span class="nc" id="L243">        StringBuilder output = new StringBuilder();</span>
        
<span class="nc" id="L245">        try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {</span>
            String line;
<span class="nc bnc" id="L247" title="All 2 branches missed.">            while ((line = reader.readLine()) != null) {</span>
<span class="nc" id="L248">                output.append(line).append(&quot;\n&quot;);</span>
            }
        }
        
<span class="nc" id="L252">        return output.toString();</span>
    }
    
    /**
     * Parses Windows Defender scan result.
     * 
     * @param output the command output
     * @param exitCode the process exit code
     * @param fileName the scanned file name
     * @return the scan result
     */
    private VirusScanResult parseWindowsDefenderResult(String output, int exitCode, String fileName) {
<span class="nc" id="L264">        logger.debug(&quot;Windows Defender scan result for file {}: exit code {}, output: {}&quot;, </span>
<span class="nc" id="L265">                    fileName, exitCode, output);</span>
        
        // Windows Defender exit codes:
        // 0 = No threats found
        // 2 = Threats found
        // Other = Error
        
<span class="nc bnc" id="L272" title="All 3 branches missed.">        switch (exitCode) {</span>
            case 0:
<span class="nc" id="L274">                return VirusScanResult.CLEAN;</span>
                
            case 2:
                // Parse threats from output
<span class="nc" id="L278">                parseThreatsFromOutput(output);</span>
<span class="nc" id="L279">                return VirusScanResult.INFECTED;</span>
                
            default:
<span class="nc" id="L282">                logger.warn(&quot;Windows Defender scan error for file {}: exit code {}, output: {}&quot;, </span>
<span class="nc" id="L283">                           fileName, exitCode, output);</span>
<span class="nc" id="L284">                return VirusScanResult.ERROR;</span>
        }
    }
    
    /**
     * Parses threat information from Windows Defender output.
     * 
     * @param output the command output
     */
    private void parseThreatsFromOutput(String output) {
        // Parse threat names from output
        // This is a simplified implementation - actual parsing would depend on
        // the specific output format of Windows Defender
<span class="nc" id="L297">        String[] lines = output.split(&quot;\n&quot;);</span>
<span class="nc bnc" id="L298" title="All 2 branches missed.">        for (String line : lines) {</span>
<span class="nc bnc" id="L299" title="All 4 branches missed.">            if (line.toLowerCase().contains(&quot;threat&quot;) || line.toLowerCase().contains(&quot;virus&quot;)) {</span>
                // Extract threat name (simplified)
<span class="nc" id="L301">                String threat = line.trim();</span>
<span class="nc bnc" id="L302" title="All 2 branches missed.">                if (!threat.isEmpty()) {</span>
<span class="nc" id="L303">                    lastDetectedThreats.add(threat);</span>
                }
            }
        }
        
        // If no specific threats were parsed but we know there are threats
<span class="nc bnc" id="L309" title="All 2 branches missed.">        if (lastDetectedThreats.isEmpty()) {</span>
<span class="nc" id="L310">            lastDetectedThreats.add(&quot;Unknown threat detected by Windows Defender&quot;);</span>
        }
<span class="nc" id="L312">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>