<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>GraphQLSecurityInterceptor.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.config</a> &gt; <span class="el_source">GraphQLSecurityInterceptor.java</span></div><h1>GraphQLSecurityInterceptor.java</h1><pre class="source lang-java linenums">package com.ascentbusiness.dms_svc.config;

import com.ascentbusiness.dms_svc.exception.InvalidTokenException;
import com.ascentbusiness.dms_svc.security.JwtTokenProvider;
import com.ascentbusiness.dms_svc.security.UserPrincipal;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.graphql.server.WebGraphQlInterceptor;
import org.springframework.graphql.server.WebGraphQlRequest;
import org.springframework.graphql.server.WebGraphQlResponse;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import reactor.core.publisher.Mono;
import java.util.List;
import java.util.Map;

/**
 * WebGraphQL interceptor that handles JWT authentication for GraphQL requests.
 * This interceptor extracts JWT tokens from the Authorization header and sets up
 * the SecurityContext for GraphQL resolvers.
 */
@Component
<span class="nc" id="L27">public class GraphQLSecurityInterceptor implements WebGraphQlInterceptor {</span>

<span class="nc" id="L29">    private static final Logger logger = LoggerFactory.getLogger(GraphQLSecurityInterceptor.class);</span>

    @Autowired
    private JwtTokenProvider tokenProvider;

    @Override
    public Mono&lt;WebGraphQlResponse&gt; intercept(WebGraphQlRequest request, Chain chain) {
        // Capture the current MDC context to preserve correlation ID and tracing info
<span class="nc" id="L37">        Map&lt;String, String&gt; mdcContext = MDC.getCopyOfContextMap();</span>

        // Set MDC context immediately to ensure it's available for logging
<span class="nc bnc" id="L40" title="All 4 branches missed.">        if (mdcContext != null &amp;&amp; !mdcContext.isEmpty()) {</span>
<span class="nc" id="L41">            MDC.setContextMap(mdcContext);</span>
        }

        // Extract correlation ID directly from request headers for logging
<span class="nc" id="L45">        String correlationId = request.getHeaders().getFirst(&quot;X-Correlation-ID&quot;);</span>
<span class="nc bnc" id="L46" title="All 4 branches missed.">        if (correlationId != null &amp;&amp; !correlationId.isEmpty()) {</span>
<span class="nc" id="L47">            MDC.put(&quot;correlationId&quot;, correlationId);</span>
<span class="nc" id="L48">            logger.info(&quot;GraphQL Security Interceptor - Processing request with correlationId: {}&quot;, correlationId);</span>
        } else {
<span class="nc" id="L50">            logger.info(&quot;GraphQL Security Interceptor - Processing request&quot;);</span>
        }

        try {
            // Check if this is a schema introspection query - allow without authentication
<span class="nc bnc" id="L55" title="All 2 branches missed.">            if (isSchemaIntrospectionRequest(request)) {</span>
<span class="nc" id="L56">                logger.info(&quot;GraphQL Security Interceptor - Schema introspection query detected, allowing without authentication&quot;);</span>
                // Set a system authentication context for introspection
<span class="nc" id="L58">                UserPrincipal introspectionPrincipal = new UserPrincipal(&quot;graphql-introspection&quot;, List.of(&quot;SYSTEM&quot;), List.of(&quot;INTROSPECT&quot;));</span>
<span class="nc" id="L59">                UsernamePasswordAuthenticationToken introspectionAuth =</span>
<span class="nc" id="L60">                        new UsernamePasswordAuthenticationToken(introspectionPrincipal, null, introspectionPrincipal.getAuthorities());</span>
<span class="nc" id="L61">                SecurityContextHolder.getContext().setAuthentication(introspectionAuth);</span>
<span class="nc" id="L62">                return preserveMDCContext(chain.next(request), mdcContext);</span>
            }

            // Check if this is a generateTestToken mutation - allow without authentication
<span class="nc bnc" id="L66" title="All 2 branches missed.">            if (isGenerateTestTokenRequest(request)) {</span>
<span class="nc" id="L67">                logger.info(&quot;GraphQL Security Interceptor - generateTestToken mutation detected, allowing without authentication&quot;);</span>
                // Set a special authentication context for generateTestToken
<span class="nc" id="L69">                UserPrincipal testTokenPrincipal = new UserPrincipal(&quot;test-token-generator&quot;, List.of(&quot;SYSTEM&quot;), List.of(&quot;GENERATE_TOKEN&quot;));</span>
<span class="nc" id="L70">                UsernamePasswordAuthenticationToken testAuth =</span>
<span class="nc" id="L71">                        new UsernamePasswordAuthenticationToken(testTokenPrincipal, null, testTokenPrincipal.getAuthorities());</span>
<span class="nc" id="L72">                SecurityContextHolder.getContext().setAuthentication(testAuth);</span>
<span class="nc" id="L73">                return preserveMDCContext(chain.next(request), mdcContext);</span>
            }

<span class="nc" id="L76">            String jwt = getJwtFromRequest(request);</span>
<span class="nc" id="L77">            logger.info(&quot;GraphQL Security Interceptor - JWT present: {}&quot;, StringUtils.hasText(jwt));</span>

<span class="nc bnc" id="L79" title="All 2 branches missed.">            if (StringUtils.hasText(jwt)) {</span>
                try {
<span class="nc bnc" id="L81" title="All 2 branches missed.">                    if (tokenProvider.validateToken(jwt)) {</span>
<span class="nc" id="L82">                        logger.info(&quot;GraphQL Security Interceptor - Valid JWT token found&quot;);</span>

<span class="nc" id="L84">                        String username = tokenProvider.getUsernameFromToken(jwt);</span>
<span class="nc" id="L85">                        List&lt;String&gt; roles = tokenProvider.getRolesFromToken(jwt);</span>
<span class="nc" id="L86">                        List&lt;String&gt; permissions = tokenProvider.getPermissionsFromToken(jwt);</span>

<span class="nc" id="L88">                        logger.info(&quot;GraphQL Security Interceptor - Setting authentication for user: {}&quot;, username);</span>

                        // Create UserPrincipal with roles and permissions
<span class="nc" id="L91">                        UserPrincipal userPrincipal = new UserPrincipal(username, roles, permissions);</span>

                        // Create authentication token
<span class="nc" id="L94">                        UsernamePasswordAuthenticationToken authentication =</span>
<span class="nc" id="L95">                                new UsernamePasswordAuthenticationToken(userPrincipal, null, userPrincipal.getAuthorities());</span>

                        // Set authentication in SecurityContext
<span class="nc" id="L98">                        SecurityContextHolder.getContext().setAuthentication(authentication);</span>

<span class="nc" id="L100">                        logger.info(&quot;GraphQL Security Interceptor - Authentication set successfully for user: {}&quot;, username);</span>
                    }
<span class="nc" id="L102">                } catch (InvalidTokenException ex) {</span>
                    // JWT validation failed - this is expected behavior for invalid tokens
                    // Log at debug level only, not as an error
<span class="nc" id="L105">                    logger.debug(&quot;GraphQL Security Interceptor - JWT token validation failed: {}&quot;, ex.getMessage());</span>

                    // Clear context and set anonymous authentication
<span class="nc" id="L108">                    SecurityContextHolder.clearContext();</span>
<span class="nc" id="L109">                    UsernamePasswordAuthenticationToken anonymousAuth =</span>
<span class="nc" id="L110">                        new UsernamePasswordAuthenticationToken(&quot;anonymousUser&quot;, null, List.of());</span>
<span class="nc" id="L111">                    SecurityContextHolder.getContext().setAuthentication(anonymousAuth);</span>
<span class="nc" id="L112">                }</span>
            } else {
<span class="nc" id="L114">                logger.info(&quot;GraphQL Security Interceptor - No JWT token found&quot;);</span>
<span class="nc" id="L115">                SecurityContextHolder.clearContext();</span>

                // Set anonymous authentication to trigger proper authentication checks in resolvers
<span class="nc" id="L118">                UsernamePasswordAuthenticationToken anonymousAuth =</span>
<span class="nc" id="L119">                    new UsernamePasswordAuthenticationToken(&quot;anonymousUser&quot;, null, List.of());</span>
<span class="nc" id="L120">                SecurityContextHolder.getContext().setAuthentication(anonymousAuth);</span>
            }
<span class="nc" id="L122">        } catch (Exception ex) {</span>
<span class="nc" id="L123">            logger.error(&quot;GraphQL Security Interceptor - Unexpected error processing request&quot;, ex);</span>
<span class="nc" id="L124">            SecurityContextHolder.clearContext();</span>
<span class="nc" id="L125">        }</span>

<span class="nc" id="L127">        return preserveMDCContext(chain.next(request), mdcContext);</span>
    }

    /**
     * Preserve MDC context throughout the reactive chain to ensure correlation ID
     * and tracing information is available in downstream processing
     */
    private Mono&lt;WebGraphQlResponse&gt; preserveMDCContext(Mono&lt;WebGraphQlResponse&gt; responseMono, Map&lt;String, String&gt; mdcContext) {
<span class="nc bnc" id="L135" title="All 4 branches missed.">        if (mdcContext == null || mdcContext.isEmpty()) {</span>
<span class="nc" id="L136">            return responseMono;</span>
        }

        // Wrap the entire reactive chain to ensure MDC is available throughout
<span class="nc" id="L140">        return Mono.deferContextual(contextView -&gt; {</span>
            // Set MDC context immediately when the chain starts
<span class="nc" id="L142">            MDC.setContextMap(mdcContext);</span>

<span class="nc" id="L144">            return responseMono</span>
<span class="nc" id="L145">                .doOnSubscribe(subscription -&gt; {</span>
                    // Ensure MDC context is set when subscription starts
<span class="nc" id="L147">                    MDC.setContextMap(mdcContext);</span>
<span class="nc" id="L148">                })</span>
<span class="nc" id="L149">                .doOnNext(response -&gt; {</span>
                    // Ensure MDC context is set for each emission
<span class="nc" id="L151">                    MDC.setContextMap(mdcContext);</span>
<span class="nc" id="L152">                })</span>
<span class="nc" id="L153">                .doOnError(error -&gt; {</span>
                    // Ensure MDC context is set for error handling
<span class="nc" id="L155">                    MDC.setContextMap(mdcContext);</span>
<span class="nc" id="L156">                })</span>
<span class="nc" id="L157">                .doFinally(signalType -&gt; {</span>
                    // Ensure MDC context is available even during cleanup
<span class="nc" id="L159">                    MDC.setContextMap(mdcContext);</span>
<span class="nc" id="L160">                });</span>
        });
    }

    private String getJwtFromRequest(WebGraphQlRequest request) {
<span class="nc" id="L165">        String bearerToken = request.getHeaders().getFirst(&quot;Authorization&quot;);</span>
<span class="nc bnc" id="L166" title="All 4 branches missed.">        if (StringUtils.hasText(bearerToken) &amp;&amp; bearerToken.startsWith(&quot;Bearer &quot;)) {</span>
<span class="nc" id="L167">            return bearerToken.substring(7);</span>
        }
<span class="nc" id="L169">        return null;</span>
    }

    /**
     * Check if the GraphQL request is for schema introspection
     */
    private boolean isSchemaIntrospectionRequest(WebGraphQlRequest request) {
        try {
<span class="nc" id="L177">            String query = request.getDocument();</span>
<span class="nc bnc" id="L178" title="All 2 branches missed.">            if (StringUtils.hasText(query)) {</span>
                // Check if the query contains schema introspection patterns
<span class="nc bnc" id="L180" title="All 2 branches missed.">                return query.contains(&quot;__schema&quot;) || </span>
<span class="nc bnc" id="L181" title="All 2 branches missed.">                       query.contains(&quot;__type&quot;) || </span>
<span class="nc bnc" id="L182" title="All 2 branches missed.">                       query.contains(&quot;__typename&quot;) ||</span>
<span class="nc bnc" id="L183" title="All 2 branches missed.">                       query.contains(&quot;IntrospectionQuery&quot;) ||</span>
<span class="nc bnc" id="L184" title="All 4 branches missed.">                       (query.contains(&quot;query&quot;) &amp;&amp; query.contains(&quot;__&quot;));</span>
            }
<span class="nc" id="L186">        } catch (Exception ex) {</span>
<span class="nc" id="L187">            logger.debug(&quot;Error checking for schema introspection request&quot;, ex);</span>
<span class="nc" id="L188">        }</span>
<span class="nc" id="L189">        return false;</span>
    }

    /**
     * Check if the GraphQL request is for generateTestToken mutation
     */
    private boolean isGenerateTestTokenRequest(WebGraphQlRequest request) {
        try {
<span class="nc" id="L197">            String query = request.getDocument();</span>
<span class="nc bnc" id="L198" title="All 2 branches missed.">            if (StringUtils.hasText(query)) {</span>
                // Check if the query contains generateTestToken mutation
<span class="nc bnc" id="L200" title="All 2 branches missed.">                return query.contains(&quot;generateTestToken&quot;) &amp;&amp;</span>
<span class="nc bnc" id="L201" title="All 4 branches missed.">                       (query.contains(&quot;mutation&quot;) || query.toLowerCase().contains(&quot;mutation&quot;));</span>
            }
<span class="nc" id="L203">        } catch (Exception ex) {</span>
<span class="nc" id="L204">            logger.debug(&quot;Error checking for generateTestToken request&quot;, ex);</span>
<span class="nc" id="L205">        }</span>
<span class="nc" id="L206">        return false;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>