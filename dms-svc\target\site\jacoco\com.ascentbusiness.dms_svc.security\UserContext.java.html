<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>UserContext.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.security</a> &gt; <span class="el_source">UserContext.java</span></div><h1>UserContext.java</h1><pre class="source lang-java linenums">package com.ascentbusiness.dms_svc.security;

import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Service for accessing current user context and security information.
 * 
 * Provides methods to get current user ID, roles, and permissions
 * for use in security-aware operations like Elasticsearch filtering.
 */
<span class="nc" id="L18">@Slf4j</span>
@Component
<span class="nc" id="L20">public class UserContext {</span>

    /**
     * Get the current authenticated user ID (alias for getCurrentUserId)
     */
    public String getUserId() {
<span class="nc" id="L26">        return getCurrentUserId();</span>
    }

    /**
     * Get the current authenticated user ID
     */
    public String getCurrentUserId() {
<span class="nc" id="L33">        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();</span>

<span class="nc bnc" id="L35" title="All 4 branches missed.">        if (authentication == null || !authentication.isAuthenticated()) {</span>
<span class="nc" id="L36">            log.warn(&quot;No authenticated user found in security context&quot;);</span>
<span class="nc" id="L37">            return null;</span>
        }

<span class="nc bnc" id="L40" title="All 2 branches missed.">        if (authentication.getPrincipal() instanceof UserPrincipal) {</span>
<span class="nc" id="L41">            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();</span>
<span class="nc" id="L42">            return userPrincipal.getUsername();</span>
        }

        // Fallback to authentication name
<span class="nc" id="L46">        return authentication.getName();</span>
    }

    /**
     * Get the current authenticated user ID or throw exception if not authenticated
     */
    public String getCurrentUserIdRequired() {
<span class="nc" id="L53">        String userId = getCurrentUserId();</span>
<span class="nc bnc" id="L54" title="All 2 branches missed.">        if (userId == null) {</span>
<span class="nc" id="L55">            throw new IllegalStateException(&quot;No authenticated user found&quot;);</span>
        }
<span class="nc" id="L57">        return userId;</span>
    }

    /**
     * Get the current authenticated user ID or return null if not authenticated (safe version)
     */
    public String getCurrentUserIdOrNull() {
        try {
<span class="nc" id="L65">            return getCurrentUserId();</span>
<span class="nc" id="L66">        } catch (Exception e) {</span>
<span class="nc" id="L67">            log.debug(&quot;Failed to get current user ID: {}&quot;, e.getMessage());</span>
<span class="nc" id="L68">            return null;</span>
        }
    }

    /**
     * Get the current user's roles
     */
    public Set&lt;String&gt; getCurrentUserRoles() {
<span class="nc" id="L76">        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();</span>
        
<span class="nc bnc" id="L78" title="All 4 branches missed.">        if (authentication == null || !authentication.isAuthenticated()) {</span>
<span class="nc" id="L79">            log.warn(&quot;No authenticated user found in security context&quot;);</span>
<span class="nc" id="L80">            return Set.of();</span>
        }
        
<span class="nc bnc" id="L83" title="All 2 branches missed.">        if (authentication.getPrincipal() instanceof UserPrincipal) {</span>
<span class="nc" id="L84">            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();</span>
<span class="nc" id="L85">            return new HashSet&lt;&gt;(userPrincipal.getRoles());</span>
        }
        
        // Fallback to authorities
<span class="nc" id="L89">        return authentication.getAuthorities().stream()</span>
<span class="nc" id="L90">            .map(authority -&gt; authority.getAuthority())</span>
<span class="nc" id="L91">            .filter(role -&gt; role.startsWith(&quot;ROLE_&quot;))</span>
<span class="nc" id="L92">            .map(role -&gt; role.substring(5)) // Remove &quot;ROLE_&quot; prefix</span>
<span class="nc" id="L93">            .collect(Collectors.toSet());</span>
    }

    /**
     * Check if current user has a specific role
     */
    public boolean hasRole(String role) {
<span class="nc" id="L100">        Set&lt;String&gt; userRoles = getCurrentUserRoles();</span>
<span class="nc" id="L101">        return userRoles.contains(role);</span>
    }

    /**
     * Check if current user has any of the specified roles
     */
    public boolean hasAnyRole(Set&lt;String&gt; roles) {
<span class="nc" id="L108">        Set&lt;String&gt; userRoles = getCurrentUserRoles();</span>
<span class="nc" id="L109">        return roles.stream().anyMatch(userRoles::contains);</span>
    }

    /**
     * Check if current user is authenticated
     */
    public boolean isAuthenticated() {
<span class="nc" id="L116">        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();</span>
<span class="nc bnc" id="L117" title="All 2 branches missed.">        return authentication != null &amp;&amp; </span>
<span class="nc bnc" id="L118" title="All 2 branches missed.">               authentication.isAuthenticated() &amp;&amp; </span>
<span class="nc bnc" id="L119" title="All 2 branches missed.">               !authentication.getPrincipal().equals(&quot;anonymousUser&quot;);</span>
    }

    /**
     * Get current user's authentication object
     */
    public Authentication getCurrentAuthentication() {
<span class="nc" id="L126">        return SecurityContextHolder.getContext().getAuthentication();</span>
    }

    /**
     * Check if current user is an admin
     */
    public boolean isAdmin() {
<span class="nc bnc" id="L133" title="All 4 branches missed.">        return hasRole(&quot;ADMIN&quot;) || hasRole(&quot;SYSTEM_ADMIN&quot;);</span>
    }

    /**
     * Check if current user can access confidential documents
     */
    public boolean canAccessConfidential() {
<span class="nc" id="L140">        return hasAnyRole(Set.of(&quot;ADMIN&quot;, &quot;SYSTEM_ADMIN&quot;, &quot;CONFIDENTIAL_ACCESS&quot;));</span>
    }

    /**
     * Get user's effective permissions for document access
     */
    public Set&lt;String&gt; getEffectivePermissions() {
<span class="nc" id="L147">        Set&lt;String&gt; roles = getCurrentUserRoles();</span>

        // Add derived permissions based on roles
<span class="nc" id="L150">        Set&lt;String&gt; permissions = roles.stream()</span>
<span class="nc" id="L151">            .collect(Collectors.toSet());</span>

        // Add special permissions
<span class="nc bnc" id="L154" title="All 2 branches missed.">        if (isAdmin()) {</span>
<span class="nc" id="L155">            permissions.add(&quot;ALL_DOCUMENTS&quot;);</span>
        }

<span class="nc bnc" id="L158" title="All 2 branches missed.">        if (canAccessConfidential()) {</span>
<span class="nc" id="L159">            permissions.add(&quot;CONFIDENTIAL_ACCESS&quot;);</span>
        }

<span class="nc" id="L162">        return permissions;</span>
    }

    /**
     * Get the current user's department
     * TODO: Implement department retrieval from JWT token claims or User entity
     * For now, returns null as department information is not yet available in the security context
     */
    public String getDepartment() {
<span class="nc" id="L171">        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();</span>

<span class="nc bnc" id="L173" title="All 4 branches missed.">        if (authentication == null || !authentication.isAuthenticated()) {</span>
<span class="nc" id="L174">            log.warn(&quot;No authenticated user found in security context&quot;);</span>
<span class="nc" id="L175">            return null;</span>
        }

        // TODO: Add department support to JWT token and UserPrincipal
        // For now, return null as department information is not available
<span class="nc" id="L180">        log.debug(&quot;Department information not yet implemented in UserContext&quot;);</span>
<span class="nc" id="L181">        return null;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>