<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>GraphQLExceptionHandler.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.config</a> &gt; <span class="el_source">GraphQLExceptionHandler.java</span></div><h1>GraphQLExceptionHandler.java</h1><pre class="source lang-java linenums">package com.ascentbusiness.dms_svc.config;

import com.ascentbusiness.dms_svc.exception.*;
import com.ascentbusiness.dms_svc.util.ExceptionUtil;
import graphql.ErrorType;
import graphql.GraphQLError;
import graphql.schema.DataFetchingEnvironment;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.graphql.execution.DataFetcherExceptionResolverAdapter;
import org.springframework.lang.NonNull;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.core.AuthenticationException;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
<span class="nc" id="L20">public class GraphQLExceptionHandler extends DataFetcherExceptionResolverAdapter {</span>

<span class="nc" id="L22">    private static final Logger logger = LoggerFactory.getLogger(GraphQLExceptionHandler.class);</span>

    @Override
    protected GraphQLError resolveToSingleError(@NonNull Throwable ex, @NonNull DataFetchingEnvironment env) {
        // Handle specific DMS exceptions first (before general DmsException handling)
        // This ensures specific error codes are used instead of generic ones

<span class="nc bnc" id="L29" title="All 2 branches missed.">        if (ex instanceof UnauthorizedException) {</span>
<span class="nc" id="L30">            logger.warn(&quot;Authorization error in GraphQL operation: {} - {}&quot;,</span>
<span class="nc" id="L31">                    env.getField().getName(), ex.getMessage());</span>

<span class="nc" id="L33">            return GraphQLError.newError()</span>
<span class="nc" id="L34">                    .errorType(ErrorType.DataFetchingException)</span>
<span class="nc" id="L35">                    .message(ex.getMessage())</span>
<span class="nc" id="L36">                    .location(env.getField().getSourceLocation())</span>
<span class="nc" id="L37">                    .path(env.getExecutionStepInfo().getPath())</span>
<span class="nc" id="L38">                    .extensions(Map.of(</span>
                            &quot;code&quot;, &quot;FORBIDDEN&quot;,
                            &quot;type&quot;, &quot;AUTHORIZATION_ERROR&quot;,
<span class="nc" id="L41">                            &quot;operation&quot;, env.getField().getName()</span>
                    ))
<span class="nc" id="L43">                    .build();</span>
        }

        // Handle JWT token validation exceptions (legacy support)
<span class="nc bnc" id="L47" title="All 2 branches missed.">        if (ex instanceof InvalidTokenException) {</span>
<span class="nc" id="L48">            logger.warn(&quot;Invalid JWT token in GraphQL operation: {} - {}&quot;,</span>
<span class="nc" id="L49">                    env.getField().getName(), ex.getMessage());</span>

<span class="nc" id="L51">            return GraphQLError.newError()</span>
<span class="nc" id="L52">                    .errorType(ErrorType.DataFetchingException)</span>
<span class="nc" id="L53">                    .message(ex.getMessage())</span>
<span class="nc" id="L54">                    .location(env.getField().getSourceLocation())</span>
<span class="nc" id="L55">                    .path(env.getExecutionStepInfo().getPath())</span>
<span class="nc" id="L56">                    .extensions(Map.of(</span>
                            &quot;code&quot;, &quot;INVALID_TOKEN&quot;,
                            &quot;type&quot;, &quot;AUTHENTICATION_ERROR&quot;,
<span class="nc" id="L59">                            &quot;operation&quot;, env.getField().getName()</span>
                    ))
<span class="nc" id="L61">                    .build();</span>
        }

        // Handle Spring Security authentication exceptions
<span class="nc bnc" id="L65" title="All 2 branches missed.">        if (ex instanceof AuthenticationException) {</span>
<span class="nc" id="L66">            logger.warn(&quot;Authentication error in GraphQL operation: {} - {}&quot;,</span>
<span class="nc" id="L67">                    env.getField().getName(), ex.getMessage());</span>

<span class="nc" id="L69">            return GraphQLError.newError()</span>
<span class="nc" id="L70">                    .errorType(ErrorType.DataFetchingException)</span>
<span class="nc" id="L71">                    .message(&quot;Authentication required or invalid credentials&quot;)</span>
<span class="nc" id="L72">                    .location(env.getField().getSourceLocation())</span>
<span class="nc" id="L73">                    .path(env.getExecutionStepInfo().getPath())</span>
<span class="nc" id="L74">                    .extensions(Map.of(</span>
                            &quot;code&quot;, &quot;AUTHENTICATION_REQUIRED&quot;,
                            &quot;type&quot;, &quot;AUTHENTICATION_ERROR&quot;,
<span class="nc" id="L77">                            &quot;operation&quot;, env.getField().getName()</span>
                    ))
<span class="nc" id="L79">                    .build();</span>
        }

        // Handle Spring Security access denied exceptions
<span class="nc bnc" id="L83" title="All 2 branches missed.">        if (ex instanceof AccessDeniedException) {</span>
<span class="nc" id="L84">            logger.warn(&quot;Access denied in GraphQL operation: {} - {}&quot;,</span>
<span class="nc" id="L85">                    env.getField().getName(), ex.getMessage());</span>

<span class="nc" id="L87">            return GraphQLError.newError()</span>
<span class="nc" id="L88">                    .errorType(ErrorType.DataFetchingException)</span>
<span class="nc" id="L89">                    .message(&quot;Access denied - insufficient permissions&quot;)</span>
<span class="nc" id="L90">                    .location(env.getField().getSourceLocation())</span>
<span class="nc" id="L91">                    .path(env.getExecutionStepInfo().getPath())</span>
<span class="nc" id="L92">                    .extensions(Map.of(</span>
                            &quot;code&quot;, &quot;ACCESS_DENIED&quot;,
                            &quot;type&quot;, &quot;AUTHORIZATION_ERROR&quot;,
<span class="nc" id="L95">                            &quot;operation&quot;, env.getField().getName()</span>
                    ))
<span class="nc" id="L97">                    .build();</span>
        }

        // Handle DMS exceptions with structured error information
        // This comes after specific exception handling to avoid overriding specific error codes
<span class="nc bnc" id="L102" title="All 2 branches missed.">        if (ex instanceof DmsException dmsEx) {</span>
<span class="nc" id="L103">            return handleDmsException(dmsEx, env);</span>
        }
        
<span class="nc bnc" id="L106" title="All 2 branches missed.">        if (ex instanceof DocumentNotFoundException) {</span>
<span class="nc" id="L107">            logger.warn(&quot;Document not found in GraphQL operation: {} - {}&quot;, </span>
<span class="nc" id="L108">                    env.getField().getName(), ex.getMessage());</span>
            
<span class="nc" id="L110">            return GraphQLError.newError()</span>
<span class="nc" id="L111">                    .errorType(ErrorType.DataFetchingException)</span>
<span class="nc" id="L112">                    .message(ex.getMessage())</span>
<span class="nc" id="L113">                    .location(env.getField().getSourceLocation())</span>
<span class="nc" id="L114">                    .path(env.getExecutionStepInfo().getPath())</span>
<span class="nc" id="L115">                    .extensions(Map.of(</span>
                            &quot;code&quot;, &quot;NOT_FOUND&quot;,
                            &quot;type&quot;, &quot;DOCUMENT_NOT_FOUND&quot;,
<span class="nc" id="L118">                            &quot;operation&quot;, env.getField().getName()</span>
                    ))
<span class="nc" id="L120">                    .build();</span>
        }
        
<span class="nc bnc" id="L123" title="All 2 branches missed.">        if (ex instanceof DuplicateFileException) {</span>
<span class="nc" id="L124">            logger.info(&quot;Duplicate file detected in GraphQL operation: {} - {}&quot;, </span>
<span class="nc" id="L125">                    env.getField().getName(), ex.getMessage());</span>
            
<span class="nc" id="L127">            return GraphQLError.newError()</span>
<span class="nc" id="L128">                    .errorType(ErrorType.ValidationError)</span>
<span class="nc" id="L129">                    .message(ex.getMessage())</span>
<span class="nc" id="L130">                    .location(env.getField().getSourceLocation())</span>
<span class="nc" id="L131">                    .path(env.getExecutionStepInfo().getPath())</span>
<span class="nc" id="L132">                    .extensions(Map.of(</span>
                            &quot;code&quot;, &quot;DUPLICATE_FILE&quot;,
                            &quot;type&quot;, &quot;VALIDATION_ERROR&quot;,
<span class="nc" id="L135">                            &quot;operation&quot;, env.getField().getName()</span>
                    ))
<span class="nc" id="L137">                    .build();</span>
        }
        
<span class="nc bnc" id="L140" title="All 2 branches missed.">        if (ex instanceof HistoricalDocumentException) {</span>
<span class="nc" id="L141">            logger.info(&quot;Historical document versioning attempted in GraphQL operation: {} - {}&quot;, </span>
<span class="nc" id="L142">                    env.getField().getName(), ex.getMessage());</span>
            
<span class="nc" id="L144">            return GraphQLError.newError()</span>
<span class="nc" id="L145">                    .errorType(ErrorType.ValidationError)</span>
<span class="nc" id="L146">                    .message(ex.getMessage())</span>
<span class="nc" id="L147">                    .location(env.getField().getSourceLocation())</span>
<span class="nc" id="L148">                    .path(env.getExecutionStepInfo().getPath())</span>
<span class="nc" id="L149">                    .extensions(Map.of(</span>
                            &quot;code&quot;, &quot;HISTORICAL_DOCUMENT&quot;,
                            &quot;type&quot;, &quot;VALIDATION_ERROR&quot;,
<span class="nc" id="L152">                            &quot;operation&quot;, env.getField().getName()</span>
                    ))
<span class="nc" id="L154">                    .build();</span>
        }
        
<span class="nc bnc" id="L157" title="All 2 branches missed.">        if (ex instanceof IllegalArgumentException) {</span>
<span class="nc" id="L158">            logger.warn(&quot;Invalid argument in GraphQL operation: {} - {}&quot;, </span>
<span class="nc" id="L159">                    env.getField().getName(), ex.getMessage());</span>
            
<span class="nc" id="L161">            return GraphQLError.newError()</span>
<span class="nc" id="L162">                    .errorType(ErrorType.ValidationError)</span>
<span class="nc" id="L163">                    .message(ex.getMessage())</span>
<span class="nc" id="L164">                    .location(env.getField().getSourceLocation())</span>
<span class="nc" id="L165">                    .path(env.getExecutionStepInfo().getPath())</span>
<span class="nc" id="L166">                    .extensions(Map.of(</span>
                            &quot;code&quot;, &quot;INVALID_ARGUMENT&quot;,
                            &quot;type&quot;, &quot;VALIDATION_ERROR&quot;,
<span class="nc" id="L169">                            &quot;operation&quot;, env.getField().getName()</span>
                    ))
<span class="nc" id="L171">                    .build();</span>
        }
        
<span class="nc bnc" id="L174" title="All 2 branches missed.">        if (ex instanceof IllegalStateException) {</span>
<span class="nc" id="L175">            logger.warn(&quot;Invalid state in GraphQL operation: {} - {}&quot;, </span>
<span class="nc" id="L176">                    env.getField().getName(), ex.getMessage());</span>
            
<span class="nc" id="L178">            return GraphQLError.newError()</span>
<span class="nc" id="L179">                    .errorType(ErrorType.ValidationError)</span>
<span class="nc" id="L180">                    .message(ex.getMessage())</span>
<span class="nc" id="L181">                    .location(env.getField().getSourceLocation())</span>
<span class="nc" id="L182">                    .path(env.getExecutionStepInfo().getPath())</span>
<span class="nc" id="L183">                    .extensions(Map.of(</span>
                            &quot;code&quot;, &quot;INVALID_STATE&quot;,
                            &quot;type&quot;, &quot;VALIDATION_ERROR&quot;,
<span class="nc" id="L186">                            &quot;operation&quot;, env.getField().getName()</span>
                    ))
<span class="nc" id="L188">                    .build();</span>
        }
        
        // Handle file I/O exceptions (like file not found on server)
<span class="nc bnc" id="L192" title="All 2 branches missed.">        if (ex instanceof java.io.IOException) {</span>
<span class="nc" id="L193">            logger.error(&quot;I/O error in GraphQL operation: {} - {}&quot;, </span>
<span class="nc" id="L194">                    env.getField().getName(), ex.getMessage());</span>
            
<span class="nc" id="L196">            return GraphQLError.newError()</span>
<span class="nc" id="L197">                    .errorType(ErrorType.DataFetchingException)</span>
<span class="nc" id="L198">                    .message(&quot;File operation failed: &quot; + ex.getMessage())</span>
<span class="nc" id="L199">                    .location(env.getField().getSourceLocation())</span>
<span class="nc" id="L200">                    .path(env.getExecutionStepInfo().getPath())</span>
<span class="nc" id="L201">                    .extensions(Map.of(</span>
                            &quot;code&quot;, &quot;FILE_ERROR&quot;,
                            &quot;type&quot;, &quot;IO_ERROR&quot;,
<span class="nc" id="L204">                            &quot;operation&quot;, env.getField().getName()</span>
                    ))
<span class="nc" id="L206">                    .build();</span>
        }
        
        // For any other unexpected exceptions, log and return a generic error
<span class="nc" id="L210">        logger.error(&quot;Unexpected error in GraphQL operation: {} - {}&quot;, </span>
<span class="nc" id="L211">                env.getField().getName(), ex.getMessage(), ex);</span>
        
<span class="nc" id="L213">        return GraphQLError.newError()</span>
<span class="nc" id="L214">                .errorType(ErrorType.DataFetchingException)</span>
<span class="nc" id="L215">                .message(&quot;An unexpected error occurred. Please try again later.&quot;)</span>
<span class="nc" id="L216">                .location(env.getField().getSourceLocation())</span>
<span class="nc" id="L217">                .path(env.getExecutionStepInfo().getPath())</span>
<span class="nc" id="L218">                .extensions(Map.of(</span>
                        &quot;code&quot;, &quot;INTERNAL_ERROR&quot;,
                        &quot;type&quot;, &quot;SYSTEM_ERROR&quot;,
<span class="nc" id="L221">                        &quot;operation&quot;, env.getField().getName()</span>
                ))
<span class="nc" id="L223">                .build();</span>
    }

    /**
     * Handle DmsException with structured error information and proper logging
     */
    private GraphQLError handleDmsException(DmsException ex, DataFetchingEnvironment env) {
        // Log the exception using the utility
<span class="nc" id="L231">        ExceptionUtil.logDmsException(ex, env.getField().getName());</span>

        // Determine GraphQL error type based on DMS exception category
<span class="nc bnc" id="L234" title="All 6 branches missed.">        ErrorType errorType = switch (ex.getCategory()) {</span>
<span class="nc" id="L235">            case VALIDATION -&gt; ErrorType.ValidationError;</span>
<span class="nc" id="L236">            case AUTHENTICATION, AUTHORIZATION -&gt; ErrorType.DataFetchingException;</span>
<span class="nc" id="L237">            case BUSINESS_RULE -&gt; ErrorType.ValidationError;</span>
<span class="nc" id="L238">            case SYSTEM, EXTERNAL_SERVICE -&gt; ErrorType.DataFetchingException;</span>
<span class="nc" id="L239">            case DATA_INTEGRITY -&gt; ErrorType.DataFetchingException;</span>
        };

        // Create extensions with structured error information
<span class="nc" id="L243">        Map&lt;String, Object&gt; extensions = Map.of(</span>
<span class="nc" id="L244">            &quot;code&quot;, ex.getErrorCode(),</span>
<span class="nc" id="L245">            &quot;type&quot;, ex.getCategory().toString(),</span>
<span class="nc" id="L246">            &quot;severity&quot;, ex.getSeverity().toString(),</span>
<span class="nc" id="L247">            &quot;correlationId&quot;, ex.getCorrelationId(),</span>
<span class="nc" id="L248">            &quot;timestamp&quot;, ex.getTimestamp().toString(),</span>
<span class="nc" id="L249">            &quot;operation&quot;, env.getField().getName(),</span>
<span class="nc" id="L250">            &quot;details&quot;, ex.getErrorDetails()</span>
        );

<span class="nc" id="L253">        return GraphQLError.newError()</span>
<span class="nc" id="L254">                .errorType(errorType)</span>
<span class="nc" id="L255">                .message(ex.getMessage())</span>
<span class="nc" id="L256">                .location(env.getField().getSourceLocation())</span>
<span class="nc" id="L257">                .path(env.getExecutionStepInfo().getPath())</span>
<span class="nc" id="L258">                .extensions(extensions)</span>
<span class="nc" id="L259">                .build();</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>