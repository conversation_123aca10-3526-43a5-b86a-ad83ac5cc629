<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>TestCaseGraphQLResolver.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.resolver</a> &gt; <span class="el_source">TestCaseGraphQLResolver.java</span></div><h1>TestCaseGraphQLResolver.java</h1><pre class="source lang-java linenums">package com.ascentbusiness.dms_svc.resolver;

import com.ascentbusiness.dms_svc.dto.TestCaseCollection;
import com.ascentbusiness.dms_svc.dto.TestCaseResponse;
import com.ascentbusiness.dms_svc.dto.TestCategorySummary;
import com.ascentbusiness.dms_svc.service.TestCaseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.graphql.data.method.annotation.Argument;
import org.springframework.graphql.data.method.annotation.QueryMapping;
import org.springframework.stereotype.Controller;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * GraphQL resolver for test case management and retrieval operations.
 * This resolver implements all test case operations to replace TestCaseController REST endpoints.
 * 
 * Provides comprehensive test case functionality including:
 * - Test case discovery and categorization
 * - Category-specific test case retrieval (22 categories)
 * - Search functionality across all test cases
 * - Test case summaries and health checks
 * - Individual test case retrieval
 */
@Controller
<span class="nc" id="L30">@Slf4j</span>
<span class="nc" id="L31">public class TestCaseGraphQLResolver {</span>

    @Autowired
    private TestCaseService testCaseService;

    // ===== OVERVIEW AND SUMMARY QUERIES =====

    /**
     * Get overview of all test categories with counts and descriptions.
     * Implements getAllTestCases query - replaces GET /api/test-cases
     */
    @QueryMapping
    public Map&lt;String, Object&gt; getAllTestCases() {
<span class="nc" id="L44">        log.info(&quot;GraphQL getAllTestCases called&quot;);</span>
        
<span class="nc" id="L46">        List&lt;TestCategorySummary&gt; summaries = testCaseService.getAllTestCategoriesSummary();</span>
<span class="nc" id="L47">        List&lt;String&gt; categories = testCaseService.getAllCategories();</span>
        
<span class="nc" id="L49">        Map&lt;String, Object&gt; response = new HashMap&lt;&gt;();</span>
<span class="nc" id="L50">        response.put(&quot;totalCategories&quot;, categories.size());</span>
<span class="nc bnc" id="L51" title="All 2 branches missed.">        response.put(&quot;totalTestCases&quot;, summaries.stream().mapToInt(s -&gt; s.getTestCount() != null ? s.getTestCount() : 0).sum());</span>
<span class="nc" id="L52">        response.put(&quot;categories&quot;, summaries);</span>
<span class="nc" id="L53">        response.put(&quot;availableCategories&quot;, categories);</span>
        
<span class="nc" id="L55">        return response;</span>
    }

    /**
     * Get summary of all test categories.
     * Implements getTestCasesSummary query
     */
    @QueryMapping
    public List&lt;TestCategorySummary&gt; getTestCasesSummary() {
<span class="nc" id="L64">        log.info(&quot;GraphQL getTestCasesSummary called&quot;);</span>
<span class="nc" id="L65">        return testCaseService.getAllTestCategoriesSummary();</span>
    }

    /**
     * Get list of all available test categories.
     * Implements getAllCategories query
     */
    @QueryMapping
    public List&lt;String&gt; getAllCategories() {
<span class="nc" id="L74">        log.info(&quot;GraphQL getAllCategories called&quot;);</span>
<span class="nc" id="L75">        return testCaseService.getAllCategories();</span>
    }

    // ===== CATEGORY-SPECIFIC TEST CASE QUERIES =====

    /**
     * Get test cases by category (generic endpoint).
     * Implements getTestCasesByCategory query - replaces GET /api/test-cases/{category}
     */
    @QueryMapping
    public TestCaseCollection getTestCasesByCategory(@Argument String category) {
<span class="nc" id="L86">        log.info(&quot;GraphQL getTestCasesByCategory called for category: {}&quot;, category);</span>
<span class="nc" id="L87">        return testCaseService.getTestCasesByCategory(category);</span>
    }

    /**
     * Get all test cases for No Access category.
     * Implements getNoAccessTestCases query - replaces GET /api/test-cases/no-access
     */
    @QueryMapping
    public TestCaseCollection getNoAccessTestCases() {
<span class="nc" id="L96">        log.info(&quot;GraphQL getNoAccessTestCases called&quot;);</span>
<span class="nc" id="L97">        return testCaseService.getTestCasesByCategory(&quot;No_Access&quot;);</span>
    }

    /**
     * Get all test cases for Read Permission category.
     * Implements getReadPermissionTestCases query - replaces GET /api/test-cases/read-permission
     */
    @QueryMapping
    public TestCaseCollection getReadPermissionTestCases() {
<span class="nc" id="L106">        log.info(&quot;GraphQL getReadPermissionTestCases called&quot;);</span>
<span class="nc" id="L107">        return testCaseService.getTestCasesByCategory(&quot;Read_Permission&quot;);</span>
    }

    /**
     * Get all test cases for Write Permission category.
     * Implements getWritePermissionTestCases query - replaces GET /api/test-cases/write-permission
     */
    @QueryMapping
    public TestCaseCollection getWritePermissionTestCases() {
<span class="nc" id="L116">        log.info(&quot;GraphQL getWritePermissionTestCases called&quot;);</span>
<span class="nc" id="L117">        return testCaseService.getTestCasesByCategory(&quot;Write_Permission&quot;);</span>
    }

    /**
     * Get all test cases for Delete Permission category.
     * Implements getDeletePermissionTestCases query - replaces GET /api/test-cases/delete-permission
     */
    @QueryMapping
    public TestCaseCollection getDeletePermissionTestCases() {
<span class="nc" id="L126">        log.info(&quot;GraphQL getDeletePermissionTestCases called&quot;);</span>
<span class="nc" id="L127">        return testCaseService.getTestCasesByCategory(&quot;Delete_Permission&quot;);</span>
    }

    /**
     * Get all test cases for Admin Permission category.
     * Implements getAdminPermissionTestCases query - replaces GET /api/test-cases/admin-permission
     */
    @QueryMapping
    public TestCaseCollection getAdminPermissionTestCases() {
<span class="nc" id="L136">        log.info(&quot;GraphQL getAdminPermissionTestCases called&quot;);</span>
<span class="nc" id="L137">        return testCaseService.getTestCasesByCategory(&quot;ADMIN_Permission&quot;);</span>
    }

    /**
     * Get all test cases for Creator Privileges category.
     * Implements getCreatorPrivilegesTestCases query - replaces GET /api/test-cases/creator-privileges
     */
    @QueryMapping
    public TestCaseCollection getCreatorPrivilegesTestCases() {
<span class="nc" id="L146">        log.info(&quot;GraphQL getCreatorPrivilegesTestCases called&quot;);</span>
<span class="nc" id="L147">        return testCaseService.getTestCasesByCategory(&quot;Creator_Privileges&quot;);</span>
    }

    /**
     * Get all test cases for Multi Role category.
     * Implements getMultiRoleTestCases query - replaces GET /api/test-cases/multi-role
     */
    @QueryMapping
    public TestCaseCollection getMultiRoleTestCases() {
<span class="nc" id="L156">        log.info(&quot;GraphQL getMultiRoleTestCases called&quot;);</span>
<span class="nc" id="L157">        return testCaseService.getTestCasesByCategory(&quot;Multi_Role&quot;);</span>
    }

    /**
     * Get all test cases for Error Handling category.
     * Implements getErrorHandlingTestCases query - replaces GET /api/test-cases/error-handling
     */
    @QueryMapping
    public TestCaseCollection getErrorHandlingTestCases() {
<span class="nc" id="L166">        log.info(&quot;GraphQL getErrorHandlingTestCases called&quot;);</span>
<span class="nc" id="L167">        return testCaseService.getTestCasesByCategory(&quot;Error_Handling&quot;);</span>
    }

    /**
     * Get all test cases for Storage Providers category.
     * Implements getStorageProvidersTestCases query - replaces GET /api/test-cases/storage-providers
     */
    @QueryMapping
    public TestCaseCollection getStorageProvidersTestCases() {
<span class="nc" id="L176">        log.info(&quot;GraphQL getStorageProvidersTestCases called&quot;);</span>
<span class="nc" id="L177">        return testCaseService.getTestCasesByCategory(&quot;Storage_Providers&quot;);</span>
    }

    /**
     * Get all test cases for Search Filter category.
     * Implements getSearchFilterTestCases query - replaces GET /api/test-cases/search-filter
     */
    @QueryMapping
    public TestCaseCollection getSearchFilterTestCases() {
<span class="nc" id="L186">        log.info(&quot;GraphQL getSearchFilterTestCases called&quot;);</span>
<span class="nc" id="L187">        return testCaseService.getTestCasesByCategory(&quot;Search_Filter&quot;);</span>
    }

    /**
     * Get all test cases for Audit Logs category.
     * Implements getAuditLogsTestCases query - replaces GET /api/test-cases/audit-logs
     */
    @QueryMapping
    public TestCaseCollection getAuditLogsTestCases() {
<span class="nc" id="L196">        log.info(&quot;GraphQL getAuditLogsTestCases called&quot;);</span>
<span class="nc" id="L197">        return testCaseService.getTestCasesByCategory(&quot;Audit_Logs&quot;);</span>
    }

    /**
     * Get all test cases for Security Validation category.
     * Implements getSecurityValidationTestCases query - replaces GET /api/test-cases/security-validation
     */
    @QueryMapping
    public TestCaseCollection getSecurityValidationTestCases() {
<span class="nc" id="L206">        log.info(&quot;GraphQL getSecurityValidationTestCases called&quot;);</span>
<span class="nc" id="L207">        return testCaseService.getTestCasesByCategory(&quot;Security_Validation&quot;);</span>
    }

    /**
     * Get all test cases for Performance category.
     * Implements getPerformanceTestCases query - replaces GET /api/test-cases/performance
     */
    @QueryMapping
    public TestCaseCollection getPerformanceTestCases() {
<span class="nc" id="L216">        log.info(&quot;GraphQL getPerformanceTestCases called&quot;);</span>
<span class="nc" id="L217">        return testCaseService.getTestCasesByCategory(&quot;Performance&quot;);</span>
    }

    /**
     * Get all test cases for Integration category.
     * Implements getIntegrationTestCases query - replaces GET /api/test-cases/integration
     */
    @QueryMapping
    public TestCaseCollection getIntegrationTestCases() {
<span class="nc" id="L226">        log.info(&quot;GraphQL getIntegrationTestCases called&quot;);</span>
<span class="nc" id="L227">        return testCaseService.getTestCasesByCategory(&quot;Integration&quot;);</span>
    }

    /**
     * Get all test cases for Boundary Tests category.
     * Implements getBoundaryTestsTestCases query - replaces GET /api/test-cases/boundary-tests
     */
    @QueryMapping
    public TestCaseCollection getBoundaryTestsTestCases() {
<span class="nc" id="L236">        log.info(&quot;GraphQL getBoundaryTestsTestCases called&quot;);</span>
<span class="nc" id="L237">        return testCaseService.getTestCasesByCategory(&quot;Boundary_Tests&quot;);</span>
    }

    // ===== INDIVIDUAL TEST CASE QUERIES =====

    /**
     * Get individual test case by category and test ID.
     * Implements getTestCase query - replaces GET /api/test-cases/{category}/{testId}
     */
    @QueryMapping
    public TestCaseResponse getTestCase(@Argument String category, @Argument String testId) {
<span class="nc" id="L248">        log.info(&quot;GraphQL getTestCase called for category: {}, testId: {}&quot;, category, testId);</span>
<span class="nc" id="L249">        return testCaseService.getTestCaseById(category, testId);</span>
    }

    // ===== SEARCH AND UTILITY QUERIES =====

    /**
     * Search test cases across all categories.
     * Implements searchTestCases query - replaces GET /api/test-cases/search?q={query}
     */
    @QueryMapping
    public Map&lt;String, Object&gt; searchTestCases(@Argument String query) {
<span class="nc" id="L260">        log.info(&quot;GraphQL searchTestCases called with query: {}&quot;, query);</span>
        
<span class="nc" id="L262">        List&lt;TestCaseResponse&gt; results = testCaseService.searchTestCases(query);</span>
        
<span class="nc" id="L264">        Map&lt;String, Object&gt; response = new HashMap&lt;&gt;();</span>
<span class="nc" id="L265">        response.put(&quot;query&quot;, query);</span>
<span class="nc" id="L266">        response.put(&quot;totalResults&quot;, results.size());</span>
<span class="nc" id="L267">        response.put(&quot;testCases&quot;, results);</span>
        
<span class="nc" id="L269">        return response;</span>
    }

    /**
     * Health check for test case service.
     * Implements testCaseHealthCheck query - replaces GET /api/test-cases/health
     */
    @QueryMapping
    public Map&lt;String, Object&gt; testCaseHealthCheck() {
<span class="nc" id="L278">        log.info(&quot;GraphQL testCaseHealthCheck called&quot;);</span>
        
<span class="nc" id="L280">        Map&lt;String, Object&gt; health = new HashMap&lt;&gt;();</span>
<span class="nc" id="L281">        health.put(&quot;status&quot;, &quot;UP&quot;);</span>
<span class="nc" id="L282">        health.put(&quot;service&quot;, &quot;Test Case GraphQL API&quot;);</span>
<span class="nc" id="L283">        health.put(&quot;timestamp&quot;, LocalDateTime.now());</span>
        
<span class="nc" id="L285">        List&lt;String&gt; categories = testCaseService.getAllCategories();</span>
<span class="nc" id="L286">        health.put(&quot;availableCategories&quot;, categories.size());</span>
<span class="nc" id="L287">        health.put(&quot;categories&quot;, categories);</span>
        
<span class="nc" id="L289">        return health;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>