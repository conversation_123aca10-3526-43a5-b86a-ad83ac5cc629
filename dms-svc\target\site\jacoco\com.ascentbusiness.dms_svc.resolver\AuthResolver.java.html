<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>AuthResolver.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.resolver</a> &gt; <span class="el_source">AuthResolver.java</span></div><h1>AuthResolver.java</h1><pre class="source lang-java linenums">package com.ascentbusiness.dms_svc.resolver;

import com.ascentbusiness.dms_svc.dto.JwtTokenRequest;
import com.ascentbusiness.dms_svc.dto.JwtTokenResponse;
import com.ascentbusiness.dms_svc.service.AuthService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.graphql.data.method.annotation.Argument;
import org.springframework.graphql.data.method.annotation.MutationMapping;
import org.springframework.stereotype.Controller;

/**
 * GraphQL resolver for authentication and authorization operations.
 *
 * &lt;p&gt;This resolver provides GraphQL mutations for authentication-related operations
 * including JWT token generation for testing and development purposes. It serves as
 * the GraphQL interface for authentication services in the DMS system.&lt;/p&gt;
 *
 * &lt;p&gt;Key features:&lt;/p&gt;
 * &lt;ul&gt;
 *   &lt;li&gt;&lt;strong&gt;Test Token Generation&lt;/strong&gt;: Creates JWT tokens for testing purposes&lt;/li&gt;
 *   &lt;li&gt;&lt;strong&gt;GraphQL Integration&lt;/strong&gt;: Provides GraphQL mutations for auth operations&lt;/li&gt;
 *   &lt;li&gt;&lt;strong&gt;Development Support&lt;/strong&gt;: Facilitates testing and development workflows&lt;/li&gt;
 * &lt;/ul&gt;
 *
 * &lt;p&gt;This resolver is primarily intended for development and testing environments.
 * Production authentication should use proper OAuth2 or similar authentication flows.&lt;/p&gt;
 *
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 1.0.0
 * @see AuthService
 * @see JwtTokenRequest
 * @see JwtTokenResponse
 */
@Controller
<span class="nc" id="L36">public class AuthResolver {</span>

    @Autowired
    private AuthService authService;

    /**
     * Generates a test JWT token for development and testing purposes.
     *
     * &lt;p&gt;This GraphQL mutation creates a JWT token based on the provided request
     * parameters. It is intended for testing and development environments to
     * facilitate API testing without requiring a full authentication flow.&lt;/p&gt;
     *
     * @param input the JWT token request containing user information and token parameters
     * @return a JWT token response with the generated token and metadata
     */
    @MutationMapping
    public JwtTokenResponse generateTestToken(@Argument JwtTokenRequest input) {
<span class="nc" id="L53">        return authService.generateTestToken(input);</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>