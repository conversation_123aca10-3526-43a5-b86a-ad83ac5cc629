<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>com.ascentbusiness.dms_svc.security</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.source.html" class="el_source">Source Files</a><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <span class="el_package">com.ascentbusiness.dms_svc.security</span></div><h1>com.ascentbusiness.dms_svc.security</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">803 of 803</td><td class="ctr2">0%</td><td class="bar">74 of 74</td><td class="ctr2">0%</td><td class="ctr1">89</td><td class="ctr2">89</td><td class="ctr1">225</td><td class="ctr2">225</td><td class="ctr1">52</td><td class="ctr2">52</td><td class="ctr1">5</td><td class="ctr2">5</td></tr></tfoot><tbody><tr><td id="a1"><a href="JwtAuthenticationFilter.html" class="el_class">JwtAuthenticationFilter</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="260" alt="260"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="34" alt="34"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f1">22</td><td class="ctr2" id="g1">22</td><td class="ctr1" id="h1">64</td><td class="ctr2" id="i1">64</td><td class="ctr1" id="j3">5</td><td class="ctr2" id="k3">5</td><td class="ctr1" id="l0">1</td><td class="ctr2" id="m0">1</td></tr><tr><td id="a2"><a href="JwtTokenProvider.html" class="el_class">JwtTokenProvider</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="103" height="10" title="224" alt="224"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="14" height="10" title="4" alt="4"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f3">13</td><td class="ctr2" id="g3">13</td><td class="ctr1" id="h0">74</td><td class="ctr2" id="i0">74</td><td class="ctr1" id="j2">11</td><td class="ctr2" id="k2">11</td><td class="ctr1" id="l1">1</td><td class="ctr2" id="m1">1</td></tr><tr><td id="a3"><a href="UserContext.html" class="el_class">UserContext</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="97" height="10" title="212" alt="212"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="112" height="10" title="32" alt="32"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f0">34</td><td class="ctr2" id="g0">34</td><td class="ctr1" id="h2">56</td><td class="ctr2" id="i2">56</td><td class="ctr1" id="j0">18</td><td class="ctr2" id="k0">18</td><td class="ctr1" id="l2">1</td><td class="ctr2" id="m2">1</td></tr><tr><td id="a4"><a href="UserPrincipal.html" class="el_class">UserPrincipal</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="41" height="10" title="90" alt="90"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="14" height="10" title="4" alt="4"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f2">17</td><td class="ctr2" id="g2">17</td><td class="ctr1" id="h3">26</td><td class="ctr2" id="i3">26</td><td class="ctr1" id="j1">15</td><td class="ctr2" id="k1">15</td><td class="ctr1" id="l3">1</td><td class="ctr2" id="m3">1</td></tr><tr><td id="a0"><a href="JwtAuthenticationEntryPoint.html" class="el_class">JwtAuthenticationEntryPoint</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="17" alt="17"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d4"/><td class="ctr2" id="e4">n/a</td><td class="ctr1" id="f4">3</td><td class="ctr2" id="g4">3</td><td class="ctr1" id="h4">5</td><td class="ctr2" id="i4">5</td><td class="ctr1" id="j4">3</td><td class="ctr2" id="k4">3</td><td class="ctr1" id="l4">1</td><td class="ctr2" id="m4">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>