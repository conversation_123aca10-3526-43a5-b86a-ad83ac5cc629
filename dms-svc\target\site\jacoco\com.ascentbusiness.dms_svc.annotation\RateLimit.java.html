<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>RateLimit.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.annotation</a> &gt; <span class="el_source">RateLimit.java</span></div><h1>RateLimit.java</h1><pre class="source lang-java linenums">package com.ascentbusiness.dms_svc.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Annotation for rate limiting with Redis-based distributed support
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface RateLimit {

    /**
     * Maximum number of requests allowed
     */
    int value() default 5;

    /**
     * Time window in seconds
     */
    int window() default 60;

    /**
     * Key for rate limiting (defaults to user ID)
     * Supported values: &quot;user&quot;, &quot;ip&quot;, &quot;global&quot;, or custom key
     */
    String key() default &quot;user&quot;;

    /**
     * Rate limit type for different operations
     */
    RateLimitType type() default RateLimitType.GENERAL;

    /**
     * Whether to use Redis for distributed rate limiting
     */
    boolean distributed() default true;

    /**
     * Custom error message when rate limit is exceeded
     */
    String message() default &quot;Rate limit exceeded. Please try again later.&quot;;

    /**
     * Rate limit types for different operations
     */
<span class="nc" id="L49">    enum RateLimitType {</span>
<span class="nc" id="L50">        GENERAL,</span>
<span class="nc" id="L51">        GRAPHQL_QUERY,</span>
<span class="nc" id="L52">        GRAPHQL_MUTATION,</span>
<span class="nc" id="L53">        DOCUMENT_UPLOAD,</span>
<span class="nc" id="L54">        DOCUMENT_DOWNLOAD,</span>
<span class="nc" id="L55">        PERMISSION_OPERATION,</span>
<span class="nc" id="L56">        AUTHENTICATION,</span>
<span class="nc" id="L57">        SEARCH_OPERATION,</span>
<span class="nc" id="L58">        DOCUMENT_OPERATION,</span>
<span class="nc" id="L59">        BULK_OPERATION</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>