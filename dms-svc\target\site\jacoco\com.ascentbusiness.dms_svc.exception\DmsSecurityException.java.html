<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>DmsSecurityException.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.exception</a> &gt; <span class="el_source">DmsSecurityException.java</span></div><h1>DmsSecurityException.java</h1><pre class="source lang-java linenums">package com.ascentbusiness.dms_svc.exception;

import java.util.Map;

/**
 * Exception for security-related errors in the DMS system.
 * Used for authentication, authorization, and security violation errors.
 */
public class DmsSecurityException extends DmsException {

    public DmsSecurityException(String message, String errorCode, DmsException.ErrorCategory category) {
<span class="nc" id="L12">        super(message, errorCode, category, DmsException.ErrorSeverity.HIGH);</span>
<span class="nc" id="L13">    }</span>

    public DmsSecurityException(String message, String errorCode, DmsException.ErrorCategory category, DmsException.ErrorSeverity severity) {
<span class="nc" id="L16">        super(message, errorCode, category, severity);</span>
<span class="nc" id="L17">    }</span>

    public DmsSecurityException(String message, String errorCode, DmsException.ErrorCategory category, Map&lt;String, Object&gt; errorDetails) {
<span class="nc" id="L20">        super(message, errorCode, category, DmsException.ErrorSeverity.HIGH, errorDetails);</span>
<span class="nc" id="L21">    }</span>

    public DmsSecurityException(String message, String errorCode, DmsException.ErrorCategory category,
                               Map&lt;String, Object&gt; errorDetails, Throwable cause) {
<span class="nc" id="L25">        super(message, errorCode, category, DmsException.ErrorSeverity.HIGH, errorDetails, cause);</span>
<span class="nc" id="L26">    }</span>

    public DmsSecurityException(String message, String errorCode, DmsException.ErrorCategory category,
                               DmsException.ErrorSeverity severity, Map&lt;String, Object&gt; errorDetails) {
<span class="nc" id="L30">        super(message, errorCode, category, severity, errorDetails);</span>
<span class="nc" id="L31">    }</span>

    // Authentication errors
    public static DmsSecurityException invalidToken(String reason) {
<span class="nc" id="L35">        return new DmsSecurityException(</span>
<span class="nc" id="L36">            String.format(&quot;Invalid authentication token: %s&quot;, reason),</span>
            &quot;INVALID_TOKEN&quot;,
            DmsException.ErrorCategory.AUTHENTICATION,
<span class="nc" id="L39">            Map.of(&quot;reason&quot;, reason)</span>
        );
    }

    public static DmsSecurityException tokenExpired() {
<span class="nc" id="L44">        return new DmsSecurityException(</span>
            &quot;Authentication token has expired&quot;,
            &quot;TOKEN_EXPIRED&quot;,
            DmsException.ErrorCategory.AUTHENTICATION
        );
    }

    public static DmsSecurityException authenticationRequired() {
<span class="nc" id="L52">        return new DmsSecurityException(</span>
            &quot;Authentication is required to access this resource&quot;,
            &quot;AUTHENTICATION_REQUIRED&quot;,
            DmsException.ErrorCategory.AUTHENTICATION,
            DmsException.ErrorSeverity.MEDIUM
        );
    }

    public static DmsSecurityException invalidCredentials() {
<span class="nc" id="L61">        return new DmsSecurityException(</span>
            &quot;Invalid username or password&quot;,
            &quot;INVALID_CREDENTIALS&quot;,
            DmsException.ErrorCategory.AUTHENTICATION
        );
    }

    // Authorization errors
    public static DmsSecurityException accessDenied(String resource, String action) {
<span class="nc" id="L70">        return new DmsSecurityException(</span>
<span class="nc" id="L71">            String.format(&quot;Access denied: insufficient permissions to %s on %s&quot;, action, resource),</span>
            &quot;ACCESS_DENIED&quot;,
            DmsException.ErrorCategory.AUTHORIZATION,
<span class="nc" id="L74">            Map.of(&quot;resource&quot;, resource, &quot;action&quot;, action)</span>
        );
    }

    public static DmsSecurityException insufficientPermissions(String requiredPermission, String currentPermissions) {
<span class="nc" id="L79">        return new DmsSecurityException(</span>
<span class="nc" id="L80">            String.format(&quot;Insufficient permissions. Required: %s, Current: %s&quot;,</span>
                         requiredPermission, currentPermissions),
            &quot;INSUFFICIENT_PERMISSIONS&quot;,
            DmsException.ErrorCategory.AUTHORIZATION,
<span class="nc" id="L84">            Map.of(&quot;requiredPermission&quot;, requiredPermission, &quot;currentPermissions&quot;, currentPermissions)</span>
        );
    }

    public static DmsSecurityException privilegeEscalation(String userId, String attemptedAction) {
<span class="nc" id="L89">        return new DmsSecurityException(</span>
<span class="nc" id="L90">            String.format(&quot;Privilege escalation attempt detected: User %s attempted %s&quot;, userId, attemptedAction),</span>
            &quot;PRIVILEGE_ESCALATION&quot;,
            DmsException.ErrorCategory.AUTHORIZATION,
            DmsException.ErrorSeverity.CRITICAL,
<span class="nc" id="L94">            Map.of(&quot;userId&quot;, userId, &quot;attemptedAction&quot;, attemptedAction)</span>
        );
    }

    public static DmsSecurityException roleNotFound(String roleName) {
<span class="nc" id="L99">        return new DmsSecurityException(</span>
<span class="nc" id="L100">            String.format(&quot;Role not found: %s&quot;, roleName),</span>
            &quot;ROLE_NOT_FOUND&quot;,
            DmsException.ErrorCategory.AUTHORIZATION,
<span class="nc" id="L103">            Map.of(&quot;roleName&quot;, roleName)</span>
        );
    }

    // Security violations
    public static DmsSecurityException inputValidationViolation(String fieldName, String violationType) {
<span class="nc" id="L109">        return new DmsSecurityException(</span>
<span class="nc" id="L110">            String.format(&quot;Security violation in input validation: %s detected in field %s&quot;,</span>
                         violationType, fieldName),
            &quot;INPUT_VALIDATION_VIOLATION&quot;,
            DmsException.ErrorCategory.VALIDATION,
            DmsException.ErrorSeverity.HIGH,
<span class="nc" id="L115">            Map.of(&quot;fieldName&quot;, fieldName, &quot;violationType&quot;, violationType)</span>
        );
    }

    public static DmsSecurityException rateLimitExceeded(String userId, String operation, int limit) {
<span class="nc" id="L120">        return new DmsSecurityException(</span>
<span class="nc" id="L121">            String.format(&quot;Rate limit exceeded for user %s on operation %s (limit: %d)&quot;,</span>
<span class="nc" id="L122">                         userId, operation, limit),</span>
            &quot;RATE_LIMIT_EXCEEDED&quot;,
            DmsException.ErrorCategory.AUTHORIZATION,
            DmsException.ErrorSeverity.MEDIUM,
<span class="nc" id="L126">            Map.of(&quot;userId&quot;, userId, &quot;operation&quot;, operation, &quot;limit&quot;, limit)</span>
        );
    }

    public static DmsSecurityException suspiciousActivity(String userId, String activityType, String details) {
<span class="nc" id="L131">        return new DmsSecurityException(</span>
<span class="nc" id="L132">            String.format(&quot;Suspicious activity detected: User %s, Activity: %s, Details: %s&quot;,</span>
                         userId, activityType, details),
            &quot;SUSPICIOUS_ACTIVITY&quot;,
            DmsException.ErrorCategory.AUTHORIZATION,
            DmsException.ErrorSeverity.CRITICAL,
<span class="nc" id="L137">            Map.of(&quot;userId&quot;, userId, &quot;activityType&quot;, activityType, &quot;details&quot;, details)</span>
        );
    }

    public static DmsSecurityException dataIntegrityViolation(String dataType, String violationType) {
<span class="nc" id="L142">        return new DmsSecurityException(</span>
<span class="nc" id="L143">            String.format(&quot;Data integrity violation: %s in %s&quot;, violationType, dataType),</span>
            &quot;DATA_INTEGRITY_VIOLATION&quot;,
            DmsException.ErrorCategory.DATA_INTEGRITY,
            DmsException.ErrorSeverity.CRITICAL,
<span class="nc" id="L147">            Map.of(&quot;dataType&quot;, dataType, &quot;violationType&quot;, violationType)</span>
        );
    }

    public static DmsSecurityException encryptionError(String operation, String reason) {
<span class="nc" id="L152">        return new DmsSecurityException(</span>
<span class="nc" id="L153">            String.format(&quot;Encryption error during %s: %s&quot;, operation, reason),</span>
            &quot;ENCRYPTION_ERROR&quot;,
            DmsException.ErrorCategory.SYSTEM,
            DmsException.ErrorSeverity.CRITICAL,
<span class="nc" id="L157">            Map.of(&quot;operation&quot;, operation, &quot;reason&quot;, reason)</span>
        );
    }

    public static DmsSecurityException auditViolation(String violationType, String details) {
<span class="nc" id="L162">        return new DmsSecurityException(</span>
<span class="nc" id="L163">            String.format(&quot;Audit violation: %s - %s&quot;, violationType, details),</span>
            &quot;AUDIT_VIOLATION&quot;,
            DmsException.ErrorCategory.DATA_INTEGRITY,
            DmsException.ErrorSeverity.CRITICAL,
<span class="nc" id="L167">            Map.of(&quot;violationType&quot;, violationType, &quot;details&quot;, details)</span>
        );
    }

    // External service security errors
    public static DmsSecurityException externalServiceAuthFailure(String serviceName, String reason) {
<span class="nc" id="L173">        return new DmsSecurityException(</span>
<span class="nc" id="L174">            String.format(&quot;Authentication failure with external service %s: %s&quot;, serviceName, reason),</span>
            &quot;EXTERNAL_SERVICE_AUTH_FAILURE&quot;,
            DmsException.ErrorCategory.EXTERNAL_SERVICE,
<span class="nc" id="L177">            Map.of(&quot;serviceName&quot;, serviceName, &quot;reason&quot;, reason)</span>
        );
    }

    public static DmsSecurityException certificateError(String certificateType, String error) {
<span class="nc" id="L182">        return new DmsSecurityException(</span>
<span class="nc" id="L183">            String.format(&quot;Certificate error for %s: %s&quot;, certificateType, error),</span>
            &quot;CERTIFICATE_ERROR&quot;,
            DmsException.ErrorCategory.SYSTEM,
            DmsException.ErrorSeverity.CRITICAL,
<span class="nc" id="L187">            Map.of(&quot;certificateType&quot;, certificateType, &quot;error&quot;, error)</span>
        );
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>