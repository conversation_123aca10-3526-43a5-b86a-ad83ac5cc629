<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>com.ascentbusiness.dms_svc.exception</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.html" class="el_class">Classes</a><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <span class="el_package">com.ascentbusiness.dms_svc.exception</span></div><h1>com.ascentbusiness.dms_svc.exception</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">2,701 of 2,877</td><td class="ctr2">6%</td><td class="bar">71 of 72</td><td class="ctr2">1%</td><td class="ctr1">223</td><td class="ctr2">231</td><td class="ctr1">514</td><td class="ctr2">550</td><td class="ctr1">184</td><td class="ctr2">192</td><td class="ctr1">21</td><td class="ctr2">25</td></tr></tfoot><tbody><tr><td id="a4"><a href="DmsSystemException.java.html" class="el_source">DmsSystemException.java</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="541" alt="541"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d7"/><td class="ctr2" id="e7">n/a</td><td class="ctr1" id="f2">24</td><td class="ctr2" id="g2">24</td><td class="ctr1" id="h0">68</td><td class="ctr2" id="i1">68</td><td class="ctr1" id="j0">24</td><td class="ctr2" id="k0">24</td><td class="ctr1" id="l0">1</td><td class="ctr2" id="m1">1</td></tr><tr><td id="a3"><a href="DmsSecurityException.java.html" class="el_source">DmsSecurityException.java</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="82" height="10" title="370" alt="370"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d8"/><td class="ctr2" id="e8">n/a</td><td class="ctr1" id="f3">21</td><td class="ctr2" id="g3">21</td><td class="ctr1" id="h2">53</td><td class="ctr2" id="i3">53</td><td class="ctr1" id="j1">21</td><td class="ctr2" id="k1">21</td><td class="ctr1" id="l1">1</td><td class="ctr2" id="m2">1</td></tr><tr><td id="a1"><a href="DmsBusinessException.java.html" class="el_source">DmsBusinessException.java</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="73" height="10" title="333" alt="333"/><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="15" alt="15"/></td><td class="ctr2" id="c1">4%</td><td class="bar" id="d9"/><td class="ctr2" id="e9">n/a</td><td class="ctr1" id="f4">16</td><td class="ctr2" id="g4">18</td><td class="ctr1" id="h4">46</td><td class="ctr2" id="i4">50</td><td class="ctr1" id="j2">16</td><td class="ctr2" id="k3">18</td><td class="ctr1" id="l21">0</td><td class="ctr2" id="m3">1</td></tr><tr><td id="a5"><a href="DmsValidationException.java.html" class="el_source">DmsValidationException.java</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="60" height="10" title="272" alt="272"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d6"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="2" alt="2"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f5">15</td><td class="ctr2" id="g5">15</td><td class="ctr1" id="h5">39</td><td class="ctr2" id="i5">39</td><td class="ctr1" id="j4">14</td><td class="ctr2" id="k5">14</td><td class="ctr1" id="l2">1</td><td class="ctr2" id="m4">1</td></tr><tr><td id="a21"><a href="VirusScanException.java.html" class="el_source">VirusScanException.java</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="58" height="10" title="265" alt="265"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="32" alt="32"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f0">33</td><td class="ctr2" id="g0">33</td><td class="ctr1" id="h1">55</td><td class="ctr2" id="i2">55</td><td class="ctr1" id="j3">15</td><td class="ctr2" id="k4">15</td><td class="ctr1" id="l3">1</td><td class="ctr2" id="m5">1</td></tr><tr><td id="a2"><a href="DmsException.java.html" class="el_source">DmsException.java</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="49" height="10" title="222" alt="222"/><img src="../jacoco-resources/greenbar.gif" width="35" height="10" title="161" alt="161"/></td><td class="ctr2" id="c0">42%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="78" height="10" title="21" alt="21"/><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="1" alt="1"/></td><td class="ctr2" id="e0">4%</td><td class="ctr1" id="f1">26</td><td class="ctr2" id="g1">32</td><td class="ctr1" id="h3">49</td><td class="ctr2" id="i0">81</td><td class="ctr1" id="j5">14</td><td class="ctr2" id="k2">20</td><td class="ctr1" id="l22">0</td><td class="ctr2" id="m0">3</td></tr><tr><td id="a22"><a href="WordConversionException.java.html" class="el_source">WordConversionException.java</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="17" height="10" title="81" alt="81"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="4" alt="4"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f7">9</td><td class="ctr2" id="g7">9</td><td class="ctr1" id="h7">24</td><td class="ctr2" id="i7">24</td><td class="ctr1" id="j9">7</td><td class="ctr2" id="k9">7</td><td class="ctr1" id="l4">1</td><td class="ctr2" id="m6">1</td></tr><tr><td id="a16"><a href="SecurityViolationException.java.html" class="el_source">SecurityViolationException.java</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="16" height="10" title="73" alt="73"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d10"/><td class="ctr2" id="e10">n/a</td><td class="ctr1" id="f10">8</td><td class="ctr2" id="g10">8</td><td class="ctr1" id="h6">26</td><td class="ctr2" id="i6">26</td><td class="ctr1" id="j8">8</td><td class="ctr2" id="k8">8</td><td class="ctr1" id="l5">1</td><td class="ctr2" id="m7">1</td></tr><tr><td id="a13"><a href="MarkdownConversionException.java.html" class="el_source">MarkdownConversionException.java</a></td><td class="bar" id="b8"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="71" alt="71"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="4" alt="4"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f11">8</td><td class="ctr2" id="g11">8</td><td class="ctr1" id="h9">20</td><td class="ctr2" id="i9">20</td><td class="ctr1" id="j11">6</td><td class="ctr2" id="k11">6</td><td class="ctr1" id="l6">1</td><td class="ctr2" id="m8">1</td></tr><tr><td id="a14"><a href="PdfConversionException.java.html" class="el_source">PdfConversionException.java</a></td><td class="bar" id="b9"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="71" alt="71"/></td><td class="ctr2" id="c9">0%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="4" alt="4"/></td><td class="ctr2" id="e5">0%</td><td class="ctr1" id="f12">8</td><td class="ctr2" id="g12">8</td><td class="ctr1" id="h10">20</td><td class="ctr2" id="i10">20</td><td class="ctr1" id="j12">6</td><td class="ctr2" id="k12">6</td><td class="ctr1" id="l7">1</td><td class="ctr2" id="m9">1</td></tr><tr><td id="a11"><a href="InvalidShareLinkException.java.html" class="el_source">InvalidShareLinkException.java</a></td><td class="bar" id="b10"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="69" alt="69"/></td><td class="ctr2" id="c10">0%</td><td class="bar" id="d11"/><td class="ctr2" id="e11">n/a</td><td class="ctr1" id="f8">9</td><td class="ctr2" id="g8">9</td><td class="ctr1" id="h12">13</td><td class="ctr2" id="i12">13</td><td class="ctr1" id="j7">9</td><td class="ctr2" id="k7">9</td><td class="ctr1" id="l8">1</td><td class="ctr2" id="m10">1</td></tr><tr><td id="a9"><a href="FileTooLargeException.java.html" class="el_source">FileTooLargeException.java</a></td><td class="bar" id="b11"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="63" alt="63"/></td><td class="ctr2" id="c11">0%</td><td class="bar" id="d5"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="4" alt="4"/></td><td class="ctr2" id="e6">0%</td><td class="ctr1" id="f9">9</td><td class="ctr2" id="g9">9</td><td class="ctr1" id="h11">19</td><td class="ctr2" id="i11">19</td><td class="ctr1" id="j10">7</td><td class="ctr2" id="k10">7</td><td class="ctr1" id="l9">1</td><td class="ctr2" id="m11">1</td></tr><tr><td id="a7"><a href="DuplicateFileException.java.html" class="el_source">DuplicateFileException.java</a></td><td class="bar" id="b12"><img src="../jacoco-resources/redbar.gif" width="12" height="10" title="56" alt="56"/></td><td class="ctr2" id="c12">0%</td><td class="bar" id="d12"/><td class="ctr2" id="e12">n/a</td><td class="ctr1" id="f13">4</td><td class="ctr2" id="g13">4</td><td class="ctr1" id="h13">10</td><td class="ctr2" id="i13">10</td><td class="ctr1" id="j13">4</td><td class="ctr2" id="k13">4</td><td class="ctr1" id="l10">1</td><td class="ctr2" id="m12">1</td></tr><tr><td id="a0"><a href="ComplianceViolationException.java.html" class="el_source">ComplianceViolationException.java</a></td><td class="bar" id="b13"><img src="../jacoco-resources/redbar.gif" width="11" height="10" title="53" alt="53"/></td><td class="ctr2" id="c13">0%</td><td class="bar" id="d13"/><td class="ctr2" id="e13">n/a</td><td class="ctr1" id="f6">10</td><td class="ctr2" id="g6">10</td><td class="ctr1" id="h8">22</td><td class="ctr2" id="i8">22</td><td class="ctr1" id="j6">10</td><td class="ctr2" id="k6">10</td><td class="ctr1" id="l11">1</td><td class="ctr2" id="m13">1</td></tr><tr><td id="a6"><a href="DocumentNotFoundException.java.html" class="el_source">DocumentNotFoundException.java</a></td><td class="bar" id="b14"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="48" alt="48"/></td><td class="ctr2" id="c14">0%</td><td class="bar" id="d14"/><td class="ctr2" id="e14">n/a</td><td class="ctr1" id="f14">4</td><td class="ctr2" id="g14">4</td><td class="ctr1" id="h14">10</td><td class="ctr2" id="i14">10</td><td class="ctr1" id="j14">4</td><td class="ctr2" id="k14">4</td><td class="ctr1" id="l12">1</td><td class="ctr2" id="m14">1</td></tr><tr><td id="a20"><a href="UnauthorizedException.java.html" class="el_source">UnauthorizedException.java</a></td><td class="bar" id="b15"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="47" alt="47"/></td><td class="ctr2" id="c15">0%</td><td class="bar" id="d15"/><td class="ctr2" id="e15">n/a</td><td class="ctr1" id="f15">4</td><td class="ctr2" id="g15">4</td><td class="ctr1" id="h15">10</td><td class="ctr2" id="i15">10</td><td class="ctr1" id="j15">4</td><td class="ctr2" id="k15">4</td><td class="ctr1" id="l13">1</td><td class="ctr2" id="m15">1</td></tr><tr><td id="a18"><a href="SharePointException.java.html" class="el_source">SharePointException.java</a></td><td class="bar" id="b16"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="13" alt="13"/></td><td class="ctr2" id="c16">0%</td><td class="bar" id="d16"/><td class="ctr2" id="e16">n/a</td><td class="ctr1" id="f16">3</td><td class="ctr2" id="g16">3</td><td class="ctr1" id="h16">6</td><td class="ctr2" id="i16">6</td><td class="ctr1" id="j16">3</td><td class="ctr2" id="k16">3</td><td class="ctr1" id="l14">1</td><td class="ctr2" id="m16">1</td></tr><tr><td id="a17"><a href="SharePointAuthenticationException.java.html" class="el_source">SharePointAuthenticationException.java</a></td><td class="bar" id="b17"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="13" alt="13"/></td><td class="ctr2" id="c17">0%</td><td class="bar" id="d17"/><td class="ctr2" id="e17">n/a</td><td class="ctr1" id="f17">3</td><td class="ctr2" id="g17">3</td><td class="ctr1" id="h17">6</td><td class="ctr2" id="i17">6</td><td class="ctr1" id="j17">3</td><td class="ctr2" id="k17">3</td><td class="ctr1" id="l15">1</td><td class="ctr2" id="m17">1</td></tr><tr><td id="a8"><a href="DuplicateResourceException.java.html" class="el_source">DuplicateResourceException.java</a></td><td class="bar" id="b18"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="9" alt="9"/></td><td class="ctr2" id="c18">0%</td><td class="bar" id="d18"/><td class="ctr2" id="e18">n/a</td><td class="ctr1" id="f18">2</td><td class="ctr2" id="g18">2</td><td class="ctr1" id="h18">4</td><td class="ctr2" id="i18">4</td><td class="ctr1" id="j18">2</td><td class="ctr2" id="k18">2</td><td class="ctr1" id="l16">1</td><td class="ctr2" id="m18">1</td></tr><tr><td id="a12"><a href="InvalidTokenException.java.html" class="el_source">InvalidTokenException.java</a></td><td class="bar" id="b19"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="9" alt="9"/></td><td class="ctr2" id="c19">0%</td><td class="bar" id="d19"/><td class="ctr2" id="e19">n/a</td><td class="ctr1" id="f19">2</td><td class="ctr2" id="g19">2</td><td class="ctr1" id="h19">4</td><td class="ctr2" id="i19">4</td><td class="ctr1" id="j19">2</td><td class="ctr2" id="k19">2</td><td class="ctr1" id="l17">1</td><td class="ctr2" id="m19">1</td></tr><tr><td id="a15"><a href="ResourceNotFoundException.java.html" class="el_source">ResourceNotFoundException.java</a></td><td class="bar" id="b20"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="9" alt="9"/></td><td class="ctr2" id="c20">0%</td><td class="bar" id="d20"/><td class="ctr2" id="e20">n/a</td><td class="ctr1" id="f20">2</td><td class="ctr2" id="g20">2</td><td class="ctr1" id="h20">4</td><td class="ctr2" id="i20">4</td><td class="ctr1" id="j20">2</td><td class="ctr2" id="k20">2</td><td class="ctr1" id="l18">1</td><td class="ctr2" id="m20">1</td></tr><tr><td id="a19"><a href="StorageMigrationException.java.html" class="el_source">StorageMigrationException.java</a></td><td class="bar" id="b21"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="9" alt="9"/></td><td class="ctr2" id="c21">0%</td><td class="bar" id="d21"/><td class="ctr2" id="e21">n/a</td><td class="ctr1" id="f21">2</td><td class="ctr2" id="g21">2</td><td class="ctr1" id="h21">4</td><td class="ctr2" id="i21">4</td><td class="ctr1" id="j21">2</td><td class="ctr2" id="k21">2</td><td class="ctr1" id="l19">1</td><td class="ctr2" id="m21">1</td></tr><tr><td id="a10"><a href="HistoricalDocumentException.java.html" class="el_source">HistoricalDocumentException.java</a></td><td class="bar" id="b22"/><td class="ctr2" id="c22">0%</td><td class="bar" id="d22"/><td class="ctr2" id="e22">n/a</td><td class="ctr1" id="f22">1</td><td class="ctr2" id="g22">1</td><td class="ctr1" id="h22">2</td><td class="ctr2" id="i22">2</td><td class="ctr1" id="j22">1</td><td class="ctr2" id="k22">1</td><td class="ctr1" id="l20">1</td><td class="ctr2" id="m22">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>