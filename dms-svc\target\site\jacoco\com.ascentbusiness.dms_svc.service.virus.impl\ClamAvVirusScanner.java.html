<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>ClamAvVirusScanner.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.service.virus.impl</a> &gt; <span class="el_source">ClamAvVirusScanner.java</span></div><h1>ClamAvVirusScanner.java</h1><pre class="source lang-java linenums">package com.ascentbusiness.dms_svc.service.virus.impl;

import com.ascentbusiness.dms_svc.enums.VirusScanResult;
import com.ascentbusiness.dms_svc.enums.VirusScannerType;
import com.ascentbusiness.dms_svc.service.virus.AbstractVirusScanner;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.io.*;
import java.net.Socket;
import java.nio.ByteBuffer;
import java.util.ArrayList;
import java.util.List;

/**
 * ClamAV virus scanner implementation.
 * 
 * &lt;p&gt;This scanner integrates with ClamAV antivirus engine through the clamd daemon.
 * ClamAV is an open-source antivirus engine for detecting trojans, viruses, malware
 * and other malicious threats.
 * 
 * &lt;p&gt;The implementation communicates with clamd using the INSTREAM command to scan
 * file content directly without writing temporary files to disk. This approach is
 * more secure and efficient for web applications.
 * 
 * &lt;p&gt;Configuration properties:
 * &lt;ul&gt;
 *   &lt;li&gt;dms.virus-scanner.clamav.host - ClamAV daemon host (default: localhost)&lt;/li&gt;
 *   &lt;li&gt;dms.virus-scanner.clamav.port - ClamAV daemon port (default: 3310)&lt;/li&gt;
 *   &lt;li&gt;dms.virus-scanner.clamav.timeout - Connection timeout in ms (default: 10000)&lt;/li&gt;
 *   &lt;li&gt;dms.virus-scanner.clamav.chunk-size - Chunk size for streaming (default: 8192)&lt;/li&gt;
 * &lt;/ul&gt;
 * 
 * <AUTHOR> Development Team
 * @since 1.0.0
 */
@Service
@ConditionalOnProperty(name = &quot;dms.virus-scanner.clamav.enabled&quot;, havingValue = &quot;true&quot;, matchIfMissing = false)
<span class="nc" id="L40">public class ClamAvVirusScanner extends AbstractVirusScanner {</span>
    
    @Value(&quot;${dms.virus-scanner.clamav.host:localhost}&quot;)
    private String clamavHost;
    
    @Value(&quot;${dms.virus-scanner.clamav.port:3310}&quot;)
    private int clamavPort;
    
    @Value(&quot;${dms.virus-scanner.clamav.timeout:10000}&quot;)
    private int connectionTimeout;
    
    @Value(&quot;${dms.virus-scanner.clamav.chunk-size:8192}&quot;)
    private int chunkSize;
    
    @Value(&quot;${dms.virus-scanner.clamav.max-file-size:104857600}&quot;) // 100MB
    private long maxFileSize;
    
<span class="nc" id="L57">    private List&lt;String&gt; lastDetectedThreats = new ArrayList&lt;&gt;();</span>
<span class="nc" id="L58">    private String scannerVersion = &quot;Unknown&quot;;</span>
    
    @Override
    public VirusScannerType getScannerType() {
<span class="nc" id="L62">        return VirusScannerType.CLAMAV;</span>
    }
    
    @Override
    public boolean isAvailable() {
        try {
<span class="nc" id="L68">            String version = getClamAvVersion();</span>
<span class="nc" id="L69">            scannerVersion = version;</span>
<span class="nc bnc" id="L70" title="All 4 branches missed.">            return version != null &amp;&amp; !version.isEmpty();</span>
<span class="nc" id="L71">        } catch (Exception e) {</span>
<span class="nc" id="L72">            logger.warn(&quot;ClamAV scanner availability check failed: {}&quot;, e.getMessage());</span>
<span class="nc" id="L73">            return false;</span>
        }
    }
    
    @Override
    protected VirusScanResult performScan(byte[] fileContent, String fileName, String scanId) throws Exception {
<span class="nc" id="L79">        lastDetectedThreats.clear();</span>
        
<span class="nc" id="L81">        try (Socket socket = new Socket()) {</span>
            // Connect to ClamAV daemon
<span class="nc" id="L83">            socket.connect(new java.net.InetSocketAddress(clamavHost, clamavPort), connectionTimeout);</span>
<span class="nc" id="L84">            socket.setSoTimeout(connectionTimeout);</span>
            
<span class="nc" id="L86">            try (OutputStream out = socket.getOutputStream();</span>
<span class="nc" id="L87">                 InputStream in = socket.getInputStream()) {</span>
                
                // Send INSTREAM command
<span class="nc" id="L90">                out.write(&quot;zINSTREAM\0&quot;.getBytes());</span>
<span class="nc" id="L91">                out.flush();</span>
                
                // Send file content in chunks
<span class="nc" id="L94">                sendFileContent(out, fileContent);</span>
                
                // Read response
<span class="nc" id="L97">                String response = readResponse(in);</span>
<span class="nc" id="L98">                return parseResponse(response, fileName);</span>
            }
        }
    }
    
    @Override
    protected List&lt;String&gt; getDetectedThreats() {
<span class="nc" id="L105">        return new ArrayList&lt;&gt;(lastDetectedThreats);</span>
    }
    
    @Override
    protected String getScannerSpecificInfo() {
<span class="nc" id="L110">        return String.format(&quot;ClamAV Host: %s:%d, Version: %s, Max File Size: %d bytes&quot;, </span>
<span class="nc" id="L111">                           clamavHost, clamavPort, scannerVersion, maxFileSize);</span>
    }
    
    @Override
    public String getScannerInfo() {
<span class="nc" id="L116">        return scannerVersion;</span>
    }
    
    @Override
    public long getMaxFileSize() {
<span class="nc" id="L121">        return maxFileSize;</span>
    }
    
    @Override
    public long getScanTimeoutMs() {
<span class="nc" id="L126">        return connectionTimeout;</span>
    }
    
    /**
     * Gets the ClamAV version information.
     * 
     * @return version string or null if unavailable
     */
    private String getClamAvVersion() {
<span class="nc" id="L135">        try (Socket socket = new Socket()) {</span>
<span class="nc" id="L136">            socket.connect(new java.net.InetSocketAddress(clamavHost, clamavPort), connectionTimeout);</span>
<span class="nc" id="L137">            socket.setSoTimeout(connectionTimeout);</span>
            
<span class="nc" id="L139">            try (OutputStream out = socket.getOutputStream();</span>
<span class="nc" id="L140">                 InputStream in = socket.getInputStream()) {</span>
                
                // Send VERSION command
<span class="nc" id="L143">                out.write(&quot;zVERSION\0&quot;.getBytes());</span>
<span class="nc" id="L144">                out.flush();</span>
                
<span class="nc" id="L146">                return readResponse(in).trim();</span>
            }
<span class="nc" id="L148">        } catch (Exception e) {</span>
<span class="nc" id="L149">            logger.debug(&quot;Failed to get ClamAV version: {}&quot;, e.getMessage());</span>
<span class="nc" id="L150">            return null;</span>
        }
    }
    
    /**
     * Sends file content to ClamAV daemon in chunks.
     * 
     * @param out the output stream to ClamAV daemon
     * @param fileContent the file content to send
     * @throws IOException if there's an error sending data
     */
    private void sendFileContent(OutputStream out, byte[] fileContent) throws IOException {
<span class="nc" id="L162">        int offset = 0;</span>
<span class="nc" id="L163">        int remaining = fileContent.length;</span>
        
<span class="nc bnc" id="L165" title="All 2 branches missed.">        while (remaining &gt; 0) {</span>
<span class="nc" id="L166">            int currentChunkSize = Math.min(chunkSize, remaining);</span>
            
            // Send chunk size (4 bytes, network byte order)
<span class="nc" id="L169">            ByteBuffer sizeBuffer = ByteBuffer.allocate(4);</span>
<span class="nc" id="L170">            sizeBuffer.putInt(currentChunkSize);</span>
<span class="nc" id="L171">            out.write(sizeBuffer.array());</span>
            
            // Send chunk data
<span class="nc" id="L174">            out.write(fileContent, offset, currentChunkSize);</span>
<span class="nc" id="L175">            out.flush();</span>
            
<span class="nc" id="L177">            offset += currentChunkSize;</span>
<span class="nc" id="L178">            remaining -= currentChunkSize;</span>
<span class="nc" id="L179">        }</span>
        
        // Send zero-length chunk to indicate end of stream
<span class="nc" id="L182">        out.write(new byte[4]); // 4 zero bytes</span>
<span class="nc" id="L183">        out.flush();</span>
<span class="nc" id="L184">    }</span>
    
    /**
     * Reads response from ClamAV daemon.
     * 
     * @param in the input stream from ClamAV daemon
     * @return the response string
     * @throws IOException if there's an error reading data
     */
    private String readResponse(InputStream in) throws IOException {
<span class="nc" id="L194">        ByteArrayOutputStream response = new ByteArrayOutputStream();</span>
<span class="nc" id="L195">        byte[] buffer = new byte[1024];</span>
        int bytesRead;
        
<span class="nc bnc" id="L198" title="All 2 branches missed.">        while ((bytesRead = in.read(buffer)) != -1) {</span>
<span class="nc" id="L199">            response.write(buffer, 0, bytesRead);</span>
            
            // Check if we have a complete response (ends with null terminator)
<span class="nc" id="L202">            byte[] responseBytes = response.toByteArray();</span>
<span class="nc bnc" id="L203" title="All 4 branches missed.">            if (responseBytes.length &gt; 0 &amp;&amp; responseBytes[responseBytes.length - 1] == 0) {</span>
<span class="nc" id="L204">                break;</span>
            }
<span class="nc" id="L206">        }</span>
        
<span class="nc" id="L208">        return response.toString().trim();</span>
    }
    
    /**
     * Parses the ClamAV response and determines the scan result.
     * 
     * @param response the response from ClamAV daemon
     * @param fileName the name of the scanned file
     * @return the scan result
     */
    private VirusScanResult parseResponse(String response, String fileName) {
<span class="nc bnc" id="L219" title="All 4 branches missed.">        if (response == null || response.isEmpty()) {</span>
<span class="nc" id="L220">            logger.warn(&quot;Empty response from ClamAV for file: {}&quot;, fileName);</span>
<span class="nc" id="L221">            return VirusScanResult.ERROR;</span>
        }
        
<span class="nc" id="L224">        logger.debug(&quot;ClamAV response for file {}: {}&quot;, fileName, response);</span>
        
        // Remove null terminators and clean up response
<span class="nc" id="L227">        response = response.replace(&quot;\0&quot;, &quot;&quot;).trim();</span>
        
<span class="nc bnc" id="L229" title="All 2 branches missed.">        if (response.endsWith(&quot;OK&quot;)) {</span>
            // File is clean
<span class="nc" id="L231">            return VirusScanResult.CLEAN;</span>
<span class="nc bnc" id="L232" title="All 2 branches missed.">        } else if (response.contains(&quot;FOUND&quot;)) {</span>
            // Virus found - extract threat name
<span class="nc" id="L234">            String[] parts = response.split(&quot;:&quot;);</span>
<span class="nc bnc" id="L235" title="All 2 branches missed.">            if (parts.length &gt;= 2) {</span>
<span class="nc" id="L236">                String threatName = parts[1].replace(&quot;FOUND&quot;, &quot;&quot;).trim();</span>
<span class="nc" id="L237">                lastDetectedThreats.add(threatName);</span>
<span class="nc" id="L238">                logger.warn(&quot;ClamAV detected threat in file {}: {}&quot;, fileName, threatName);</span>
            }
<span class="nc" id="L240">            return VirusScanResult.INFECTED;</span>
<span class="nc bnc" id="L241" title="All 2 branches missed.">        } else if (response.contains(&quot;ERROR&quot;)) {</span>
            // Scan error
<span class="nc" id="L243">            logger.error(&quot;ClamAV scan error for file {}: {}&quot;, fileName, response);</span>
<span class="nc" id="L244">            return VirusScanResult.ERROR;</span>
        } else {
            // Unknown response
<span class="nc" id="L247">            logger.warn(&quot;Unknown ClamAV response for file {}: {}&quot;, fileName, response);</span>
<span class="nc" id="L248">            return VirusScanResult.ERROR;</span>
        }
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>