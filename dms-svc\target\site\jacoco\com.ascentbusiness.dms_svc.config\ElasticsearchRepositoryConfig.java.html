<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>ElasticsearchRepositoryConfig.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.config</a> &gt; <span class="el_source">ElasticsearchRepositoryConfig.java</span></div><h1>ElasticsearchRepositoryConfig.java</h1><pre class="source lang-java linenums">package com.ascentbusiness.dms_svc.config;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.elasticsearch.repository.config.EnableElasticsearchRepositories;

/**
 * Elasticsearch Repository Configuration for Document Management System
 *
 * This configuration is separate from the main ElasticsearchConfig to ensure
 * that repository scanning only happens when Elasticsearch is enabled.
 *
 * The @EnableElasticsearchRepositories annotation is processed during Spring's
 * component scanning phase, so it needs to be on a conditionally loaded class.
 */
@Configuration
@ConditionalOnProperty(name = &quot;elasticsearch.enabled&quot;, havingValue = &quot;true&quot;, matchIfMissing = false)
@EnableElasticsearchRepositories(basePackages = &quot;com.ascentbusiness.dms_svc.search.repository&quot;)
<span class="nc" id="L19">public class ElasticsearchRepositoryConfig {</span>
    // This class only exists to conditionally enable Elasticsearch repositories
    // The actual client configuration is in ElasticsearchConfig
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>