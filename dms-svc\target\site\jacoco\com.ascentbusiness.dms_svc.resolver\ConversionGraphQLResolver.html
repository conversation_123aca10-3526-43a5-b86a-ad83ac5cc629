<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>ConversionGraphQLResolver</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.html" class="el_package">com.ascentbusiness.dms_svc.resolver</a> &gt; <span class="el_class">ConversionGraphQLResolver</span></div><h1>ConversionGraphQLResolver</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">3,628 of 3,628</td><td class="ctr2">0%</td><td class="bar">312 of 312</td><td class="ctr2">0%</td><td class="ctr1">217</td><td class="ctr2">217</td><td class="ctr1">950</td><td class="ctr2">950</td><td class="ctr1">59</td><td class="ctr2">59</td></tr></tfoot><tbody><tr><td id="a3"><a href="ConversionGraphQLResolver.java.html#L364" class="el_method">batchConvertFiles(BatchConversionInput)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="341" alt="341"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="72" height="10" title="18" alt="18"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f4">10</td><td class="ctr2" id="g4">10</td><td class="ctr1" id="h0">94</td><td class="ctr2" id="i0">94</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a12"><a href="ConversionGraphQLResolver.java.html#L62" class="el_method">convertMarkdownToWord(MarkdownConversionInput)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="93" height="10" title="265" alt="265"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="88" height="10" title="22" alt="22"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f1">12</td><td class="ctr2" id="g1">12</td><td class="ctr1" id="h1">66</td><td class="ctr2" id="i1">66</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a14"><a href="ConversionGraphQLResolver.java.html#L231" class="el_method">convertWordToPdf(WordConversionInput)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="76" height="10" title="218" alt="218"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="88" height="10" title="22" alt="22"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f2">12</td><td class="ctr2" id="g2">12</td><td class="ctr1" id="h4">47</td><td class="ctr2" id="i4">47</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a13"><a href="ConversionGraphQLResolver.java.html#L167" class="el_method">convertPdfToWord(PdfConversionInput)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="65" height="10" title="185" alt="185"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d9"><img src="../jacoco-resources/redbar.gif" width="40" height="10" title="10" alt="10"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f9">6</td><td class="ctr2" id="g9">6</td><td class="ctr1" id="h7">39</td><td class="ctr2" id="i7">39</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a11"><a href="ConversionGraphQLResolver.java.html#L309" class="el_method">convertFile(ConversionInput)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="58" height="10" title="166" alt="166"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="80" height="10" title="20" alt="20"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f3">11</td><td class="ctr2" id="g3">11</td><td class="ctr1" id="h9">27</td><td class="ctr2" id="i9">27</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a29"><a href="ConversionGraphQLResolver.java.html#L657" class="el_method">getConversionStatistics(LocalDateTime, LocalDateTime, List)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="51" height="10" title="146" alt="146"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d18"><img src="../jacoco-resources/redbar.gif" width="16" height="10" title="4" alt="4"/></td><td class="ctr2" id="e5">0%</td><td class="ctr1" id="f19">3</td><td class="ctr2" id="g19">3</td><td class="ctr1" id="h2">53</td><td class="ctr2" id="i2">53</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a18"><a href="ConversionGraphQLResolver.java.html#L1503" class="el_method">createMockConversionHistory()</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="51" height="10" title="145" alt="145"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d46"/><td class="ctr2" id="e46">n/a</td><td class="ctr1" id="f46">1</td><td class="ctr2" id="g46">1</td><td class="ctr1" id="h3">53</td><td class="ctr2" id="i3">53</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a5"><a href="ConversionGraphQLResolver.java.html#L946" class="el_method">cancelConversion(String)</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="44" height="10" title="126" alt="126"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d12"><img src="../jacoco-resources/redbar.gif" width="24" height="10" title="6" alt="6"/></td><td class="ctr2" id="e6">0%</td><td class="ctr1" id="f12">4</td><td class="ctr2" id="g12">4</td><td class="ctr1" id="h5">42</td><td class="ctr2" id="i5">42</td><td class="ctr1" id="j7">1</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a56"><a href="ConversionGraphQLResolver.java.html#L1008" class="el_method">retryConversion(String)</a></td><td class="bar" id="b8"><img src="../jacoco-resources/redbar.gif" width="42" height="10" title="122" alt="122"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d19"><img src="../jacoco-resources/redbar.gif" width="16" height="10" title="4" alt="4"/></td><td class="ctr2" id="e7">0%</td><td class="ctr1" id="f20">3</td><td class="ctr2" id="g20">3</td><td class="ctr1" id="h6">41</td><td class="ctr2" id="i6">41</td><td class="ctr1" id="j8">1</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a24"><a href="ConversionGraphQLResolver.java.html#L603" class="el_method">getConversionHistory(String, ConversionType, LocalDateTime, LocalDateTime, PaginationInput)</a></td><td class="bar" id="b9"><img src="../jacoco-resources/redbar.gif" width="37" height="10" title="107" alt="107"/></td><td class="ctr2" id="c9">0%</td><td class="bar" id="d7"><img src="../jacoco-resources/redbar.gif" width="48" height="10" title="12" alt="12"/></td><td class="ctr2" id="e8">0%</td><td class="ctr1" id="f8">7</td><td class="ctr2" id="g8">7</td><td class="ctr1" id="h16">23</td><td class="ctr2" id="i16">23</td><td class="ctr1" id="j9">1</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a51"><a href="ConversionGraphQLResolver.java.html#L1382" class="el_method">performMarkdownToWordConversion(String, MarkdownConversionInput)</a></td><td class="bar" id="b10"><img src="../jacoco-resources/redbar.gif" width="35" height="10" title="102" alt="102"/></td><td class="ctr2" id="c10">0%</td><td class="bar" id="d13"><img src="../jacoco-resources/redbar.gif" width="24" height="10" title="6" alt="6"/></td><td class="ctr2" id="e9">0%</td><td class="ctr1" id="f13">4</td><td class="ctr2" id="g13">4</td><td class="ctr1" id="h11">26</td><td class="ctr2" id="i11">26</td><td class="ctr1" id="j10">1</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a32"><a href="ConversionGraphQLResolver.java.html#L902" class="el_method">getUserConversions(String, ConversionStatus, PaginationInput)</a></td><td class="bar" id="b11"><img src="../jacoco-resources/redbar.gif" width="34" height="10" title="99" alt="99"/></td><td class="ctr2" id="c11">0%</td><td class="bar" id="d10"><img src="../jacoco-resources/redbar.gif" width="40" height="10" title="10" alt="10"/></td><td class="ctr2" id="e10">0%</td><td class="ctr1" id="f10">6</td><td class="ctr2" id="g10">6</td><td class="ctr1" id="h17">21</td><td class="ctr2" id="i17">21</td><td class="ctr1" id="j11">1</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a52"><a href="ConversionGraphQLResolver.java.html#L1423" class="el_method">performPdfToWordConversion(String, PdfConversionInput)</a></td><td class="bar" id="b12"><img src="../jacoco-resources/redbar.gif" width="34" height="10" title="98" alt="98"/></td><td class="ctr2" id="c12">0%</td><td class="bar" id="d14"><img src="../jacoco-resources/redbar.gif" width="24" height="10" title="6" alt="6"/></td><td class="ctr2" id="e11">0%</td><td class="ctr1" id="f14">4</td><td class="ctr2" id="g14">4</td><td class="ctr1" id="h12">26</td><td class="ctr2" id="i12">26</td><td class="ctr1" id="j12">1</td><td class="ctr2" id="k12">1</td></tr><tr><td id="a53"><a href="ConversionGraphQLResolver.java.html#L1464" class="el_method">performWordToPdfConversion(String, WordConversionInput)</a></td><td class="bar" id="b13"><img src="../jacoco-resources/redbar.gif" width="34" height="10" title="98" alt="98"/></td><td class="ctr2" id="c13">0%</td><td class="bar" id="d15"><img src="../jacoco-resources/redbar.gif" width="24" height="10" title="6" alt="6"/></td><td class="ctr2" id="e12">0%</td><td class="ctr1" id="f15">4</td><td class="ctr2" id="g15">4</td><td class="ctr1" id="h13">26</td><td class="ctr2" id="i13">26</td><td class="ctr1" id="j13">1</td><td class="ctr2" id="k13">1</td></tr><tr><td id="a22"><a href="ConversionGraphQLResolver.java.html#L553" class="el_method">getBatchConversionStatus(String)</a></td><td class="bar" id="b14"><img src="../jacoco-resources/redbar.gif" width="32" height="10" title="93" alt="93"/></td><td class="ctr2" id="c14">0%</td><td class="bar" id="d29"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="2" alt="2"/></td><td class="ctr2" id="e13">0%</td><td class="ctr1" id="f29">2</td><td class="ctr2" id="g29">2</td><td class="ctr1" id="h8">33</td><td class="ctr2" id="i8">33</td><td class="ctr1" id="j14">1</td><td class="ctr2" id="k14">1</td></tr><tr><td id="a50"><a href="ConversionGraphQLResolver.java.html#L1311" class="el_method">performGenericConversion(String, ConversionInput, ConversionType)</a></td><td class="bar" id="b15"><img src="../jacoco-resources/redbar.gif" width="30" height="10" title="87" alt="87"/></td><td class="ctr2" id="c15">0%</td><td class="bar" id="d20"><img src="../jacoco-resources/redbar.gif" width="16" height="10" title="4" alt="4"/></td><td class="ctr2" id="e14">0%</td><td class="ctr1" id="f16">4</td><td class="ctr2" id="g16">4</td><td class="ctr1" id="h18">20</td><td class="ctr2" id="i18">20</td><td class="ctr1" id="j15">1</td><td class="ctr2" id="k15">1</td></tr><tr><td id="a27"><a href="ConversionGraphQLResolver.java.html#L847" class="el_method">getConversionQueue()</a></td><td class="bar" id="b16"><img src="../jacoco-resources/redbar.gif" width="29" height="10" title="83" alt="83"/></td><td class="ctr2" id="c16">0%</td><td class="bar" id="d47"/><td class="ctr2" id="e47">n/a</td><td class="ctr1" id="f47">1</td><td class="ctr2" id="g47">1</td><td class="ctr1" id="h14">26</td><td class="ctr2" id="i14">26</td><td class="ctr1" id="j16">1</td><td class="ctr2" id="k16">1</td></tr><tr><td id="a20"><a href="ConversionGraphQLResolver.java.html#L1278" class="el_method">determineConversionType(String, String)</a></td><td class="bar" id="b17"><img src="../jacoco-resources/redbar.gif" width="27" height="10" title="78" alt="78"/></td><td class="ctr2" id="c17">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="30" alt="30"/></td><td class="ctr2" id="e15">0%</td><td class="ctr1" id="f0">16</td><td class="ctr2" id="g0">16</td><td class="ctr1" id="h20">18</td><td class="ctr2" id="i20">18</td><td class="ctr1" id="j17">1</td><td class="ctr2" id="k17">1</td></tr><tr><td id="a25"><a href="ConversionGraphQLResolver.java.html#L512" class="el_method">getConversionProgress(String, String)</a></td><td class="bar" id="b18"><img src="../jacoco-resources/redbar.gif" width="27" height="10" title="77" alt="77"/></td><td class="ctr2" id="c18">0%</td><td class="bar" id="d11"><img src="../jacoco-resources/redbar.gif" width="32" height="10" title="8" alt="8"/></td><td class="ctr2" id="e16">0%</td><td class="ctr1" id="f11">5</td><td class="ctr2" id="g11">5</td><td class="ctr1" id="h15">25</td><td class="ctr2" id="i15">25</td><td class="ctr1" id="j18">1</td><td class="ctr2" id="k18">1</td></tr><tr><td id="a15"><a href="ConversionGraphQLResolver.java.html#L1347" class="el_method">createConversionInputFromBatch(BatchConversionInput, MultipartFile, String)</a></td><td class="bar" id="b19"><img src="../jacoco-resources/redbar.gif" width="27" height="10" title="77" alt="77"/></td><td class="ctr2" id="c19">0%</td><td class="bar" id="d8"><img src="../jacoco-resources/redbar.gif" width="48" height="10" title="12" alt="12"/></td><td class="ctr2" id="e17">0%</td><td class="ctr1" id="f7">8</td><td class="ctr2" id="g7">8</td><td class="ctr1" id="h21">18</td><td class="ctr2" id="i21">18</td><td class="ctr1" id="j19">1</td><td class="ctr2" id="k19">1</td></tr><tr><td id="a31"><a href="ConversionGraphQLResolver.java.html#L784" class="el_method">getSupportedConversions()</a></td><td class="bar" id="b20"><img src="../jacoco-resources/redbar.gif" width="23" height="10" title="67" alt="67"/></td><td class="ctr2" id="c20">0%</td><td class="bar" id="d48"/><td class="ctr2" id="e48">n/a</td><td class="ctr1" id="f48">1</td><td class="ctr2" id="g48">1</td><td class="ctr1" id="h10">27</td><td class="ctr2" id="i10">27</td><td class="ctr1" id="j20">1</td><td class="ctr2" id="k20">1</td></tr><tr><td id="a46"><a href="ConversionGraphQLResolver.java.html#L724" class="el_method">lambda$getConversionStatistics$6(Map.Entry)</a></td><td class="bar" id="b21"><img src="../jacoco-resources/redbar.gif" width="22" height="10" title="63" alt="63"/></td><td class="ctr2" id="c21">0%</td><td class="bar" id="d30"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="2" alt="2"/></td><td class="ctr2" id="e18">0%</td><td class="ctr1" id="f30">2</td><td class="ctr2" id="g30">2</td><td class="ctr1" id="h22">17</td><td class="ctr2" id="i22">17</td><td class="ctr1" id="j21">1</td><td class="ctr2" id="k21">1</td></tr><tr><td id="a44"><a href="ConversionGraphQLResolver.java.html#L698" class="el_method">lambda$getConversionStatistics$4(Map.Entry)</a></td><td class="bar" id="b22"><img src="../jacoco-resources/redbar.gif" width="22" height="10" title="63" alt="63"/></td><td class="ctr2" id="c22">0%</td><td class="bar" id="d31"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="2" alt="2"/></td><td class="ctr2" id="e19">0%</td><td class="ctr1" id="f31">2</td><td class="ctr2" id="g31">2</td><td class="ctr1" id="h23">17</td><td class="ctr2" id="i23">17</td><td class="ctr1" id="j22">1</td><td class="ctr2" id="k22">1</td></tr><tr><td id="a6"><a href="ConversionGraphQLResolver.java.html#L1137" class="el_method">cleanupCompletedConversions(Integer)</a></td><td class="bar" id="b23"><img src="../jacoco-resources/redbar.gif" width="21" height="10" title="60" alt="60"/></td><td class="ctr2" id="c23">0%</td><td class="bar" id="d21"><img src="../jacoco-resources/redbar.gif" width="16" height="10" title="4" alt="4"/></td><td class="ctr2" id="e20">0%</td><td class="ctr1" id="f21">3</td><td class="ctr2" id="g21">3</td><td class="ctr1" id="h24">16</td><td class="ctr2" id="i24">16</td><td class="ctr1" id="j23">1</td><td class="ctr2" id="k23">1</td></tr><tr><td id="a7"><a href="ConversionGraphQLResolver.java.html#L1174" class="el_method">cleanupFailedConversions(Integer)</a></td><td class="bar" id="b24"><img src="../jacoco-resources/redbar.gif" width="21" height="10" title="60" alt="60"/></td><td class="ctr2" id="c24">0%</td><td class="bar" id="d22"><img src="../jacoco-resources/redbar.gif" width="16" height="10" title="4" alt="4"/></td><td class="ctr2" id="e21">0%</td><td class="ctr1" id="f22">3</td><td class="ctr2" id="g22">3</td><td class="ctr1" id="h25">16</td><td class="ctr2" id="i25">16</td><td class="ctr1" id="j24">1</td><td class="ctr2" id="k24">1</td></tr><tr><td id="a16"><a href="ConversionGraphQLResolver.java.html#L1250" class="el_method">createErrorResult(String, ConversionType, String, Exception)</a></td><td class="bar" id="b25"><img src="../jacoco-resources/redbar.gif" width="17" height="10" title="50" alt="50"/></td><td class="ctr2" id="c25">0%</td><td class="bar" id="d49"/><td class="ctr2" id="e49">n/a</td><td class="ctr1" id="f49">1</td><td class="ctr2" id="g49">1</td><td class="ctr1" id="h19">20</td><td class="ctr2" id="i19">20</td><td class="ctr1" id="j25">1</td><td class="ctr2" id="k25">1</td></tr><tr><td id="a38"><a href="ConversionGraphQLResolver.java.html#L868" class="el_method">lambda$getConversionQueue$12(Map.Entry)</a></td><td class="bar" id="b26"><img src="../jacoco-resources/redbar.gif" width="14" height="10" title="40" alt="40"/></td><td class="ctr2" id="c26">0%</td><td class="bar" id="d50"/><td class="ctr2" id="e50">n/a</td><td class="ctr1" id="f50">1</td><td class="ctr2" id="g50">1</td><td class="ctr1" id="h26">12</td><td class="ctr2" id="i26">12</td><td class="ctr1" id="j26">1</td><td class="ctr2" id="k26">1</td></tr><tr><td id="a41"><a href="ConversionGraphQLResolver.java.html#L667" class="el_method">lambda$getConversionStatistics$1(LocalDateTime, LocalDateTime, List, ConversionResult)</a></td><td class="bar" id="b27"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="38" alt="38"/></td><td class="ctr2" id="c27">0%</td><td class="bar" id="d5"><img src="../jacoco-resources/redbar.gif" width="72" height="10" title="18" alt="18"/></td><td class="ctr2" id="e22">0%</td><td class="ctr1" id="f5">10</td><td class="ctr2" id="g5">10</td><td class="ctr1" id="h33">7</td><td class="ctr2" id="i33">7</td><td class="ctr1" id="j27">1</td><td class="ctr2" id="k27">1</td></tr><tr><td id="a23"><a href="ConversionGraphQLResolver.java.html#L827" class="el_method">getConversionCapabilities()</a></td><td class="bar" id="b28"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="37" alt="37"/></td><td class="ctr2" id="c28">0%</td><td class="bar" id="d51"/><td class="ctr2" id="e51">n/a</td><td class="ctr1" id="f51">1</td><td class="ctr2" id="g51">1</td><td class="ctr1" id="h27">11</td><td class="ctr2" id="i27">11</td><td class="ctr1" id="j28">1</td><td class="ctr2" id="k28">1</td></tr><tr><td id="a58"><a href="ConversionGraphQLResolver.java.html#L1228" class="el_method">updateProgressToCompleted(String, ConversionStatus)</a></td><td class="bar" id="b29"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="37" alt="37"/></td><td class="ctr2" id="c29">0%</td><td class="bar" id="d23"><img src="../jacoco-resources/redbar.gif" width="16" height="10" title="4" alt="4"/></td><td class="ctr2" id="e23">0%</td><td class="ctr1" id="f23">3</td><td class="ctr2" id="g23">3</td><td class="ctr1" id="h29">9</td><td class="ctr2" id="i29">9</td><td class="ctr1" id="j29">1</td><td class="ctr2" id="k29">1</td></tr><tr><td id="a35"><a href="ConversionGraphQLResolver.java.html#L613" class="el_method">lambda$getConversionHistory$0(ConversionType, LocalDateTime, LocalDateTime, ConversionResult)</a></td><td class="bar" id="b30"><img src="../jacoco-resources/redbar.gif" width="12" height="10" title="35" alt="35"/></td><td class="ctr2" id="c30">0%</td><td class="bar" id="d6"><img src="../jacoco-resources/redbar.gif" width="64" height="10" title="16" alt="16"/></td><td class="ctr2" id="e24">0%</td><td class="ctr1" id="f6">9</td><td class="ctr2" id="g6">9</td><td class="ctr1" id="h34">7</td><td class="ctr2" id="i34">7</td><td class="ctr1" id="j30">1</td><td class="ctr2" id="k30">1</td></tr><tr><td id="a49"><a href="ConversionGraphQLResolver.java.html#L1065" class="el_method">pauseConversion(String)</a></td><td class="bar" id="b31"><img src="../jacoco-resources/redbar.gif" width="11" height="10" title="34" alt="34"/></td><td class="ctr2" id="c31">0%</td><td class="bar" id="d24"><img src="../jacoco-resources/redbar.gif" width="16" height="10" title="4" alt="4"/></td><td class="ctr2" id="e25">0%</td><td class="ctr1" id="f24">3</td><td class="ctr2" id="g24">3</td><td class="ctr1" id="h30">8</td><td class="ctr2" id="i30">8</td><td class="ctr1" id="j31">1</td><td class="ctr2" id="k31">1</td></tr><tr><td id="a55"><a href="ConversionGraphQLResolver.java.html#L1083" class="el_method">resumeConversion(String)</a></td><td class="bar" id="b32"><img src="../jacoco-resources/redbar.gif" width="11" height="10" title="34" alt="34"/></td><td class="ctr2" id="c32">0%</td><td class="bar" id="d25"><img src="../jacoco-resources/redbar.gif" width="16" height="10" title="4" alt="4"/></td><td class="ctr2" id="e26">0%</td><td class="ctr1" id="f25">3</td><td class="ctr2" id="g25">3</td><td class="ctr1" id="h31">8</td><td class="ctr2" id="i31">8</td><td class="ctr1" id="j32">1</td><td class="ctr2" id="k32">1</td></tr><tr><td id="a4"><a href="ConversionGraphQLResolver.java.html#L1119" class="el_method">cancelBatchConversion(String)</a></td><td class="bar" id="b33"><img src="../jacoco-resources/redbar.gif" width="11" height="10" title="34" alt="34"/></td><td class="ctr2" id="c33">0%</td><td class="bar" id="d26"><img src="../jacoco-resources/redbar.gif" width="16" height="10" title="4" alt="4"/></td><td class="ctr2" id="e27">0%</td><td class="ctr1" id="f26">3</td><td class="ctr2" id="g26">3</td><td class="ctr1" id="h32">8</td><td class="ctr2" id="i32">8</td><td class="ctr1" id="j33">1</td><td class="ctr2" id="k33">1</td></tr><tr><td id="a54"><a href="ConversionGraphQLResolver.java.html#L1101" class="el_method">prioritizeConversion(String, ConversionPriority)</a></td><td class="bar" id="b34"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="24" alt="24"/></td><td class="ctr2" id="c34">0%</td><td class="bar" id="d32"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="2" alt="2"/></td><td class="ctr2" id="e28">0%</td><td class="ctr1" id="f32">2</td><td class="ctr2" id="g32">2</td><td class="ctr1" id="h35">6</td><td class="ctr2" id="i35">6</td><td class="ctr1" id="j34">1</td><td class="ctr2" id="k34">1</td></tr><tr><td id="a17"><a href="ConversionGraphQLResolver.java.html#L1211" class="el_method">createInitialProgress(String, ConversionType, String)</a></td><td class="bar" id="b35"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="24" alt="24"/></td><td class="ctr2" id="c35">0%</td><td class="bar" id="d52"/><td class="ctr2" id="e52">n/a</td><td class="ctr1" id="f52">1</td><td class="ctr2" id="g52">1</td><td class="ctr1" id="h28">11</td><td class="ctr2" id="i28">11</td><td class="ctr1" id="j35">1</td><td class="ctr2" id="k35">1</td></tr><tr><td id="a34"><a href="ConversionGraphQLResolver.java.html#L1185" class="el_method">lambda$cleanupFailedConversions$15(LocalDateTime, Map.Entry)</a></td><td class="bar" id="b36"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="20" alt="20"/></td><td class="ctr2" id="c36">0%</td><td class="bar" id="d16"><img src="../jacoco-resources/redbar.gif" width="24" height="10" title="6" alt="6"/></td><td class="ctr2" id="e29">0%</td><td class="ctr1" id="f17">4</td><td class="ctr2" id="g17">4</td><td class="ctr1" id="h36">4</td><td class="ctr2" id="i36">4</td><td class="ctr1" id="j36">1</td><td class="ctr2" id="k36">1</td></tr><tr><td id="a33"><a href="ConversionGraphQLResolver.java.html#L1148" class="el_method">lambda$cleanupCompletedConversions$14(LocalDateTime, Map.Entry)</a></td><td class="bar" id="b37"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="20" alt="20"/></td><td class="ctr2" id="c37">0%</td><td class="bar" id="d17"><img src="../jacoco-resources/redbar.gif" width="24" height="10" title="6" alt="6"/></td><td class="ctr2" id="e30">0%</td><td class="ctr1" id="f18">4</td><td class="ctr2" id="g18">4</td><td class="ctr1" id="h37">4</td><td class="ctr2" id="i37">4</td><td class="ctr1" id="j37">1</td><td class="ctr2" id="k37">1</td></tr><tr><td id="a8"><a href="ConversionGraphQLResolver.java.html#L35" class="el_method">ConversionGraphQLResolver()</a></td><td class="bar" id="b38"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="18" alt="18"/></td><td class="ctr2" id="c38">0%</td><td class="bar" id="d53"/><td class="ctr2" id="e53">n/a</td><td class="ctr1" id="f53">1</td><td class="ctr2" id="g53">1</td><td class="ctr1" id="h38">4</td><td class="ctr2" id="i38">4</td><td class="ctr1" id="j38">1</td><td class="ctr2" id="k38">1</td></tr><tr><td id="a48"><a href="ConversionGraphQLResolver.java.html#L908" class="el_method">lambda$getUserConversions$13(ConversionStatus, ConversionResult)</a></td><td class="bar" id="b39"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="11" alt="11"/></td><td class="ctr2" id="c39">0%</td><td class="bar" id="d27"><img src="../jacoco-resources/redbar.gif" width="16" height="10" title="4" alt="4"/></td><td class="ctr2" id="e31">0%</td><td class="ctr1" id="f27">3</td><td class="ctr2" id="g27">3</td><td class="ctr1" id="h39">3</td><td class="ctr2" id="i39">3</td><td class="ctr1" id="j39">1</td><td class="ctr2" id="k39">1</td></tr><tr><td id="a47"><a href="ConversionGraphQLResolver.java.html#L752" class="el_method">lambda$getConversionStatistics$7(ConversionResult)</a></td><td class="bar" id="b40"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="11" alt="11"/></td><td class="ctr2" id="c40">0%</td><td class="bar" id="d28"><img src="../jacoco-resources/redbar.gif" width="16" height="10" title="4" alt="4"/></td><td class="ctr2" id="e32">0%</td><td class="ctr1" id="f28">3</td><td class="ctr2" id="g28">3</td><td class="ctr1" id="h41">1</td><td class="ctr2" id="i41">1</td><td class="ctr1" id="j40">1</td><td class="ctr2" id="k40">1</td></tr><tr><td id="a30"><a href="ConversionGraphQLResolver.java.html#L501" class="el_method">getConversionStatus(String)</a></td><td class="bar" id="b41"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="10" alt="10"/></td><td class="ctr2" id="c41">0%</td><td class="bar" id="d54"/><td class="ctr2" id="e54">n/a</td><td class="ctr1" id="f54">1</td><td class="ctr2" id="g54">1</td><td class="ctr1" id="h40">2</td><td class="ctr2" id="i40">2</td><td class="ctr1" id="j41">1</td><td class="ctr2" id="k41">1</td></tr><tr><td id="a2"><a href="ConversionGraphQLResolver.java.html#L1582" class="el_method">batchConversionResultStartedAt(BatchConversionResult)</a></td><td class="bar" id="b42"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="10" alt="10"/></td><td class="ctr2" id="c42">0%</td><td class="bar" id="d33"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="2" alt="2"/></td><td class="ctr2" id="e33">0%</td><td class="ctr1" id="f33">2</td><td class="ctr2" id="g33">2</td><td class="ctr1" id="h42">1</td><td class="ctr2" id="i42">1</td><td class="ctr1" id="j42">1</td><td class="ctr2" id="k42">1</td></tr><tr><td id="a0"><a href="ConversionGraphQLResolver.java.html#L1591" class="el_method">batchConversionResultCompletedAt(BatchConversionResult)</a></td><td class="bar" id="b43"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="10" alt="10"/></td><td class="ctr2" id="c43">0%</td><td class="bar" id="d34"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="2" alt="2"/></td><td class="ctr2" id="e34">0%</td><td class="ctr1" id="f34">2</td><td class="ctr2" id="g34">2</td><td class="ctr1" id="h43">1</td><td class="ctr2" id="i43">1</td><td class="ctr1" id="j43">1</td><td class="ctr2" id="k43">1</td></tr><tr><td id="a1"><a href="ConversionGraphQLResolver.java.html#L1600" class="el_method">batchConversionResultEstimatedCompletionTime(BatchConversionResult)</a></td><td class="bar" id="b44"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="10" alt="10"/></td><td class="ctr2" id="c44">0%</td><td class="bar" id="d35"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="2" alt="2"/></td><td class="ctr2" id="e35">0%</td><td class="ctr1" id="f35">2</td><td class="ctr2" id="g35">2</td><td class="ctr1" id="h44">1</td><td class="ctr2" id="i44">1</td><td class="ctr1" id="j44">1</td><td class="ctr2" id="k44">1</td></tr><tr><td id="a10"><a href="ConversionGraphQLResolver.java.html#L1609" class="el_method">conversionProgressStartedAt(ConversionProgress)</a></td><td class="bar" id="b45"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="10" alt="10"/></td><td class="ctr2" id="c45">0%</td><td class="bar" id="d36"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="2" alt="2"/></td><td class="ctr2" id="e36">0%</td><td class="ctr1" id="f36">2</td><td class="ctr2" id="g36">2</td><td class="ctr1" id="h45">1</td><td class="ctr2" id="i45">1</td><td class="ctr1" id="j45">1</td><td class="ctr2" id="k45">1</td></tr><tr><td id="a9"><a href="ConversionGraphQLResolver.java.html#L1618" class="el_method">conversionProgressLastUpdatedAt(ConversionProgress)</a></td><td class="bar" id="b46"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="10" alt="10"/></td><td class="ctr2" id="c46">0%</td><td class="bar" id="d37"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="2" alt="2"/></td><td class="ctr2" id="e37">0%</td><td class="ctr1" id="f37">2</td><td class="ctr2" id="g37">2</td><td class="ctr1" id="h46">1</td><td class="ctr2" id="i46">1</td><td class="ctr1" id="j46">1</td><td class="ctr2" id="k46">1</td></tr><tr><td id="a37"><a href="ConversionGraphQLResolver.java.html#L863" class="el_method">lambda$getConversionQueue$11(ConversionResult)</a></td><td class="bar" id="b47"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="8" alt="8"/></td><td class="ctr2" id="c47">0%</td><td class="bar" id="d38"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="2" alt="2"/></td><td class="ctr2" id="e38">0%</td><td class="ctr1" id="f38">2</td><td class="ctr2" id="g38">2</td><td class="ctr1" id="h47">1</td><td class="ctr2" id="i47">1</td><td class="ctr1" id="j47">1</td><td class="ctr2" id="k47">1</td></tr><tr><td id="a36"><a href="ConversionGraphQLResolver.java.html#L859" class="el_method">lambda$getConversionQueue$10(ConversionResult)</a></td><td class="bar" id="b48"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="8" alt="8"/></td><td class="ctr2" id="c48">0%</td><td class="bar" id="d39"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="2" alt="2"/></td><td class="ctr2" id="e39">0%</td><td class="ctr1" id="f39">2</td><td class="ctr2" id="g39">2</td><td class="ctr1" id="h48">1</td><td class="ctr2" id="i48">1</td><td class="ctr1" id="j48">1</td><td class="ctr2" id="k48">1</td></tr><tr><td id="a40"><a href="ConversionGraphQLResolver.java.html#L855" class="el_method">lambda$getConversionQueue$9(ConversionProgress)</a></td><td class="bar" id="b49"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="8" alt="8"/></td><td class="ctr2" id="c49">0%</td><td class="bar" id="d40"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="2" alt="2"/></td><td class="ctr2" id="e40">0%</td><td class="ctr1" id="f40">2</td><td class="ctr2" id="g40">2</td><td class="ctr1" id="h49">1</td><td class="ctr2" id="i49">1</td><td class="ctr1" id="j49">1</td><td class="ctr2" id="k49">1</td></tr><tr><td id="a39"><a href="ConversionGraphQLResolver.java.html#L851" class="el_method">lambda$getConversionQueue$8(ConversionProgress)</a></td><td class="bar" id="b50"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="8" alt="8"/></td><td class="ctr2" id="c50">0%</td><td class="bar" id="d41"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="2" alt="2"/></td><td class="ctr2" id="e41">0%</td><td class="ctr1" id="f41">2</td><td class="ctr2" id="g41">2</td><td class="ctr1" id="h50">1</td><td class="ctr2" id="i50">1</td><td class="ctr1" id="j50">1</td><td class="ctr2" id="k50">1</td></tr><tr><td id="a45"><a href="ConversionGraphQLResolver.java.html#L726" class="el_method">lambda$getConversionStatistics$5(ConversionResult)</a></td><td class="bar" id="b51"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="8" alt="8"/></td><td class="ctr2" id="c51">0%</td><td class="bar" id="d42"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="2" alt="2"/></td><td class="ctr2" id="e42">0%</td><td class="ctr1" id="f42">2</td><td class="ctr2" id="g42">2</td><td class="ctr1" id="h51">1</td><td class="ctr2" id="i51">1</td><td class="ctr1" id="j51">1</td><td class="ctr2" id="k51">1</td></tr><tr><td id="a43"><a href="ConversionGraphQLResolver.java.html#L700" class="el_method">lambda$getConversionStatistics$3(ConversionResult)</a></td><td class="bar" id="b52"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="8" alt="8"/></td><td class="ctr2" id="c52">0%</td><td class="bar" id="d43"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="2" alt="2"/></td><td class="ctr2" id="e43">0%</td><td class="ctr1" id="f43">2</td><td class="ctr2" id="g43">2</td><td class="ctr1" id="h52">1</td><td class="ctr2" id="i52">1</td><td class="ctr1" id="j52">1</td><td class="ctr2" id="k52">1</td></tr><tr><td id="a42"><a href="ConversionGraphQLResolver.java.html#L683" class="el_method">lambda$getConversionStatistics$2(ConversionResult)</a></td><td class="bar" id="b53"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="8" alt="8"/></td><td class="ctr2" id="c53">0%</td><td class="bar" id="d44"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="2" alt="2"/></td><td class="ctr2" id="e44">0%</td><td class="ctr1" id="f44">2</td><td class="ctr2" id="g44">2</td><td class="ctr1" id="h53">1</td><td class="ctr2" id="i53">1</td><td class="ctr1" id="j53">1</td><td class="ctr2" id="k53">1</td></tr><tr><td id="a19"><a href="ConversionGraphQLResolver.java.html#L1243" class="el_method">determineConversionMethod(boolean)</a></td><td class="bar" id="b54"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="6" alt="6"/></td><td class="ctr2" id="c54">0%</td><td class="bar" id="d45"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="2" alt="2"/></td><td class="ctr2" id="e45">0%</td><td class="ctr1" id="f45">2</td><td class="ctr2" id="g45">2</td><td class="ctr1" id="h54">1</td><td class="ctr2" id="i54">1</td><td class="ctr1" id="j54">1</td><td class="ctr2" id="k54">1</td></tr><tr><td id="a57"><a href="ConversionGraphQLResolver.java.html#L34" class="el_method">static {...}</a></td><td class="bar" id="b55"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="4" alt="4"/></td><td class="ctr2" id="c55">0%</td><td class="bar" id="d55"/><td class="ctr2" id="e55">n/a</td><td class="ctr1" id="f55">1</td><td class="ctr2" id="g55">1</td><td class="ctr1" id="h55">1</td><td class="ctr2" id="i55">1</td><td class="ctr1" id="j55">1</td><td class="ctr2" id="k55">1</td></tr><tr><td id="a28"><a href="ConversionGraphQLResolver.java.html#L1563" class="el_method">getConversionResultMap()</a></td><td class="bar" id="b56"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c56">0%</td><td class="bar" id="d56"/><td class="ctr2" id="e56">n/a</td><td class="ctr1" id="f56">1</td><td class="ctr2" id="g56">1</td><td class="ctr1" id="h56">1</td><td class="ctr2" id="i56">1</td><td class="ctr1" id="j56">1</td><td class="ctr2" id="k56">1</td></tr><tr><td id="a26"><a href="ConversionGraphQLResolver.java.html#L1567" class="el_method">getConversionProgressMap()</a></td><td class="bar" id="b57"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c57">0%</td><td class="bar" id="d57"/><td class="ctr2" id="e57">n/a</td><td class="ctr1" id="f57">1</td><td class="ctr2" id="g57">1</td><td class="ctr1" id="h57">1</td><td class="ctr2" id="i57">1</td><td class="ctr1" id="j57">1</td><td class="ctr2" id="k57">1</td></tr><tr><td id="a21"><a href="ConversionGraphQLResolver.java.html#L1571" class="el_method">getBatchConversionResultMap()</a></td><td class="bar" id="b58"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c58">0%</td><td class="bar" id="d58"/><td class="ctr2" id="e58">n/a</td><td class="ctr1" id="f58">1</td><td class="ctr2" id="g58">1</td><td class="ctr1" id="h58">1</td><td class="ctr2" id="i58">1</td><td class="ctr1" id="j58">1</td><td class="ctr2" id="k58">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>