<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>DmsException</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.html" class="el_package">com.ascentbusiness.dms_svc.exception</a> &gt; <span class="el_class">DmsException</span></div><h1>DmsException</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">222 of 311</td><td class="ctr2">28%</td><td class="bar">21 of 22</td><td class="ctr2">4%</td><td class="ctr1">26</td><td class="ctr2">30</td><td class="ctr1">49</td><td class="ctr2">68</td><td class="ctr1">14</td><td class="ctr2">18</td></tr></tfoot><tbody><tr><td id="a2"><a href="DmsException.java.html#L81" class="el_method">DmsException(String, String, DmsException.ErrorCategory, DmsException.ErrorSeverity, Map, Throwable)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="96" height="10" title="29" alt="29"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="30" height="10" title="2" alt="2"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f3">2</td><td class="ctr2" id="g3">2</td><td class="ctr1" id="h0">8</td><td class="ctr2" id="i0">8</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a9"><a href="DmsException.java.html#L113" class="el_method">getErrorDetailsString()</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="93" height="10" title="28" alt="28"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="60" height="10" title="4" alt="4"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f2">3</td><td class="ctr2" id="g2">3</td><td class="ctr1" id="h3">7</td><td class="ctr2" id="i5">7</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a3"><a href="DmsException.java.html#L92" class="el_method">DmsException(String, String, DmsException.ErrorCategory, DmsException.ErrorSeverity, String)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="90" height="10" title="27" alt="27"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="30" height="10" title="2" alt="2"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f4">2</td><td class="ctr2" id="g4">2</td><td class="ctr1" id="h1">8</td><td class="ctr2" id="i1">8</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a16"><a href="DmsException.java.html#L152" class="el_method">toLogMap()</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="86" height="10" title="26" alt="26"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d6"/><td class="ctr2" id="e6">n/a</td><td class="ctr1" id="f5">1</td><td class="ctr2" id="g6">1</td><td class="ctr1" id="h4">5</td><td class="ctr2" id="i6">5</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a10"><a href="DmsException.java.html#L105" class="el_method">getFormattedMessage()</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="83" height="10" title="25" alt="25"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d7"/><td class="ctr2" id="e7">n/a</td><td class="ctr1" id="f6">1</td><td class="ctr2" id="g7">1</td><td class="ctr1" id="h6">2</td><td class="ctr2" id="i8">2</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a4"><a href="DmsException.java.html#L59" class="el_method">DmsException(String, String, DmsException.ErrorCategory, DmsException.ErrorSeverity, Throwable)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="80" height="10" title="24" alt="24"/></td><td class="ctr2" id="c9">0%</td><td class="bar" id="d8"/><td class="ctr2" id="e8">n/a</td><td class="ctr1" id="f7">1</td><td class="ctr2" id="g8">1</td><td class="ctr1" id="h2">8</td><td class="ctr2" id="i2">8</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a15"><a href="DmsException.java.html#L133" class="el_method">shouldLogStackTrace()</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="66" height="10" title="20" alt="20"/></td><td class="ctr2" id="c10">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="8" alt="8"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f0">5</td><td class="ctr2" id="g0">5</td><td class="ctr1" id="h7">1</td><td class="ctr2" id="i10">1</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a11"><a href="DmsException.java.html#L141" class="el_method">getLogLevel()</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="53" height="10" title="16" alt="16"/></td><td class="ctr2" id="c11">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="60" height="10" title="4" alt="4"/></td><td class="ctr2" id="e5">0%</td><td class="ctr1" id="f1">4</td><td class="ctr2" id="g1">4</td><td class="ctr1" id="h5">4</td><td class="ctr2" id="i7">4</td><td class="ctr1" id="j7">1</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a14"><a href="DmsException.java.html#L119" class="el_method">lambda$getErrorDetailsString$0(StringBuilder, String, Object)</a></td><td class="bar" id="b8"><img src="../jacoco-resources/redbar.gif" width="36" height="10" title="11" alt="11"/></td><td class="ctr2" id="c12">0%</td><td class="bar" id="d9"/><td class="ctr2" id="e9">n/a</td><td class="ctr1" id="f8">1</td><td class="ctr2" id="g9">1</td><td class="ctr1" id="h8">1</td><td class="ctr2" id="i11">1</td><td class="ctr1" id="j8">1</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a6"><a href="DmsException.java.html#L18" class="el_method">getCorrelationId()</a></td><td class="bar" id="b9"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="3" alt="3"/></td><td class="ctr2" id="c13">0%</td><td class="bar" id="d10"/><td class="ctr2" id="e10">n/a</td><td class="ctr1" id="f9">1</td><td class="ctr2" id="g10">1</td><td class="ctr1" id="h9">1</td><td class="ctr2" id="i12">1</td><td class="ctr1" id="j9">1</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a13"><a href="DmsException.java.html#L19" class="el_method">getTimestamp()</a></td><td class="bar" id="b10"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="3" alt="3"/></td><td class="ctr2" id="c14">0%</td><td class="bar" id="d11"/><td class="ctr2" id="e11">n/a</td><td class="ctr1" id="f10">1</td><td class="ctr2" id="g11">1</td><td class="ctr1" id="h10">1</td><td class="ctr2" id="i13">1</td><td class="ctr1" id="j10">1</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a8"><a href="DmsException.java.html#L20" class="el_method">getErrorDetails()</a></td><td class="bar" id="b11"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="3" alt="3"/></td><td class="ctr2" id="c15">0%</td><td class="bar" id="d12"/><td class="ctr2" id="e12">n/a</td><td class="ctr1" id="f11">1</td><td class="ctr2" id="g12">1</td><td class="ctr1" id="h11">1</td><td class="ctr2" id="i14">1</td><td class="ctr1" id="j11">1</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a5"><a href="DmsException.java.html#L21" class="el_method">getCategory()</a></td><td class="bar" id="b12"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="3" alt="3"/></td><td class="ctr2" id="c16">0%</td><td class="bar" id="d13"/><td class="ctr2" id="e13">n/a</td><td class="ctr1" id="f12">1</td><td class="ctr2" id="g13">1</td><td class="ctr1" id="h12">1</td><td class="ctr2" id="i15">1</td><td class="ctr1" id="j12">1</td><td class="ctr2" id="k12">1</td></tr><tr><td id="a12"><a href="DmsException.java.html#L22" class="el_method">getSeverity()</a></td><td class="bar" id="b13"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="3" alt="3"/></td><td class="ctr2" id="c17">0%</td><td class="bar" id="d14"/><td class="ctr2" id="e14">n/a</td><td class="ctr1" id="f13">1</td><td class="ctr2" id="g14">1</td><td class="ctr1" id="h13">1</td><td class="ctr2" id="i16">1</td><td class="ctr1" id="j13">1</td><td class="ctr2" id="k13">1</td></tr><tr><td id="a1"><a href="DmsException.java.html#L70" class="el_method">DmsException(String, String, DmsException.ErrorCategory, DmsException.ErrorSeverity, Map)</a></td><td class="bar" id="b14"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="90" height="10" title="27" alt="27"/></td><td class="ctr2" id="c3">96%</td><td class="bar" id="d5"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="15" height="10" title="1" alt="1"/></td><td class="ctr2" id="e0">50%</td><td class="ctr1" id="f14">1</td><td class="ctr2" id="g5">2</td><td class="ctr1" id="h14">0</td><td class="ctr2" id="i3">8</td><td class="ctr1" id="j14">0</td><td class="ctr2" id="k14">1</td></tr><tr><td id="a17"><a href="DmsException.java.html#L165" class="el_method">toString()</a></td><td class="bar" id="b15"><img src="../jacoco-resources/greenbar.gif" width="120" height="10" title="36" alt="36"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d15"/><td class="ctr2" id="e15">n/a</td><td class="ctr1" id="f15">0</td><td class="ctr2" id="g15">1</td><td class="ctr1" id="h15">0</td><td class="ctr2" id="i9">2</td><td class="ctr1" id="j15">0</td><td class="ctr2" id="k15">1</td></tr><tr><td id="a0"><a href="DmsException.java.html#L48" class="el_method">DmsException(String, String, DmsException.ErrorCategory, DmsException.ErrorSeverity)</a></td><td class="bar" id="b16"><img src="../jacoco-resources/greenbar.gif" width="76" height="10" title="23" alt="23"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d16"/><td class="ctr2" id="e16">n/a</td><td class="ctr1" id="f16">0</td><td class="ctr2" id="g16">1</td><td class="ctr1" id="h16">0</td><td class="ctr2" id="i4">8</td><td class="ctr1" id="j16">0</td><td class="ctr2" id="k16">1</td></tr><tr><td id="a7"><a href="DmsException.java.html#L17" class="el_method">getErrorCode()</a></td><td class="bar" id="b17"><img src="../jacoco-resources/greenbar.gif" width="10" height="10" title="3" alt="3"/></td><td class="ctr2" id="c2">100%</td><td class="bar" id="d17"/><td class="ctr2" id="e17">n/a</td><td class="ctr1" id="f17">0</td><td class="ctr2" id="g17">1</td><td class="ctr1" id="h17">0</td><td class="ctr2" id="i17">1</td><td class="ctr1" id="j17">0</td><td class="ctr2" id="k17">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>