<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>UploadTrendData.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.dto</a> &gt; <span class="el_source">UploadTrendData.java</span></div><h1>UploadTrendData.java</h1><pre class="source lang-java linenums">package com.ascentbusiness.dms_svc.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.OffsetDateTime;

/**
 * DTO for upload trend data.
 * Corresponds to UploadTrendData GraphQL type.
 */
<span class="nc bnc" id="L14" title="All 38 branches missed.">@Data</span>
<span class="nc" id="L15">@Builder</span>
<span class="nc" id="L16">@NoArgsConstructor</span>
<span class="nc" id="L17">@AllArgsConstructor</span>
public class UploadTrendData {

    /**
     * Date for this trend data point.
     */
<span class="nc" id="L23">    private OffsetDateTime date;</span>

    /**
     * Number of uploads on this date.
     */
<span class="nc" id="L28">    private Long uploadCount;</span>

    /**
     * Total size of uploads on this date in bytes.
     */
<span class="nc" id="L33">    private Long totalSize;</span>

    /**
     * Average processing time for uploads on this date in milliseconds.
     */
<span class="nc" id="L38">    private Long averageProcessingTime;</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>