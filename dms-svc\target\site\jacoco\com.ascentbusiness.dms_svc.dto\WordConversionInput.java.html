<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>WordConversionInput.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.dto</a> &gt; <span class="el_source">WordConversionInput.java</span></div><h1>WordConversionInput.java</h1><pre class="source lang-java linenums">package com.ascentbusiness.dms_svc.dto;

import com.ascentbusiness.dms_svc.enums.VirusScannerType;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

/**
 * Input DTO for Word conversion operations from conversion-schema.graphqls.
 */
<span class="nc bnc" id="L10" title="All 46 branches missed.">@Data</span>
public class WordConversionInput {
<span class="nc" id="L12">    private MultipartFile file;</span>
<span class="nc" id="L13">    private String filePath;</span>
<span class="nc" id="L14">    private VirusScannerType scannerType;</span>
<span class="nc" id="L15">    private ConversionOptionsInput options;</span>
<span class="nc" id="L16">    private String outputFormat = &quot;pdf&quot;;</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>