<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>JwtTokenProvider</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.html" class="el_package">com.ascentbusiness.dms_svc.security</a> &gt; <span class="el_class">JwtTokenProvider</span></div><h1>JwtTokenProvider</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">224 of 224</td><td class="ctr2">0%</td><td class="bar">4 of 4</td><td class="ctr2">0%</td><td class="ctr1">13</td><td class="ctr2">13</td><td class="ctr1">74</td><td class="ctr2">74</td><td class="ctr1">11</td><td class="ctr2">11</td></tr></tfoot><tbody><tr><td id="a10"><a href="JwtTokenProvider.java.html#L123" class="el_method">validateToken(String)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="45" alt="45"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d2"/><td class="ctr2" id="e2">n/a</td><td class="ctr1" id="f2">1</td><td class="ctr2" id="g2">1</td><td class="ctr1" id="h0">15</td><td class="ctr2" id="i0">15</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a2"><a href="JwtTokenProvider.java.html#L55" class="el_method">generateTokenWithRolesAndPermissions(String, List, List)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="80" height="10" title="30" alt="30"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d3"/><td class="ctr2" id="e3">n/a</td><td class="ctr1" id="f3">1</td><td class="ctr2" id="g3">1</td><td class="ctr1" id="h3">9</td><td class="ctr2" id="i3">9</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a0"><a href="JwtTokenProvider.java.html#L32" class="el_method">generateToken(Authentication)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="77" height="10" title="29" alt="29"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d4"/><td class="ctr2" id="e4">n/a</td><td class="ctr1" id="f4">1</td><td class="ctr2" id="g4">1</td><td class="ctr1" id="h4">8</td><td class="ctr2" id="i4">8</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a5"><a href="JwtTokenProvider.java.html#L80" class="el_method">getRolesFromToken(String)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="77" height="10" title="29" alt="29"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="2" alt="2"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f0">2</td><td class="ctr2" id="g0">2</td><td class="ctr1" id="h1">10</td><td class="ctr2" id="i1">10</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a4"><a href="JwtTokenProvider.java.html#L97" class="el_method">getPermissionsFromToken(String)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="77" height="10" title="29" alt="29"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="2" alt="2"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f1">2</td><td class="ctr2" id="g1">2</td><td class="ctr1" id="h2">10</td><td class="ctr2" id="i2">10</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a1"><a href="JwtTokenProvider.java.html#L44" class="el_method">generateTokenFromUsername(String)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="64" height="10" title="24" alt="24"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d5"/><td class="ctr2" id="e5">n/a</td><td class="ctr1" id="f5">1</td><td class="ctr2" id="g5">1</td><td class="ctr1" id="h5">7</td><td class="ctr2" id="i5">7</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a7"><a href="JwtTokenProvider.java.html#L68" class="el_method">getUsernameFromToken(String)</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="34" height="10" title="13" alt="13"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d6"/><td class="ctr2" id="e6">n/a</td><td class="ctr1" id="f6">1</td><td class="ctr2" id="g6">1</td><td class="ctr1" id="h6">6</td><td class="ctr2" id="i6">6</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a3"><a href="JwtTokenProvider.java.html#L112" class="el_method">getExpirationDateFromToken(String)</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="34" height="10" title="13" alt="13"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d7"/><td class="ctr2" id="e7">n/a</td><td class="ctr1" id="f7">1</td><td class="ctr2" id="g7">1</td><td class="ctr1" id="h7">6</td><td class="ctr2" id="i7">6</td><td class="ctr1" id="j7">1</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a6"><a href="JwtTokenProvider.java.html#L28" class="el_method">getSigningKey()</a></td><td class="bar" id="b8"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="5" alt="5"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d8"/><td class="ctr2" id="e8">n/a</td><td class="ctr1" id="f8">1</td><td class="ctr2" id="g8">1</td><td class="ctr1" id="h8">1</td><td class="ctr2" id="i8">1</td><td class="ctr1" id="j8">1</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a9"><a href="JwtTokenProvider.java.html#L19" class="el_method">static {...}</a></td><td class="bar" id="b9"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="4" alt="4"/></td><td class="ctr2" id="c9">0%</td><td class="bar" id="d9"/><td class="ctr2" id="e9">n/a</td><td class="ctr1" id="f9">1</td><td class="ctr2" id="g9">1</td><td class="ctr1" id="h9">1</td><td class="ctr2" id="i9">1</td><td class="ctr1" id="j9">1</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a8"><a href="JwtTokenProvider.java.html#L17" class="el_method">JwtTokenProvider()</a></td><td class="bar" id="b10"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="3" alt="3"/></td><td class="ctr2" id="c10">0%</td><td class="bar" id="d10"/><td class="ctr2" id="e10">n/a</td><td class="ctr1" id="f10">1</td><td class="ctr2" id="g10">1</td><td class="ctr1" id="h10">1</td><td class="ctr2" id="i10">1</td><td class="ctr1" id="j10">1</td><td class="ctr2" id="k10">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>