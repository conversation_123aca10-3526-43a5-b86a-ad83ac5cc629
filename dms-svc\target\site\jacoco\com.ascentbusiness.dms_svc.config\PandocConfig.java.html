<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>PandocConfig.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.config</a> &gt; <span class="el_source">PandocConfig.java</span></div><h1>PandocConfig.java</h1><pre class="source lang-java linenums">package com.ascentbusiness.dms_svc.config;

import com.ascentbusiness.dms_svc.enums.VirusScannerType;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * Configuration properties for Pandoc-based document conversion functionality.
 * 
 * This configuration manages Pandoc executable settings, conversion timeouts,
 * and fallback behavior when Pandoc is not available.
 */
<span class="nc bnc" id="L14" title="All 50 branches missed.">@Data</span>
@Configuration
@ConfigurationProperties(prefix = &quot;dms.pandoc&quot;)
public class PandocConfig {

    /**
     * Path to the Pandoc executable.
     * Default: &quot;pandoc&quot; (assumes it's in PATH)
     */
<span class="nc" id="L23">    private String executablePath = &quot;pandoc&quot;;</span>

    /**
     * Whether Pandoc conversion is enabled.
     * Default: true
     */
<span class="nc" id="L29">    private boolean enabled = true;</span>

    /**
     * Whether to use fallback to existing implementations when Pandoc is not available.
     * Default: true
     */
<span class="nc" id="L35">    private boolean enableFallback = true;</span>

    /**
     * Conversion timeout in seconds.
     * Default: 300 (5 minutes)
     */
<span class="nc" id="L41">    private int timeoutSeconds = 300;</span>

    /**
     * Maximum file size for Pandoc conversion in bytes.
     * Default: 52428800 (50MB)
     */
<span class="nc" id="L47">    private long maxFileSize = 52428800L;</span>

    /**
     * Default virus scanner type for Pandoc conversions.
     * Default: MOCK
     */
<span class="nc" id="L53">    private VirusScannerType virusScanner = VirusScannerType.MOCK;</span>

    /**
     * Temporary directory for Pandoc conversion operations.
     * If empty, uses system temp directory.
     */
<span class="nc" id="L59">    private String tempDirectory = &quot;&quot;;</span>

    /**
     * Auto-cleanup converted files after specified hours.
     * Default: 24 hours
     */
<span class="nc" id="L65">    private int cleanupAfterHours = 24;</span>

    /**
     * Whether to check Pandoc availability on startup.
     * Default: true
     */
<span class="nc" id="L71">    private boolean checkAvailabilityOnStartup = true;</span>

    /**
     * Additional Pandoc command line arguments.
     * Default: empty array
     */
<span class="nc" id="L77">    private String[] additionalArgs = {};</span>

    /**
     * Get the maximum file size in a human-readable format.
     * 
     * @return formatted file size string
     */
    public String getMaxFileSizeFormatted() {
<span class="nc bnc" id="L85" title="All 2 branches missed.">        if (maxFileSize &gt;= 1024 * 1024 * 1024) {</span>
<span class="nc" id="L86">            return String.format(&quot;%.1f GB&quot;, maxFileSize / (1024.0 * 1024.0 * 1024.0));</span>
<span class="nc bnc" id="L87" title="All 2 branches missed.">        } else if (maxFileSize &gt;= 1024 * 1024) {</span>
<span class="nc" id="L88">            return String.format(&quot;%.1f MB&quot;, maxFileSize / (1024.0 * 1024.0));</span>
<span class="nc bnc" id="L89" title="All 2 branches missed.">        } else if (maxFileSize &gt;= 1024) {</span>
<span class="nc" id="L90">            return String.format(&quot;%.1f KB&quot;, maxFileSize / 1024.0);</span>
        } else {
<span class="nc" id="L92">            return maxFileSize + &quot; bytes&quot;;</span>
        }
    }

    /**
     * Get the timeout in a human-readable format.
     * 
     * @return formatted timeout string
     */
    public String getTimeoutFormatted() {
<span class="nc bnc" id="L102" title="All 2 branches missed.">        if (timeoutSeconds &gt;= 3600) {</span>
<span class="nc" id="L103">            return String.format(&quot;%.1f hours&quot;, timeoutSeconds / 3600.0);</span>
<span class="nc bnc" id="L104" title="All 2 branches missed.">        } else if (timeoutSeconds &gt;= 60) {</span>
<span class="nc" id="L105">            return String.format(&quot;%.1f minutes&quot;, timeoutSeconds / 60.0);</span>
        } else {
<span class="nc" id="L107">            return timeoutSeconds + &quot; seconds&quot;;</span>
        }
    }

    /**
     * Check if a file size is within the allowed limit.
     * 
     * @param fileSize the file size to check
     * @return true if within limit, false otherwise
     */
    public boolean isFileSizeAllowed(long fileSize) {
<span class="nc bnc" id="L118" title="All 2 branches missed.">        return fileSize &lt;= maxFileSize;</span>
    }

    /**
     * Get the cleanup interval in milliseconds.
     * 
     * @return cleanup interval in milliseconds
     */
    public long getCleanupIntervalMs() {
<span class="nc" id="L127">        return cleanupAfterHours * 60L * 60L * 1000L;</span>
    }

    /**
     * Check if temp directory is configured.
     * 
     * @return true if temp directory is configured, false if using system default
     */
    public boolean hasTempDirectory() {
<span class="nc bnc" id="L136" title="All 4 branches missed.">        return tempDirectory != null &amp;&amp; !tempDirectory.trim().isEmpty();</span>
    }

    /**
     * Get the full command array for Pandoc execution.
     * 
     * @param inputFile input file path
     * @param outputFile output file path
     * @param fromFormat source format (e.g., &quot;pdf&quot;, &quot;docx&quot;, &quot;markdown&quot;)
     * @param toFormat target format (e.g., &quot;docx&quot;, &quot;pdf&quot;)
     * @return command array for ProcessBuilder
     */
    public String[] buildCommand(String inputFile, String outputFile, String fromFormat, String toFormat) {
<span class="nc" id="L149">        String[] baseCommand = {</span>
            executablePath,
            &quot;--from&quot;, fromFormat,
            &quot;--to&quot;, toFormat,
            &quot;--output&quot;, outputFile,
            inputFile
        };

<span class="nc bnc" id="L157" title="All 2 branches missed.">        if (additionalArgs.length == 0) {</span>
<span class="nc" id="L158">            return baseCommand;</span>
        }

        // Merge base command with additional arguments
<span class="nc" id="L162">        String[] fullCommand = new String[baseCommand.length + additionalArgs.length];</span>
<span class="nc" id="L163">        System.arraycopy(baseCommand, 0, fullCommand, 0, baseCommand.length);</span>
<span class="nc" id="L164">        System.arraycopy(additionalArgs, 0, fullCommand, baseCommand.length, additionalArgs.length);</span>
        
<span class="nc" id="L166">        return fullCommand;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>