<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>GraphQLMultipartConfig</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.html" class="el_package">com.ascentbusiness.dms_svc.config</a> &gt; <span class="el_class">GraphQLMultipartConfig</span></div><h1>GraphQLMultipartConfig</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">3,504 of 3,504</td><td class="ctr2">0%</td><td class="bar">336 of 336</td><td class="ctr2">0%</td><td class="ctr1">204</td><td class="ctr2">204</td><td class="ctr1">806</td><td class="ctr2">806</td><td class="ctr1">36</td><td class="ctr2">36</td></tr></tfoot><tbody><tr><td id="a14"><a href="GraphQLMultipartConfig.java.html#L351" class="el_method">handleBulkUploadDocumentsMultipart(HttpServletRequest, Map, HttpServletResponse)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="367" alt="367"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="46" alt="46"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f0">24</td><td class="ctr2" id="g0">24</td><td class="ctr1" id="h0">79</td><td class="ctr2" id="i0">79</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a19"><a href="GraphQLMultipartConfig.java.html#L105" class="el_method">handleMultipartGraphQL(HttpServletRequest, HttpServletResponse)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="92" height="10" title="283" alt="283"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="46" alt="46"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f1">24</td><td class="ctr2" id="g1">24</td><td class="ctr1" id="h3">58</td><td class="ctr2" id="i3">58</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a7"><a href="GraphQLMultipartConfig.java.html#L898" class="el_method">createEnhancedUploadInput(Map, MultipartFile)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="78" height="10" title="241" alt="241"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="88" height="10" title="34" alt="34"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f2">18</td><td class="ctr2" id="g2">18</td><td class="ctr1" id="h1">65</td><td class="ctr2" id="i1">65</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a22"><a href="GraphQLMultipartConfig.java.html#L638" class="el_method">handleUploadDocumentEnhancedMultipart(Map, HttpServletResponse)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="73" height="10" title="225" alt="225"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d6"><img src="../jacoco-resources/redbar.gif" width="36" height="10" title="14" alt="14"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f6">8</td><td class="ctr2" id="g6">8</td><td class="ctr1" id="h2">62</td><td class="ctr2" id="i2">62</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a23"><a href="GraphQLMultipartConfig.java.html#L484" class="el_method">handleValidateFileMultipart(Map, HttpServletResponse)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="66" height="10" title="202" alt="202"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d7"><img src="../jacoco-resources/redbar.gif" width="36" height="10" title="14" alt="14"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f7">8</td><td class="ctr2" id="g7">8</td><td class="ctr1" id="h4">54</td><td class="ctr2" id="i4">54</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a33"><a href="GraphQLMultipartConfig.java.html#L809" class="el_method">setNestedValue(Map, String, Object)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="61" height="10" title="187" alt="187"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="88" height="10" title="34" alt="34"/></td><td class="ctr2" id="e5">0%</td><td class="ctr1" id="f3">18</td><td class="ctr2" id="g3">18</td><td class="ctr1" id="h8">38</td><td class="ctr2" id="i8">38</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a4"><a href="GraphQLMultipartConfig.java.html#L1433" class="el_method">convertFileValidationResultToMap(FileValidationResult)</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="60" height="10" title="184" alt="184"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d9"><img src="../jacoco-resources/redbar.gif" width="20" height="10" title="8" alt="8"/></td><td class="ctr2" id="e6">0%</td><td class="ctr1" id="f9">5</td><td class="ctr2" id="g9">5</td><td class="ctr1" id="h9">32</td><td class="ctr2" id="i9">32</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a21"><a href="GraphQLMultipartConfig.java.html#L571" class="el_method">handleUploadChunkMultipart(Map, HttpServletResponse)</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="57" height="10" title="177" alt="177"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d15"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="6" alt="6"/></td><td class="ctr2" id="e7">0%</td><td class="ctr1" id="f15">4</td><td class="ctr2" id="g15">4</td><td class="ctr1" id="h6">42</td><td class="ctr2" id="i6">42</td><td class="ctr1" id="j7">1</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a18"><a href="GraphQLMultipartConfig.java.html#L231" class="el_method">handleMarkdownConversionMultipart(Map, HttpServletResponse, String)</a></td><td class="bar" id="b8"><img src="../jacoco-resources/redbar.gif" width="54" height="10" title="167" alt="167"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d16"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="6" alt="6"/></td><td class="ctr2" id="e8">0%</td><td class="ctr1" id="f16">4</td><td class="ctr2" id="g16">4</td><td class="ctr1" id="h5">43</td><td class="ctr2" id="i5">43</td><td class="ctr1" id="j8">1</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a8"><a href="GraphQLMultipartConfig.java.html#L1366" class="el_method">createFileValidationOptions(Map)</a></td><td class="bar" id="b9"><img src="../jacoco-resources/redbar.gif" width="45" height="10" title="138" alt="138"/></td><td class="ctr2" id="c9">0%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="57" height="10" title="22" alt="22"/></td><td class="ctr2" id="e9">0%</td><td class="ctr1" id="f4">12</td><td class="ctr2" id="g4">12</td><td class="ctr1" id="h7">39</td><td class="ctr2" id="i7">39</td><td class="ctr1" id="j9">1</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a1"><a href="GraphQLMultipartConfig.java.html#L1185" class="el_method">convertBatchResultToMap(BatchConversionResult)</a></td><td class="bar" id="b10"><img src="../jacoco-resources/redbar.gif" width="43" height="10" title="134" alt="134"/></td><td class="ctr2" id="c10">0%</td><td class="bar" id="d10"><img src="../jacoco-resources/redbar.gif" width="20" height="10" title="8" alt="8"/></td><td class="ctr2" id="e10">0%</td><td class="ctr1" id="f10">5</td><td class="ctr2" id="g10">5</td><td class="ctr1" id="h13">23</td><td class="ctr2" id="i13">23</td><td class="ctr1" id="j10">1</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a13"><a href="GraphQLMultipartConfig.java.html#L1088" class="el_method">handleBatchConversionMultipart(Map, HttpServletResponse)</a></td><td class="bar" id="b11"><img src="../jacoco-resources/redbar.gif" width="39" height="10" title="121" alt="121"/></td><td class="ctr2" id="c11">0%</td><td class="bar" id="d8"><img src="../jacoco-resources/redbar.gif" width="26" height="10" title="10" alt="10"/></td><td class="ctr2" id="e11">0%</td><td class="ctr1" id="f8">6</td><td class="ctr2" id="g8">6</td><td class="ctr1" id="h10">29</td><td class="ctr2" id="i10">29</td><td class="ctr1" id="j11">1</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a6"><a href="GraphQLMultipartConfig.java.html#L1008" class="el_method">convertUploadResultToMap(DocumentUploadResult)</a></td><td class="bar" id="b12"><img src="../jacoco-resources/redbar.gif" width="37" height="10" title="114" alt="114"/></td><td class="ctr2" id="c12">0%</td><td class="bar" id="d11"><img src="../jacoco-resources/redbar.gif" width="20" height="10" title="8" alt="8"/></td><td class="ctr2" id="e12">0%</td><td class="ctr1" id="f11">5</td><td class="ctr2" id="g11">5</td><td class="ctr1" id="h14">19</td><td class="ctr2" id="i14">19</td><td class="ctr1" id="j12">1</td><td class="ctr2" id="k12">1</td></tr><tr><td id="a2"><a href="GraphQLMultipartConfig.java.html#L1217" class="el_method">convertConversionResultToMap(ConversionResult)</a></td><td class="bar" id="b13"><img src="../jacoco-resources/redbar.gif" width="37" height="10" title="114" alt="114"/></td><td class="ctr2" id="c13">0%</td><td class="bar" id="d17"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="6" alt="6"/></td><td class="ctr2" id="e13">0%</td><td class="ctr1" id="f17">4</td><td class="ctr2" id="g17">4</td><td class="ctr1" id="h16">17</td><td class="ctr2" id="i16">17</td><td class="ctr1" id="j13">1</td><td class="ctr2" id="k13">1</td></tr><tr><td id="a3"><a href="GraphQLMultipartConfig.java.html#L1481" class="el_method">convertDocumentUploadResultToMap(DocumentUploadResult)</a></td><td class="bar" id="b14"><img src="../jacoco-resources/redbar.gif" width="37" height="10" title="114" alt="114"/></td><td class="ctr2" id="c14">0%</td><td class="bar" id="d12"><img src="../jacoco-resources/redbar.gif" width="20" height="10" title="8" alt="8"/></td><td class="ctr2" id="e14">0%</td><td class="ctr1" id="f12">5</td><td class="ctr2" id="g12">5</td><td class="ctr1" id="h15">19</td><td class="ctr2" id="i15">19</td><td class="ctr1" id="j14">1</td><td class="ctr2" id="k14">1</td></tr><tr><td id="a16"><a href="GraphQLMultipartConfig.java.html#L1141" class="el_method">handleGenericConversionMultipart(Map, HttpServletResponse)</a></td><td class="bar" id="b15"><img src="../jacoco-resources/redbar.gif" width="32" height="10" title="98" alt="98"/></td><td class="ctr2" id="c15">0%</td><td class="bar" id="d20"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="4" alt="4"/></td><td class="ctr2" id="e15">0%</td><td class="ctr1" id="f20">3</td><td class="ctr2" id="g20">3</td><td class="ctr1" id="h12">25</td><td class="ctr2" id="i12">25</td><td class="ctr1" id="j15">1</td><td class="ctr2" id="k15">1</td></tr><tr><td id="a15"><a href="GraphQLMultipartConfig.java.html#L304" class="el_method">handleDocumentUploadMultipart(Map, HttpServletResponse)</a></td><td class="bar" id="b16"><img src="../jacoco-resources/redbar.gif" width="30" height="10" title="94" alt="94"/></td><td class="ctr2" id="c16">0%</td><td class="bar" id="d21"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="4" alt="4"/></td><td class="ctr2" id="e16">0%</td><td class="ctr1" id="f21">3</td><td class="ctr2" id="g21">3</td><td class="ctr1" id="h11">27</td><td class="ctr2" id="i11">27</td><td class="ctr1" id="j16">1</td><td class="ctr2" id="k16">1</td></tr><tr><td id="a5"><a href="GraphQLMultipartConfig.java.html#L1240" class="el_method">convertMarkdownResultToMap(MarkdownConversionResult)</a></td><td class="bar" id="b17"><img src="../jacoco-resources/redbar.gif" width="30" height="10" title="93" alt="93"/></td><td class="ctr2" id="c17">0%</td><td class="bar" id="d27"/><td class="ctr2" id="e27">n/a</td><td class="ctr1" id="f27">1</td><td class="ctr2" id="g27">1</td><td class="ctr1" id="h17">16</td><td class="ctr2" id="i17">16</td><td class="ctr1" id="j17">1</td><td class="ctr2" id="k17">1</td></tr><tr><td id="a25"><a href="GraphQLMultipartConfig.java.html#L1306" class="el_method">isAuthenticated(HttpServletRequest)</a></td><td class="bar" id="b18"><img src="../jacoco-resources/redbar.gif" width="16" height="10" title="49" alt="49"/></td><td class="ctr2" id="c18">0%</td><td class="bar" id="d5"><img src="../jacoco-resources/redbar.gif" width="41" height="10" title="16" alt="16"/></td><td class="ctr2" id="e17">0%</td><td class="ctr1" id="f5">9</td><td class="ctr2" id="g5">9</td><td class="ctr1" id="h18">14</td><td class="ctr2" id="i18">14</td><td class="ctr1" id="j18">1</td><td class="ctr2" id="k18">1</td></tr><tr><td id="a10"><a href="GraphQLMultipartConfig.java.html#L738" class="el_method">extractOperationName(String)</a></td><td class="bar" id="b19"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="47" alt="47"/></td><td class="ctr2" id="c19">0%</td><td class="bar" id="d13"><img src="../jacoco-resources/redbar.gif" width="20" height="10" title="8" alt="8"/></td><td class="ctr2" id="e18">0%</td><td class="ctr1" id="f13">5</td><td class="ctr2" id="g13">5</td><td class="ctr1" id="h23">8</td><td class="ctr2" id="i23">8</td><td class="ctr1" id="j19">1</td><td class="ctr2" id="k19">1</td></tr><tr><td id="a29"><a href="GraphQLMultipartConfig.java.html#L154" class="el_method">lambda$handleMultipartGraphQL$1(MultipartHttpServletRequest, Map, String, Object)</a></td><td class="bar" id="b20"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="46" alt="46"/></td><td class="ctr2" id="c20">0%</td><td class="bar" id="d18"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="6" alt="6"/></td><td class="ctr2" id="e19">0%</td><td class="ctr1" id="f18">4</td><td class="ctr2" id="g18">4</td><td class="ctr1" id="h19">14</td><td class="ctr2" id="i19">14</td><td class="ctr1" id="j20">1</td><td class="ctr2" id="k20">1</td></tr><tr><td id="a9"><a href="GraphQLMultipartConfig.java.html#L1265" class="el_method">createPdfConversionInput(Map)</a></td><td class="bar" id="b21"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="42" alt="42"/></td><td class="ctr2" id="c21">0%</td><td class="bar" id="d14"><img src="../jacoco-resources/redbar.gif" width="20" height="10" title="8" alt="8"/></td><td class="ctr2" id="e20">0%</td><td class="ctr1" id="f14">5</td><td class="ctr2" id="g14">5</td><td class="ctr1" id="h20">11</td><td class="ctr2" id="i20">11</td><td class="ctr1" id="j21">1</td><td class="ctr2" id="k21">1</td></tr><tr><td id="a20"><a href="GraphQLMultipartConfig.java.html#L1040" class="el_method">handlePdfConversionMultipart(Map, HttpServletResponse)</a></td><td class="bar" id="b22"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="41" alt="41"/></td><td class="ctr2" id="c22">0%</td><td class="bar" id="d24"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e21">0%</td><td class="ctr1" id="f24">2</td><td class="ctr2" id="g24">2</td><td class="ctr1" id="h21">11</td><td class="ctr2" id="i21">11</td><td class="ctr1" id="j22">1</td><td class="ctr2" id="k22">1</td></tr><tr><td id="a32"><a href="GraphQLMultipartConfig.java.html#L1291" class="el_method">sendGraphQLResponse(HttpServletResponse, String, ConversionResult)</a></td><td class="bar" id="b23"><img src="../jacoco-resources/redbar.gif" width="11" height="10" title="34" alt="34"/></td><td class="ctr2" id="c23">0%</td><td class="bar" id="d28"/><td class="ctr2" id="e28">n/a</td><td class="ctr1" id="f28">1</td><td class="ctr2" id="g28">1</td><td class="ctr1" id="h24">8</td><td class="ctr2" id="i24">8</td><td class="ctr1" id="j23">1</td><td class="ctr2" id="k23">1</td></tr><tr><td id="a24"><a href="GraphQLMultipartConfig.java.html#L1065" class="el_method">handleWordConversionMultipart(Map, HttpServletResponse)</a></td><td class="bar" id="b24"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="33" alt="33"/></td><td class="ctr2" id="c24">0%</td><td class="bar" id="d25"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e22">0%</td><td class="ctr1" id="f25">2</td><td class="ctr2" id="g25">2</td><td class="ctr1" id="h22">10</td><td class="ctr2" id="i22">10</td><td class="ctr1" id="j24">1</td><td class="ctr2" id="k24">1</td></tr><tr><td id="a27"><a href="GraphQLMultipartConfig.java.html#L1338" class="el_method">isTestEnvironment()</a></td><td class="bar" id="b25"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="28" alt="28"/></td><td class="ctr2" id="c25">0%</td><td class="bar" id="d22"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="4" alt="4"/></td><td class="ctr2" id="e23">0%</td><td class="ctr1" id="f22">3</td><td class="ctr2" id="g22">3</td><td class="ctr1" id="h29">5</td><td class="ctr2" id="i29">5</td><td class="ctr1" id="j25">1</td><td class="ctr2" id="k25">1</td></tr><tr><td id="a11"><a href="GraphQLMultipartConfig.java.html#L786" class="el_method">findMultipartRequest(HttpServletRequest)</a></td><td class="bar" id="b26"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="26" alt="26"/></td><td class="ctr2" id="c26">0%</td><td class="bar" id="d19"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="6" alt="6"/></td><td class="ctr2" id="e24">0%</td><td class="ctr1" id="f19">4</td><td class="ctr2" id="g19">4</td><td class="ctr1" id="h25">8</td><td class="ctr2" id="i25">8</td><td class="ctr1" id="j26">1</td><td class="ctr2" id="k26">1</td></tr><tr><td id="a34"><a href="GraphQLMultipartConfig.java.html#L1351" class="el_method">setupTestSecurityContext()</a></td><td class="bar" id="b27"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="22" alt="22"/></td><td class="ctr2" id="c27">0%</td><td class="bar" id="d23"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="4" alt="4"/></td><td class="ctr2" id="e25">0%</td><td class="ctr1" id="f23">3</td><td class="ctr2" id="g23">3</td><td class="ctr1" id="h26">7</td><td class="ctr2" id="i26">7</td><td class="ctr1" id="j27">1</td><td class="ctr2" id="k27">1</td></tr><tr><td id="a31"><a href="GraphQLMultipartConfig.java.html#L756" class="el_method">sendGraphQLErrorResponse(HttpServletResponse, String)</a></td><td class="bar" id="b28"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="21" alt="21"/></td><td class="ctr2" id="c28">0%</td><td class="bar" id="d26"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e26">0%</td><td class="ctr1" id="f26">2</td><td class="ctr2" id="g26">2</td><td class="ctr1" id="h27">6</td><td class="ctr2" id="i27">6</td><td class="ctr1" id="j28">1</td><td class="ctr2" id="k28">1</td></tr><tr><td id="a30"><a href="GraphQLMultipartConfig.java.html#L773" class="el_method">sendErrorResponse(HttpServletResponse, String)</a></td><td class="bar" id="b29"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="20" alt="20"/></td><td class="ctr2" id="c29">0%</td><td class="bar" id="d29"/><td class="ctr2" id="e29">n/a</td><td class="ctr1" id="f29">1</td><td class="ctr2" id="g29">1</td><td class="ctr1" id="h28">6</td><td class="ctr2" id="i28">6</td><td class="ctr1" id="j29">1</td><td class="ctr2" id="k29">1</td></tr><tr><td id="a0"><a href="GraphQLMultipartConfig.java.html#L77" class="el_method">addInterceptors(InterceptorRegistry)</a></td><td class="bar" id="b30"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="15" alt="15"/></td><td class="ctr2" id="c30">0%</td><td class="bar" id="d30"/><td class="ctr2" id="e30">n/a</td><td class="ctr1" id="f30">1</td><td class="ctr2" id="g30">1</td><td class="ctr1" id="h31">3</td><td class="ctr2" id="i31">3</td><td class="ctr1" id="j30">1</td><td class="ctr2" id="k30">1</td></tr><tr><td id="a26"><a href="GraphQLMultipartConfig.java.html#L886" class="el_method">isNumeric(String)</a></td><td class="bar" id="b31"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="8" alt="8"/></td><td class="ctr2" id="c31">0%</td><td class="bar" id="d31"/><td class="ctr2" id="e31">n/a</td><td class="ctr1" id="f31">1</td><td class="ctr2" id="g31">1</td><td class="ctr1" id="h30">4</td><td class="ctr2" id="i30">4</td><td class="ctr1" id="j31">1</td><td class="ctr2" id="k31">1</td></tr><tr><td id="a17"><a href="GraphQLMultipartConfig.java.html#L222" class="el_method">handleMarkdownConversionMultipart(Map, HttpServletResponse)</a></td><td class="bar" id="b32"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="6" alt="6"/></td><td class="ctr2" id="c32">0%</td><td class="bar" id="d32"/><td class="ctr2" id="e32">n/a</td><td class="ctr1" id="f32">1</td><td class="ctr2" id="g32">1</td><td class="ctr1" id="h32">2</td><td class="ctr2" id="i32">2</td><td class="ctr1" id="j32">1</td><td class="ctr2" id="k32">1</td></tr><tr><td id="a28"><a href="GraphQLMultipartConfig.java.html#L171" class="el_method">lambda$handleMultipartGraphQL$0(Map, MultipartFile, String)</a></td><td class="bar" id="b33"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="6" alt="6"/></td><td class="ctr2" id="c33">0%</td><td class="bar" id="d33"/><td class="ctr2" id="e33">n/a</td><td class="ctr1" id="f33">1</td><td class="ctr2" id="g33">1</td><td class="ctr1" id="h33">1</td><td class="ctr2" id="i33">1</td><td class="ctr1" id="j33">1</td><td class="ctr2" id="k33">1</td></tr><tr><td id="a35"><a href="GraphQLMultipartConfig.java.html#L50" class="el_method">static {...}</a></td><td class="bar" id="b34"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="4" alt="4"/></td><td class="ctr2" id="c34">0%</td><td class="bar" id="d34"/><td class="ctr2" id="e34">n/a</td><td class="ctr1" id="f34">1</td><td class="ctr2" id="g34">1</td><td class="ctr1" id="h34">1</td><td class="ctr2" id="i34">1</td><td class="ctr1" id="j34">1</td><td class="ctr2" id="k34">1</td></tr><tr><td id="a12"><a href="GraphQLMultipartConfig.java.html#L52" class="el_method">GraphQLMultipartConfig()</a></td><td class="bar" id="b35"/><td class="ctr2" id="c35">0%</td><td class="bar" id="d35"/><td class="ctr2" id="e35">n/a</td><td class="ctr1" id="f35">1</td><td class="ctr2" id="g35">1</td><td class="ctr1" id="h35">1</td><td class="ctr2" id="i35">1</td><td class="ctr1" id="j35">1</td><td class="ctr2" id="k35">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>