<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>JpaAuditingConfig.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.config</a> &gt; <span class="el_source">JpaAuditingConfig.java</span></div><h1>JpaAuditingConfig.java</h1><pre class="source lang-java linenums">package com.ascentbusiness.dms_svc.config;

import com.ascentbusiness.dms_svc.security.UserPrincipal;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.domain.AuditorAware;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.lang.NonNull;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;

import java.util.Optional;

@Configuration
@EnableJpaAuditing(auditorAwareRef = &quot;auditorProvider&quot;)
<span class="nc" id="L18">public class JpaAuditingConfig {</span>

    @Bean
    public AuditorAware&lt;String&gt; auditorProvider() {
<span class="nc" id="L22">        return new SpringSecurityAuditorAware();</span>
    }

<span class="nc" id="L25">    public static class SpringSecurityAuditorAware implements AuditorAware&lt;String&gt; {</span>
        
<span class="nc" id="L27">        private static final Logger logger = LoggerFactory.getLogger(SpringSecurityAuditorAware.class);</span>
        @Override
        @NonNull
        public Optional&lt;String&gt; getCurrentAuditor() {
<span class="nc" id="L31">            logger.debug(&quot;JPA Auditing: getCurrentAuditor() called&quot;);</span>
            
<span class="nc" id="L33">            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();</span>
<span class="nc" id="L34">            logger.debug(&quot;JPA Auditing: Authentication object: {}&quot;, authentication);</span>
            
<span class="nc bnc" id="L36" title="All 2 branches missed.">            if (authentication == null) {</span>
<span class="nc" id="L37">                logger.warn(&quot;JPA Auditing: Authentication is null, returning 'system'&quot;);</span>
<span class="nc" id="L38">                return Optional.of(&quot;system&quot;);</span>
            }
            
<span class="nc bnc" id="L41" title="All 2 branches missed.">            if (!authentication.isAuthenticated()) {</span>
<span class="nc" id="L42">                logger.warn(&quot;JPA Auditing: Authentication not authenticated, returning 'system'&quot;);</span>
<span class="nc" id="L43">                return Optional.of(&quot;system&quot;);</span>
            }
            
<span class="nc" id="L46">            logger.debug(&quot;JPA Auditing: Authentication principal type: {}&quot;, </span>
<span class="nc bnc" id="L47" title="All 2 branches missed.">                    authentication.getPrincipal() != null ? authentication.getPrincipal().getClass().getName() : &quot;null&quot;);</span>
            
<span class="nc bnc" id="L49" title="All 2 branches missed.">            if (authentication.getPrincipal() instanceof UserPrincipal) {</span>
<span class="nc" id="L50">                UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();</span>
<span class="nc" id="L51">                String username = userPrincipal.getUsername();</span>
<span class="nc" id="L52">                logger.info(&quot;JPA Auditing: Found UserPrincipal, username: {}&quot;, username);</span>
<span class="nc" id="L53">                return Optional.of(username);</span>
            }
            
<span class="nc bnc" id="L56" title="All 2 branches missed.">            if (authentication.getPrincipal() instanceof String) {</span>
<span class="nc" id="L57">                String username = (String) authentication.getPrincipal();</span>
<span class="nc" id="L58">                logger.info(&quot;JPA Auditing: Found String principal, username: {}&quot;, username);</span>
<span class="nc" id="L59">                return Optional.of(username);</span>
            }
            
<span class="nc" id="L62">            logger.warn(&quot;JPA Auditing: Unknown principal type, returning 'system'. Principal: {}&quot;, </span>
<span class="nc" id="L63">                    authentication.getPrincipal());</span>
<span class="nc" id="L64">            return Optional.of(&quot;system&quot;);</span>
        }
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>