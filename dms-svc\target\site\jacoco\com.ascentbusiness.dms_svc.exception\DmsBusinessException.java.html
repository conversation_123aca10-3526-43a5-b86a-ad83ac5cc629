<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>DmsBusinessException.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.exception</a> &gt; <span class="el_source">DmsBusinessException.java</span></div><h1>DmsBusinessException.java</h1><pre class="source lang-java linenums">package com.ascentbusiness.dms_svc.exception;

import java.util.Map;

/**
 * Exception for business logic errors in the DMS system.
 * Used for business rule violations, workflow errors, and domain-specific errors.
 */
public class DmsBusinessException extends DmsException {

    public DmsBusinessException(String message) {
<span class="nc" id="L12">        super(message, &quot;BUSINESS_ERROR&quot;, ErrorCategory.BUSINESS_RULE, ErrorSeverity.MEDIUM);</span>
<span class="nc" id="L13">    }</span>

    public DmsBusinessException(String message, String errorCode) {
<span class="fc" id="L16">        super(message, errorCode, ErrorCategory.BUSINESS_RULE, ErrorSeverity.MEDIUM);</span>
<span class="fc" id="L17">    }</span>

    public DmsBusinessException(String message, String errorCode, ErrorSeverity severity) {
<span class="nc" id="L20">        super(message, errorCode, ErrorCategory.BUSINESS_RULE, severity);</span>
<span class="nc" id="L21">    }</span>

    public DmsBusinessException(String message, String errorCode, Map&lt;String, Object&gt; errorDetails) {
<span class="fc" id="L24">        super(message, errorCode, ErrorCategory.BUSINESS_RULE, ErrorSeverity.MEDIUM, errorDetails);</span>
<span class="fc" id="L25">    }</span>

    public DmsBusinessException(String message, String errorCode, Map&lt;String, Object&gt; errorDetails, Throwable cause) {
<span class="nc" id="L28">        super(message, errorCode, ErrorCategory.BUSINESS_RULE, ErrorSeverity.MEDIUM, errorDetails, cause);</span>
<span class="nc" id="L29">    }</span>

    public DmsBusinessException(String message, String errorCode, ErrorSeverity severity, Map&lt;String, Object&gt; errorDetails) {
<span class="nc" id="L32">        super(message, errorCode, ErrorCategory.BUSINESS_RULE, severity, errorDetails);</span>
<span class="nc" id="L33">    }</span>

    // Specific business error types
    public static DmsBusinessException documentNotFound(Long documentId) {
<span class="nc" id="L37">        return new DmsBusinessException(</span>
<span class="nc" id="L38">            String.format(&quot;Document not found with ID: %d&quot;, documentId),</span>
            &quot;DOCUMENT_NOT_FOUND&quot;,
<span class="nc" id="L40">            Map.of(&quot;documentId&quot;, documentId)</span>
        );
    }

    public static DmsBusinessException documentNotFound(String identifier, String identifierType) {
<span class="nc" id="L45">        return new DmsBusinessException(</span>
<span class="nc" id="L46">            String.format(&quot;Document not found with %s: %s&quot;, identifierType, identifier),</span>
            &quot;DOCUMENT_NOT_FOUND&quot;,
<span class="nc" id="L48">            Map.of(&quot;identifier&quot;, identifier, &quot;identifierType&quot;, identifierType)</span>
        );
    }

    public static DmsBusinessException duplicateFile(String fileName, String existingDocumentId) {
<span class="nc" id="L53">        return new DmsBusinessException(</span>
<span class="nc" id="L54">            String.format(&quot;File '%s' already exists. Document ID: %s. Use overrideFile=true to create a new version.&quot;, </span>
                         fileName, existingDocumentId),
            &quot;DUPLICATE_FILE&quot;,
<span class="nc" id="L57">            Map.of(&quot;fileName&quot;, fileName, &quot;existingDocumentId&quot;, existingDocumentId)</span>
        );
    }

    public static DmsBusinessException historicalDocumentModification(Long documentId, String operation) {
<span class="nc" id="L62">        return new DmsBusinessException(</span>
<span class="nc" id="L63">            String.format(&quot;Cannot perform operation '%s' on historical document with ID: %d&quot;, operation, documentId),</span>
            &quot;HISTORICAL_DOCUMENT_MODIFICATION&quot;,
<span class="nc" id="L65">            Map.of(&quot;documentId&quot;, documentId, &quot;operation&quot;, operation)</span>
        );
    }

    public static DmsBusinessException documentAlreadyDeleted(Long documentId, String documentName) {
<span class="nc" id="L70">        return new DmsBusinessException(</span>
<span class="nc" id="L71">            String.format(&quot;Document '%s' (ID: %d) is already deleted and cannot be deleted again&quot;, </span>
                         documentName, documentId),
            &quot;DOCUMENT_ALREADY_DELETED&quot;,
<span class="nc" id="L74">            Map.of(&quot;documentId&quot;, documentId, &quot;documentName&quot;, documentName)</span>
        );
    }

    public static DmsBusinessException retentionPolicyViolation(Long documentId, String policyName, String reason) {
<span class="nc" id="L79">        return new DmsBusinessException(</span>
<span class="nc" id="L80">            String.format(&quot;Document ID %d violates retention policy '%s': %s&quot;, documentId, policyName, reason),</span>
            &quot;RETENTION_POLICY_VIOLATION&quot;,
<span class="nc" id="L82">            Map.of(&quot;documentId&quot;, documentId, &quot;policyName&quot;, policyName, &quot;reason&quot;, reason)</span>
        );
    }

    public static DmsBusinessException legalHoldViolation(Long documentId, String legalHoldName) {
<span class="nc" id="L87">        return new DmsBusinessException(</span>
<span class="nc" id="L88">            String.format(&quot;Document ID %d is under legal hold '%s' and cannot be modified or deleted&quot;,</span>
                         documentId, legalHoldName),
            &quot;LEGAL_HOLD_VIOLATION&quot;,
            ErrorSeverity.HIGH,
<span class="nc" id="L92">            Map.of(&quot;documentId&quot;, documentId, &quot;legalHoldName&quot;, legalHoldName)</span>
        );
    }

    public static DmsBusinessException complianceViolation(String frameworkName, String violationType, String details) {
<span class="nc" id="L97">        return new DmsBusinessException(</span>
<span class="nc" id="L98">            String.format(&quot;Compliance violation for framework '%s': %s - %s&quot;,</span>
                         frameworkName, violationType, details),
            &quot;COMPLIANCE_VIOLATION&quot;,
            ErrorSeverity.HIGH,
<span class="nc" id="L102">            Map.of(&quot;frameworkName&quot;, frameworkName, &quot;violationType&quot;, violationType, &quot;details&quot;, details)</span>
        );
    }

    public static DmsBusinessException workflowViolation(String workflowName, String currentState, String attemptedAction) {
<span class="nc" id="L107">        return new DmsBusinessException(</span>
<span class="nc" id="L108">            String.format(&quot;Workflow '%s' violation: Cannot perform action '%s' in state '%s'&quot;, </span>
                         workflowName, attemptedAction, currentState),
            &quot;WORKFLOW_VIOLATION&quot;,
<span class="nc" id="L111">            Map.of(&quot;workflowName&quot;, workflowName, &quot;currentState&quot;, currentState, &quot;attemptedAction&quot;, attemptedAction)</span>
        );
    }

    public static DmsBusinessException resourceLimitExceeded(String resourceType, long currentValue, long limit) {
<span class="nc" id="L116">        return new DmsBusinessException(</span>
<span class="nc" id="L117">            String.format(&quot;Resource limit exceeded for %s: current=%d, limit=%d&quot;, </span>
<span class="nc" id="L118">                         resourceType, currentValue, limit),</span>
            &quot;RESOURCE_LIMIT_EXCEEDED&quot;,
<span class="nc" id="L120">            Map.of(&quot;resourceType&quot;, resourceType, &quot;currentValue&quot;, currentValue, &quot;limit&quot;, limit)</span>
        );
    }

    public static DmsBusinessException dependencyViolation(String entityType, String entityId, String dependentType) {
<span class="nc" id="L125">        return new DmsBusinessException(</span>
<span class="nc" id="L126">            String.format(&quot;Cannot modify/delete %s '%s' because it has dependent %s&quot;, </span>
                         entityType, entityId, dependentType),
            &quot;DEPENDENCY_VIOLATION&quot;,
<span class="nc" id="L129">            Map.of(&quot;entityType&quot;, entityType, &quot;entityId&quot;, entityId, &quot;dependentType&quot;, dependentType)</span>
        );
    }

    public static DmsBusinessException versioningError(Long documentId, int currentVersion, String operation) {
<span class="nc" id="L134">        return new DmsBusinessException(</span>
<span class="nc" id="L135">            String.format(&quot;Versioning error for document ID %d (version %d): %s&quot;, </span>
<span class="nc" id="L136">                         documentId, currentVersion, operation),</span>
            &quot;VERSIONING_ERROR&quot;,
<span class="nc" id="L138">            Map.of(&quot;documentId&quot;, documentId, &quot;currentVersion&quot;, currentVersion, &quot;operation&quot;, operation)</span>
        );
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>