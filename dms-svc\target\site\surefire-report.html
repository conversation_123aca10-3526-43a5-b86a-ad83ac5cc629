<!DOCTYPE html>
<!--
 | Generated by Apache Maven Doxia Site Renderer 1.11.1 at 2025-07-22

 | Rendered using Apache Maven Default Skin
-->
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en" lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="generator" content="Apache Maven Doxia Site Renderer 1.11.1" />
    <title>dms-svc &#x2013; Surefire Report</title>
    <link rel="stylesheet" href="./css/maven-base.css" />
    <link rel="stylesheet" href="./css/maven-theme.css" />
    <link rel="stylesheet" href="./css/site.css" />
    <link rel="stylesheet" href="./css/print.css" media="print" />
  </head>
  <body class="composite">
    <div id="banner">
      <div class="clear">
        <hr/>
      </div>
    </div>
    <div id="breadcrumbs">
      <div class="xleft">
        <span id="publishDate">Last Published: 2025-07-22</span>
           | <span id="projectVersion">Version: 0.0.1-SNAPSHOT</span>
      </div>
      <div class="xright">      </div>
      <div class="clear">
        <hr/>
      </div>
    </div>
    <div id="leftColumn">
      <div id="navcolumn">
      <a href="http://maven.apache.org/" title="Built by Maven" class="poweredBy">
        <img class="poweredBy" alt="Built by Maven" src="./images/logos/maven-feather.png" />
      </a>
      </div>
    </div>
    <div id="bodyColumn">
      <div id="contentBox">
<script type="text/javascript">
function toggleDisplay(elementId) {
 var elm = document.getElementById(elementId + '-error');
 if (elm == null) {
  elm = document.getElementById(elementId + '-failure');
 }
 if (elm && typeof elm.style != "undefined") {
  if (elm.style.display == "none") {
   elm.style.display = "";
   document.getElementById(elementId + '-off').style.display = "none";
   document.getElementById(elementId + '-on').style.display = "inline";
  } else if (elm.style.display == "") {   elm.style.display = "none";
   document.getElementById(elementId + '-off').style.display = "inline";
   document.getElementById(elementId + '-on').style.display = "none";
  }
 }
 }</script><section>
<h2><a name="Surefire_Report"></a>Surefire Report</h2></section><section><a id="Summary"></a>
<h2><a name="Summary"></a>Summary</h2>
<p>[<a href="#Summary">Summary</a>] [<a href="#Package_List">Package List</a>] [<a href="#Test_Cases">Test Cases</a>]</p><br />
<table border="0" class="bodyTable">
<tr class="a">
<th>Tests</th>
<th>Errors</th>
<th>Failures</th>
<th>Skipped</th>
<th>Success Rate</th>
<th>Time</th></tr>
<tr class="b">
<td align="left">9</td>
<td>0</td>
<td>0</td>
<td>0</td>
<td>100%</td>
<td>3.641 s</td></tr></table><br />
<p>Note: failures are anticipated and checked for with assertions while errors are unanticipated.</p><br /></section><section><a id="Package_List"></a>
<h2><a name="Package_List"></a>Package List</h2>
<p>[<a href="#Summary">Summary</a>] [<a href="#Package_List">Package List</a>] [<a href="#Test_Cases">Test Cases</a>]</p><br />
<table border="0" class="bodyTable">
<tr class="a">
<th>Package</th>
<th>Tests</th>
<th>Errors</th>
<th>Failures</th>
<th>Skipped</th>
<th>Success Rate</th>
<th>Time</th></tr>
<tr class="b">
<td align="left"><a href="#com.ascentbusiness.dms_svc.service">com.ascentbusiness.dms_svc.service</a></td>
<td>9</td>
<td>0</td>
<td>0</td>
<td>0</td>
<td>100%</td>
<td>3.641 s</td></tr></table><br />
<p>Note: package statistics are not computed recursively, they only sum up all of its testsuites numbers.</p><section><a id="com.ascentbusiness.dms_svc.service"></a>
<h3><a name="com.ascentbusiness.dms_svc.service"></a>com.ascentbusiness.dms_svc.service</h3>
<table border="0" class="bodyTable">
<tr class="a">
<th>-</th>
<th>Class</th>
<th>Tests</th>
<th>Errors</th>
<th>Failures</th>
<th>Skipped</th>
<th>Success Rate</th>
<th>Time</th></tr>
<tr class="b">
<td align="left"><a href="#com.ascentbusiness.dms_svc.service.UrlDownloadServiceTest"><img src="images/icon_success_sml.gif" alt="" /></a></td>
<td><a href="#com.ascentbusiness.dms_svc.service.UrlDownloadServiceTest">UrlDownloadServiceTest</a></td>
<td>9</td>
<td>0</td>
<td>0</td>
<td>0</td>
<td>100%</td>
<td>3.641 s</td></tr></table></section><br /></section><section><a id="Test_Cases"></a>
<h2><a name="Test_Cases"></a>Test Cases</h2>
<p>[<a href="#Summary">Summary</a>] [<a href="#Package_List">Package List</a>] [<a href="#Test_Cases">Test Cases</a>]</p><section><a id="com.ascentbusiness.dms_svc.service.UrlDownloadServiceTest"></a>
<h3><a name="UrlDownloadServiceTest"></a>UrlDownloadServiceTest</h3>
<table border="0" class="bodyTable">
<tr class="a">
<td align="left"><img src="images/icon_success_sml.gif" alt="" /></td>
<td><a id="TC_com.ascentbusiness.dms_svc.service.UrlDownloadServiceTest.testDownloadFromPath_FileTooLarge"></a>testDownloadFromPath_FileTooLarge</td>
<td>3.434 s</td></tr>
<tr class="b">
<td align="left"><img src="images/icon_success_sml.gif" alt="" /></td>
<td><a id="TC_com.ascentbusiness.dms_svc.service.UrlDownloadServiceTest.testValidateUrl_PortBlocked"></a>testValidateUrl_PortBlocked</td>
<td>0.035 s</td></tr>
<tr class="a">
<td align="left"><img src="images/icon_success_sml.gif" alt="" /></td>
<td><a id="TC_com.ascentbusiness.dms_svc.service.UrlDownloadServiceTest.testValidateUrl_DomainNotAllowed"></a>testValidateUrl_DomainNotAllowed</td>
<td>0.011 s</td></tr>
<tr class="b">
<td align="left"><img src="images/icon_success_sml.gif" alt="" /></td>
<td><a id="TC_com.ascentbusiness.dms_svc.service.UrlDownloadServiceTest.testValidateUrl_UrlUploadsDisabled"></a>testValidateUrl_UrlUploadsDisabled</td>
<td>0.011 s</td></tr>
<tr class="a">
<td align="left"><img src="images/icon_success_sml.gif" alt="" /></td>
<td><a id="TC_com.ascentbusiness.dms_svc.service.UrlDownloadServiceTest.testExtractFilenameFromUrl"></a>testExtractFilenameFromUrl</td>
<td>0.027 s</td></tr>
<tr class="b">
<td align="left"><img src="images/icon_success_sml.gif" alt="" /></td>
<td><a id="TC_com.ascentbusiness.dms_svc.service.UrlDownloadServiceTest.testDownloadFromPath_FileNotFound"></a>testDownloadFromPath_FileNotFound</td>
<td>0.013 s</td></tr>
<tr class="a">
<td align="left"><img src="images/icon_success_sml.gif" alt="" /></td>
<td><a id="TC_com.ascentbusiness.dms_svc.service.UrlDownloadServiceTest.testValidateUrl_ValidHttpUrl"></a>testValidateUrl_ValidHttpUrl</td>
<td>0.012 s</td></tr>
<tr class="b">
<td align="left"><img src="images/icon_success_sml.gif" alt="" /></td>
<td><a id="TC_com.ascentbusiness.dms_svc.service.UrlDownloadServiceTest.testValidateUrl_InvalidProtocol"></a>testValidateUrl_InvalidProtocol</td>
<td>0.012 s</td></tr>
<tr class="a">
<td align="left"><img src="images/icon_success_sml.gif" alt="" /></td>
<td><a id="TC_com.ascentbusiness.dms_svc.service.UrlDownloadServiceTest.testDownloadFromPath_ValidFile"></a>testDownloadFromPath_ValidFile</td>
<td>0.018 s</td></tr></table></section><br /></section>
      </div>
    </div>
    <div class="clear">
      <hr/>
    </div>
    <div id="footer">
      <div class="xright">
        Copyright &#169;      2025..      </div>
      <div class="clear">
        <hr/>
      </div>
    </div>
  </body>
</html>
