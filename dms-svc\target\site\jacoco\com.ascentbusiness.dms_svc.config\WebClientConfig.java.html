<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>WebClientConfig.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.config</a> &gt; <span class="el_source">WebClientConfig.java</span></div><h1>WebClientConfig.java</h1><pre class="source lang-java linenums">package com.ascentbusiness.dms_svc.config;

import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

import java.time.Duration;

/**
 * Configuration for HTTP clients including RestTemplate
 */
@Configuration
<span class="nc" id="L14">public class WebClientConfig {</span>

    /**
     * Configure RestTemplate with appropriate timeouts and settings
     */
    @Bean
    public RestTemplate restTemplate(RestTemplateBuilder builder) {
<span class="nc" id="L21">        return builder</span>
<span class="nc" id="L22">                .connectTimeout(Duration.ofSeconds(30))</span>
<span class="nc" id="L23">                .readTimeout(Duration.ofSeconds(60))</span>
<span class="nc" id="L24">                .build();</span>
    }

    /**
     * RestTemplateBuilder bean for custom configurations
     */
    @Bean
    public RestTemplateBuilder restTemplateBuilder() {
<span class="nc" id="L32">        return new RestTemplateBuilder();</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>