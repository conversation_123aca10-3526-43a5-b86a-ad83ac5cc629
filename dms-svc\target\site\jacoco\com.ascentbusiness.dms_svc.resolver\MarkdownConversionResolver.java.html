<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>MarkdownConversionResolver.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.resolver</a> &gt; <span class="el_source">MarkdownConversionResolver.java</span></div><h1>MarkdownConversionResolver.java</h1><pre class="source lang-java linenums">package com.ascentbusiness.dms_svc.resolver;

import com.ascentbusiness.dms_svc.dto.MarkdownConversionResult;
import com.ascentbusiness.dms_svc.enums.AuditAction;
import com.ascentbusiness.dms_svc.enums.VirusScannerType;
import com.ascentbusiness.dms_svc.exception.DmsBusinessException;
import com.ascentbusiness.dms_svc.exception.MarkdownConversionException;
import com.ascentbusiness.dms_svc.security.UserContext;
import com.ascentbusiness.dms_svc.service.AuditService;
import com.ascentbusiness.dms_svc.service.MarkdownToWordConversionService;
import com.ascentbusiness.dms_svc.util.CorrelationIdUtil;
import com.ascentbusiness.dms_svc.util.SecurityValidationUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.graphql.data.method.annotation.Argument;
import org.springframework.graphql.data.method.annotation.MutationMapping;
import org.springframework.stereotype.Controller;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

/**
 * GraphQL resolver for Markdown to Word conversion operations.
 * 
 * This resolver provides GraphQL mutations for converting Markdown files to Word documents
 * with comprehensive virus scanning, audit logging, and error handling.
 */
@Controller
<span class="nc" id="L30">public class MarkdownConversionResolver {</span>

<span class="nc" id="L32">    private static final Logger logger = LoggerFactory.getLogger(MarkdownConversionResolver.class);</span>

    @Autowired
    private MarkdownToWordConversionService markdownConversionService;

    @Autowired
    private AuditService auditService;

    @Autowired
    private UserContext userContext;

    /**
     * Convert Markdown file to Word document from multipart file upload.
     * 
     * @param input the conversion input containing file and scanner type
     * @return conversion result with download path
     */
    @MutationMapping
    public MarkdownConversionResult convertMarkdownToWordMultipart(@Argument Map&lt;String, Object&gt; input) {
<span class="nc" id="L51">        String correlationId = CorrelationIdUtil.getCurrentCorrelationId();</span>
<span class="nc" id="L52">        logger.info(&quot;GraphQL mutation: convertMarkdownToWordMultipart() [{}]&quot;, correlationId);</span>

        try {
            // Extract parameters from input
<span class="nc" id="L56">            MultipartFile file = (MultipartFile) input.get(&quot;file&quot;);</span>
<span class="nc" id="L57">            String scannerTypeStr = (String) input.get(&quot;scannerType&quot;);</span>

            // Get user ID from context
<span class="nc" id="L60">            String userId = userContext.getUserId();</span>


            // Validate file first
<span class="nc bnc" id="L64" title="All 4 branches missed.">            if (file == null || file.isEmpty()) {</span>
<span class="nc" id="L65">                throw new DmsBusinessException(&quot;File is required for Markdown to Word conversion&quot;, &quot;FILE_REQUIRED&quot;, Map.of());</span>
            }

            // Parse scanner type
<span class="nc" id="L69">            VirusScannerType scannerType = null;</span>
<span class="nc bnc" id="L70" title="All 4 branches missed.">            if (scannerTypeStr != null &amp;&amp; !scannerTypeStr.trim().isEmpty()) {</span>
                try {
<span class="nc" id="L72">                    scannerType = VirusScannerType.valueOf(scannerTypeStr.toUpperCase());</span>
<span class="nc" id="L73">                } catch (IllegalArgumentException e) {</span>
<span class="nc" id="L74">                    logger.warn(&quot;Invalid scanner type provided: {} [{}]&quot;, scannerTypeStr, correlationId);</span>
<span class="nc" id="L75">                    throw new DmsBusinessException(&quot;Invalid scanner type: &quot; + scannerTypeStr, &quot;INVALID_SCANNER_TYPE&quot;, Map.of(&quot;scannerType&quot;, scannerTypeStr));</span>
<span class="nc" id="L76">                }</span>
            }

<span class="nc" id="L79">            logger.info(&quot;Converting Markdown document to Word via multipart upload: {} (scanner: {}) [{}]&quot;, </span>
<span class="nc" id="L80">                       SecurityValidationUtil.sanitizeForLogging(file.getOriginalFilename()), scannerType, correlationId);</span>

            // Perform conversion
<span class="nc" id="L83">            MarkdownConversionResult result = markdownConversionService.convertMarkdownToWordFromMultipart(file, userId, scannerType);</span>

            // Log successful conversion
<span class="nc" id="L86">            auditService.logAudit(AuditAction.CONVERSION_FILE_DOWNLOADED, null, userId,</span>
<span class="nc" id="L87">                    String.format(&quot;Markdown to Word conversion file ready for download: %s -&gt; %s (session: %s, method: %s)&quot;, </span>
<span class="nc" id="L88">                                SecurityValidationUtil.sanitizeForLogging(result.getOriginalFileName()),</span>
<span class="nc" id="L89">                                SecurityValidationUtil.sanitizeForLogging(result.getConvertedFileName()),</span>
<span class="nc" id="L90">                                result.getSessionId(),</span>
<span class="nc" id="L91">                                result.getConversionMethod()));</span>

<span class="nc" id="L93">            logger.info(&quot;Markdown to Word conversion completed successfully via multipart upload: {} -&gt; {} (method: {}) [{}]&quot;, </span>
<span class="nc" id="L94">                       SecurityValidationUtil.sanitizeForLogging(file.getOriginalFilename()),</span>
<span class="nc" id="L95">                       SecurityValidationUtil.sanitizeForLogging(result.getConvertedFileName()),</span>
<span class="nc" id="L96">                       result.getConversionMethod(),</span>
                       correlationId);

<span class="nc" id="L99">            return result;</span>

<span class="nc" id="L101">        } catch (DmsBusinessException e) {</span>
            // Re-throw DmsBusinessException as-is to preserve error codes and messages
<span class="nc" id="L103">            throw e;</span>
<span class="nc" id="L104">        } catch (MarkdownConversionException e) {</span>
<span class="nc" id="L105">            logger.error(&quot;Markdown to Word conversion failed via multipart upload [{}]&quot;, correlationId, e);</span>
<span class="nc" id="L106">            throw e;</span>
<span class="nc" id="L107">        } catch (Exception e) {</span>
<span class="nc" id="L108">            logger.error(&quot;Unexpected error during Markdown to Word conversion via multipart upload [{}]&quot;, correlationId, e);</span>
<span class="nc" id="L109">            throw new DmsBusinessException(&quot;Markdown to Word conversion failed: &quot; + e.getMessage(), &quot;MARKDOWN_CONVERSION_FAILED&quot;, Map.of(&quot;error&quot;, e.getMessage()), e);</span>
        }
    }

    /**
     * Convert Markdown file to Word document from file path.
     * 
     * @param input the conversion input containing file path and scanner type
     * @return conversion result with download path
     */
    @MutationMapping
    public MarkdownConversionResult convertMarkdownToWordFromPath(@Argument Map&lt;String, Object&gt; input) {
<span class="nc" id="L121">        String correlationId = CorrelationIdUtil.getCurrentCorrelationId();</span>
<span class="nc" id="L122">        logger.info(&quot;GraphQL mutation: convertMarkdownToWordFromPath() [{}]&quot;, correlationId);</span>

        try {
            // Extract parameters from input
<span class="nc" id="L126">            String filePath = (String) input.get(&quot;filePath&quot;);</span>
<span class="nc" id="L127">            String scannerTypeStr = (String) input.get(&quot;scannerType&quot;);</span>

            // Get user ID from context
<span class="nc" id="L130">            String userId = userContext.getUserId();</span>

            // Validate file path first
<span class="nc bnc" id="L133" title="All 4 branches missed.">            if (filePath == null || filePath.trim().isEmpty()) {</span>
<span class="nc" id="L134">                throw new DmsBusinessException(&quot;File path is required for Markdown to Word conversion&quot;, &quot;FILE_PATH_REQUIRED&quot;, Map.of());</span>
            }

            // Parse scanner type
<span class="nc" id="L138">            VirusScannerType scannerType = null;</span>
<span class="nc bnc" id="L139" title="All 4 branches missed.">            if (scannerTypeStr != null &amp;&amp; !scannerTypeStr.trim().isEmpty()) {</span>
                try {
<span class="nc" id="L141">                    scannerType = VirusScannerType.valueOf(scannerTypeStr.toUpperCase());</span>
<span class="nc" id="L142">                } catch (IllegalArgumentException e) {</span>
<span class="nc" id="L143">                    logger.warn(&quot;Invalid scanner type provided: {} [{}]&quot;, scannerTypeStr, correlationId);</span>
<span class="nc" id="L144">                    throw new DmsBusinessException(&quot;Invalid scanner type: &quot; + scannerTypeStr, &quot;INVALID_SCANNER_TYPE&quot;, Map.of(&quot;scannerType&quot;, scannerTypeStr));</span>
<span class="nc" id="L145">                }</span>
            }

<span class="nc" id="L148">            logger.info(&quot;Converting Markdown document to Word via file path: {} (scanner: {}) [{}]&quot;, </span>
<span class="nc" id="L149">                       SecurityValidationUtil.sanitizeForLogging(filePath), scannerType, correlationId);</span>

            // Perform conversion
<span class="nc" id="L152">            MarkdownConversionResult result = markdownConversionService.convertMarkdownToWordFromPath(filePath, userId, scannerType);</span>

            // Log successful conversion
<span class="nc" id="L155">            auditService.logAudit(AuditAction.CONVERSION_FILE_DOWNLOADED, null, userId,</span>
<span class="nc" id="L156">                    String.format(&quot;Markdown to Word conversion file ready for download: %s -&gt; %s (session: %s, method: %s)&quot;, </span>
<span class="nc" id="L157">                                SecurityValidationUtil.sanitizeForLogging(result.getOriginalFileName()),</span>
<span class="nc" id="L158">                                SecurityValidationUtil.sanitizeForLogging(result.getConvertedFileName()),</span>
<span class="nc" id="L159">                                result.getSessionId(),</span>
<span class="nc" id="L160">                                result.getConversionMethod()));</span>

<span class="nc" id="L162">            logger.info(&quot;Markdown to Word conversion completed successfully via file path: {} -&gt; {} (method: {}) [{}]&quot;, </span>
<span class="nc" id="L163">                       SecurityValidationUtil.sanitizeForLogging(filePath),</span>
<span class="nc" id="L164">                       SecurityValidationUtil.sanitizeForLogging(result.getConvertedFileName()),</span>
<span class="nc" id="L165">                       result.getConversionMethod(),</span>
                       correlationId);

<span class="nc" id="L168">            return result;</span>

<span class="nc" id="L170">        } catch (DmsBusinessException e) {</span>
            // Re-throw DmsBusinessException as-is to preserve error codes and messages
<span class="nc" id="L172">            throw e;</span>
<span class="nc" id="L173">        } catch (MarkdownConversionException e) {</span>
<span class="nc" id="L174">            logger.error(&quot;Markdown to Word conversion failed via file path [{}]&quot;, correlationId, e);</span>
<span class="nc" id="L175">            throw e;</span>
<span class="nc" id="L176">        } catch (Exception e) {</span>
<span class="nc" id="L177">            logger.error(&quot;Unexpected error during Markdown to Word conversion via file path [{}]&quot;, correlationId, e);</span>
<span class="nc" id="L178">            throw new DmsBusinessException(&quot;Markdown to Word conversion failed: &quot; + e.getMessage(), &quot;MARKDOWN_CONVERSION_ERROR&quot;, Map.of(&quot;correlationId&quot;, correlationId), e);</span>
        }
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>