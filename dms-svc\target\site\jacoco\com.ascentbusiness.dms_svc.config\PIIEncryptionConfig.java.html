<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>PIIEncryptionConfig.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.config</a> &gt; <span class="el_source">PIIEncryptionConfig.java</span></div><h1>PIIEncryptionConfig.java</h1><pre class="source lang-java linenums">package com.ascentbusiness.dms_svc.config;

import com.ascentbusiness.dms_svc.service.PIIEncryptionService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Scheduled;

/**
 * Configuration for PII field-level encryption
 */
@Configuration
<span class="nc" id="L16">public class PIIEncryptionConfig {</span>
    
<span class="nc" id="L18">    private static final Logger logger = LoggerFactory.getLogger(PIIEncryptionConfig.class);</span>
    
    @Autowired
    private PIIEncryptionService piiEncryptionService;
    
    /**
     * Initialize PII encryption service when application is ready
     */
    @EventListener(ApplicationReadyEvent.class)
    public void initializePIIEncryption() {
        try {
<span class="nc" id="L29">            piiEncryptionService.initialize();</span>
<span class="nc" id="L30">            logger.info(&quot;PII encryption service initialized successfully&quot;);</span>
<span class="nc" id="L31">        } catch (Exception e) {</span>
<span class="nc" id="L32">            logger.error(&quot;Failed to initialize PII encryption service&quot;, e);</span>
<span class="nc" id="L33">        }</span>
<span class="nc" id="L34">    }</span>
    
    /**
     * Scheduled task to clean up old PII encryption keys
     * Runs weekly on Sunday at 3 AM
     */
    @Scheduled(cron = &quot;0 0 3 * * SUN&quot;)
    public void cleanupOldPIIEncryptionKeys() {
        try {
            // Keep keys for 90 days for decryption purposes (longer than audit keys)
<span class="nc" id="L44">            long ninetyDaysInMillis = 90L * 24L * 60L * 60L * 1000L;</span>
            // Note: PIIEncryptionService doesn't have clearOldKeys method yet
            // This would need to be implemented similar to AuditEncryptionService
            
<span class="nc" id="L48">            logger.info(&quot;Completed cleanup of old PII encryption keys&quot;);</span>
<span class="nc" id="L49">        } catch (Exception e) {</span>
<span class="nc" id="L50">            logger.error(&quot;Failed to cleanup old PII encryption keys&quot;, e);</span>
<span class="nc" id="L51">        }</span>
<span class="nc" id="L52">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>