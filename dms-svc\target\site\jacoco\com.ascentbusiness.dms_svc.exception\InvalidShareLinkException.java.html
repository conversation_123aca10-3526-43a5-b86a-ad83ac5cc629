<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>InvalidShareLinkException.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.exception</a> &gt; <span class="el_source">InvalidShareLinkException.java</span></div><h1>InvalidShareLinkException.java</h1><pre class="source lang-java linenums">package com.ascentbusiness.dms_svc.exception;

import java.util.Map;

/**
 * Exception thrown when a share link is invalid, expired, or cannot be used.
 * Extends DmsBusinessException with specific error handling for share link validation failures.
 */
public class InvalidShareLinkException extends DmsBusinessException {

    public InvalidShareLinkException(String message) {
<span class="nc" id="L12">        super(message, &quot;INVALID_SHARE_LINK&quot;);</span>
<span class="nc" id="L13">    }</span>

    public InvalidShareLinkException(String message, Throwable cause) {
<span class="nc" id="L16">        super(message, &quot;INVALID_SHARE_LINK&quot;, Map.of(), cause);</span>
<span class="nc" id="L17">    }</span>

    public InvalidShareLinkException(String linkId, String reason) {
<span class="nc" id="L20">        super(String.format(&quot;Share link '%s' is invalid: %s&quot;, linkId, reason),</span>
              &quot;INVALID_SHARE_LINK&quot;,
<span class="nc" id="L22">              Map.of(&quot;linkId&quot;, linkId, &quot;reason&quot;, reason));</span>
<span class="nc" id="L23">    }</span>

    public static InvalidShareLinkException expired(String linkId) {
<span class="nc" id="L26">        return new InvalidShareLinkException(linkId, &quot;Link has expired&quot;);</span>
    }

    public static InvalidShareLinkException inactive(String linkId) {
<span class="nc" id="L30">        return new InvalidShareLinkException(linkId, &quot;Link is no longer active&quot;);</span>
    }

    public static InvalidShareLinkException maxUsesReached(String linkId) {
<span class="nc" id="L34">        return new InvalidShareLinkException(linkId, &quot;Link has reached its maximum number of uses&quot;);</span>
    }

    public static InvalidShareLinkException passwordRequired(String linkId) {
<span class="nc" id="L38">        return new InvalidShareLinkException(linkId, &quot;Password is required to access this link&quot;);</span>
    }

    public static InvalidShareLinkException invalidPassword(String linkId) {
<span class="nc" id="L42">        return new InvalidShareLinkException(linkId, &quot;Invalid password provided&quot;);</span>
    }

    public static InvalidShareLinkException notFound(String linkId) {
<span class="nc" id="L46">        return new InvalidShareLinkException(linkId, &quot;Share link not found&quot;);</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>