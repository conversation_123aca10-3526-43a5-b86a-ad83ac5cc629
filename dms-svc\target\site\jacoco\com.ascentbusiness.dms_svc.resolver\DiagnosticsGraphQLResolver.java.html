<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>DiagnosticsGraphQLResolver.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.resolver</a> &gt; <span class="el_source">DiagnosticsGraphQLResolver.java</span></div><h1>DiagnosticsGraphQLResolver.java</h1><pre class="source lang-java linenums">package com.ascentbusiness.dms_svc.resolver;

import com.ascentbusiness.dms_svc.dto.*;
import com.ascentbusiness.dms_svc.enums.*;
import com.ascentbusiness.dms_svc.service.DynamicStorageProviderFactory;
import com.ascentbusiness.dms_svc.util.SharePointTestUtility;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.actuate.health.Health;
import org.springframework.boot.actuate.health.HealthEndpoint;
import org.springframework.boot.actuate.health.HealthIndicator;
import org.springframework.boot.info.BuildProperties;
import org.springframework.context.ApplicationContext;
import org.springframework.graphql.data.method.annotation.Argument;
import org.springframework.graphql.data.method.annotation.MutationMapping;
import org.springframework.graphql.data.method.annotation.QueryMapping;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;

import javax.sql.DataSource;
import java.lang.management.ManagementFactory;
import java.sql.Connection;
import java.lang.management.MemoryMXBean;
import java.time.OffsetDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * GraphQL resolver for system diagnostics and health monitoring operations.
 * This resolver implements the diagnostics-schema.graphqls operations to replace REST endpoints.
 * 
 * Provides comprehensive system diagnostics including:
 * - System health monitoring
 * - Component health checks
 * - Connection testing (SharePoint, Database, Elasticsearch)
 * - Performance monitoring
 * - Storage provider diagnostics
 */
@Controller
<span class="nc" id="L43">@Slf4j</span>
<span class="nc" id="L44">public class DiagnosticsGraphQLResolver {</span>

    @Autowired
    private ApplicationContext applicationContext;
    
    @Autowired(required = false)
    private SharePointTestUtility sharePointTestUtility;
    
    @Autowired(required = false)
    private DataSource dataSource;
    
    @Autowired(required = false)
    private BuildProperties buildProperties;

    @Autowired(required = false)
    private DynamicStorageProviderFactory storageProviderFactory;

    @Autowired(required = false)
    private HealthEndpoint healthEndpoint;
    
    // In-memory storage for diagnostic history (in production, use database)
<span class="nc" id="L65">    private final Map&lt;String, DiagnosticResult&gt; diagnosticHistory = new ConcurrentHashMap&lt;&gt;();</span>
<span class="nc" id="L66">    private final Map&lt;String, SystemPerformance&gt; performanceHistory = new ConcurrentHashMap&lt;&gt;();</span>

    // ===== SYSTEM HEALTH QUERIES =====



    // ===== CONNECTION TEST QUERIES =====

    /**
     * Test SharePoint connection.
     * Implements testSharePointConnection query from diagnostics-schema.graphqls.
     */
    @QueryMapping
    public ConnectionTestResult testSharePointConnection() {
<span class="nc" id="L80">        log.info(&quot;GraphQL testSharePointConnection called&quot;);</span>
        
<span class="nc bnc" id="L82" title="All 2 branches missed.">        if (sharePointTestUtility == null) {</span>
<span class="nc" id="L83">            return ConnectionTestResult.builder()</span>
<span class="nc" id="L84">                    .testId(UUID.randomUUID().toString())</span>
<span class="nc" id="L85">                    .testType(ConnectionTestType.SHAREPOINT)</span>
<span class="nc" id="L86">                    .target(&quot;SharePoint&quot;)</span>
<span class="nc" id="L87">                    .status(TestStatus.SKIPPED)</span>
<span class="nc" id="L88">                    .success(false)</span>
<span class="nc" id="L89">                    .responseTime(0L)</span>
<span class="nc" id="L90">                    .message(&quot;SharePoint test utility not available&quot;)</span>
<span class="nc" id="L91">                    .timestamp(OffsetDateTime.now())</span>
<span class="nc" id="L92">                    .retryCount(0)</span>
<span class="nc" id="L93">                    .build();</span>
        }
        
<span class="nc" id="L96">        long startTime = System.currentTimeMillis();</span>
        try {
<span class="nc" id="L98">            sharePointTestUtility.testSharePointConnection();</span>
<span class="nc" id="L99">            long responseTime = System.currentTimeMillis() - startTime;</span>
            
<span class="nc" id="L101">            return ConnectionTestResult.builder()</span>
<span class="nc" id="L102">                    .testId(UUID.randomUUID().toString())</span>
<span class="nc" id="L103">                    .testType(ConnectionTestType.SHAREPOINT)</span>
<span class="nc" id="L104">                    .target(&quot;SharePoint&quot;)</span>
<span class="nc" id="L105">                    .status(TestStatus.PASSED)</span>
<span class="nc" id="L106">                    .success(true)</span>
<span class="nc" id="L107">                    .responseTime(responseTime)</span>
<span class="nc" id="L108">                    .message(&quot;SharePoint connection test passed&quot;)</span>
<span class="nc" id="L109">                    .timestamp(OffsetDateTime.now())</span>
<span class="nc" id="L110">                    .retryCount(0)</span>
<span class="nc" id="L111">                    .build();</span>
                    
<span class="nc" id="L113">        } catch (Exception e) {</span>
<span class="nc" id="L114">            long responseTime = System.currentTimeMillis() - startTime;</span>
<span class="nc" id="L115">            log.error(&quot;SharePoint connection test failed&quot;, e);</span>
            
<span class="nc" id="L117">            return ConnectionTestResult.builder()</span>
<span class="nc" id="L118">                    .testId(UUID.randomUUID().toString())</span>
<span class="nc" id="L119">                    .testType(ConnectionTestType.SHAREPOINT)</span>
<span class="nc" id="L120">                    .target(&quot;SharePoint&quot;)</span>
<span class="nc" id="L121">                    .status(TestStatus.FAILED)</span>
<span class="nc" id="L122">                    .success(false)</span>
<span class="nc" id="L123">                    .responseTime(responseTime)</span>
<span class="nc" id="L124">                    .message(&quot;SharePoint connection test failed: &quot; + e.getMessage())</span>
<span class="nc" id="L125">                    .timestamp(OffsetDateTime.now())</span>
<span class="nc" id="L126">                    .retryCount(0)</span>
<span class="nc" id="L127">                    .build();</span>
        }
    }

    /**
     * Test database connection.
     * Implements testDatabaseConnection query from diagnostics-schema.graphqls.
     */
    @QueryMapping
    public ConnectionTestResult testDatabaseConnection() {
<span class="nc" id="L137">        log.info(&quot;GraphQL testDatabaseConnection called&quot;);</span>
        
<span class="nc bnc" id="L139" title="All 2 branches missed.">        if (dataSource == null) {</span>
<span class="nc" id="L140">            return createFailedConnectionTest(ConnectionTestType.DATABASE, &quot;Database&quot;, &quot;DataSource not available&quot;);</span>
        }
        
<span class="nc" id="L143">        long startTime = System.currentTimeMillis();</span>
<span class="nc" id="L144">        try (Connection connection = dataSource.getConnection()) {</span>
<span class="nc" id="L145">            boolean isValid = connection.isValid(5); // 5 second timeout</span>
<span class="nc" id="L146">            long responseTime = System.currentTimeMillis() - startTime;</span>
            
<span class="nc bnc" id="L148" title="All 2 branches missed.">            if (isValid) {</span>
<span class="nc" id="L149">                return ConnectionTestResult.builder()</span>
<span class="nc" id="L150">                        .testId(UUID.randomUUID().toString())</span>
<span class="nc" id="L151">                        .testType(ConnectionTestType.DATABASE)</span>
<span class="nc" id="L152">                        .target(&quot;Database&quot;)</span>
<span class="nc" id="L153">                        .status(TestStatus.PASSED)</span>
<span class="nc" id="L154">                        .success(true)</span>
<span class="nc" id="L155">                        .responseTime(responseTime)</span>
<span class="nc" id="L156">                        .message(&quot;Database connection test passed&quot;)</span>
<span class="nc" id="L157">                        .timestamp(OffsetDateTime.now())</span>
<span class="nc" id="L158">                        .retryCount(0)</span>
<span class="nc" id="L159">                        .build();</span>
            } else {
<span class="nc" id="L161">                return createFailedConnectionTest(ConnectionTestType.DATABASE, &quot;Database&quot;, &quot;Connection validation failed&quot;);</span>
            }
            
<span class="nc bnc" id="L164" title="All 2 branches missed.">        } catch (Exception e) {</span>
<span class="nc" id="L165">            long responseTime = System.currentTimeMillis() - startTime;</span>
<span class="nc" id="L166">            log.error(&quot;Database connection test failed&quot;, e);</span>
            
<span class="nc" id="L168">            return ConnectionTestResult.builder()</span>
<span class="nc" id="L169">                    .testId(UUID.randomUUID().toString())</span>
<span class="nc" id="L170">                    .testType(ConnectionTestType.DATABASE)</span>
<span class="nc" id="L171">                    .target(&quot;Database&quot;)</span>
<span class="nc" id="L172">                    .status(TestStatus.FAILED)</span>
<span class="nc" id="L173">                    .success(false)</span>
<span class="nc" id="L174">                    .responseTime(responseTime)</span>
<span class="nc" id="L175">                    .message(&quot;Database connection test failed: &quot; + e.getMessage())</span>
<span class="nc" id="L176">                    .timestamp(OffsetDateTime.now())</span>
<span class="nc" id="L177">                    .retryCount(0)</span>
<span class="nc" id="L178">                    .build();</span>
        }
    }

    /**
     * Test Elasticsearch connection.
     * Implements testElasticsearchConnection query from diagnostics-schema.graphqls.
     */
    @QueryMapping
    public ConnectionTestResult testElasticsearchConnection() {
<span class="nc" id="L188">        log.info(&quot;GraphQL testElasticsearchConnection called&quot;);</span>

        // Try to get Elasticsearch health indicator
        try {
<span class="nc" id="L192">            HealthIndicator esHealthIndicator = applicationContext.getBean(&quot;elasticsearch&quot;, HealthIndicator.class);</span>
<span class="nc" id="L193">            long startTime = System.currentTimeMillis();</span>
<span class="nc" id="L194">            Health health = esHealthIndicator.health();</span>
<span class="nc" id="L195">            long responseTime = System.currentTimeMillis() - startTime;</span>

<span class="nc" id="L197">            boolean isUp = health.getStatus().getCode().equals(&quot;UP&quot;);</span>

<span class="nc" id="L199">            return ConnectionTestResult.builder()</span>
<span class="nc" id="L200">                    .testId(UUID.randomUUID().toString())</span>
<span class="nc" id="L201">                    .testType(ConnectionTestType.ELASTICSEARCH)</span>
<span class="nc" id="L202">                    .target(&quot;Elasticsearch&quot;)</span>
<span class="nc bnc" id="L203" title="All 2 branches missed.">                    .status(isUp ? TestStatus.PASSED : TestStatus.FAILED)</span>
<span class="nc" id="L204">                    .success(isUp)</span>
<span class="nc" id="L205">                    .responseTime(responseTime)</span>
<span class="nc bnc" id="L206" title="All 2 branches missed.">                    .message(isUp ? &quot;Elasticsearch connection test passed&quot; : &quot;Elasticsearch connection test failed&quot;)</span>
<span class="nc" id="L207">                    .timestamp(OffsetDateTime.now())</span>
<span class="nc" id="L208">                    .retryCount(0)</span>
<span class="nc" id="L209">                    .build();</span>

<span class="nc" id="L211">        } catch (Exception e) {</span>
<span class="nc" id="L212">            log.warn(&quot;Elasticsearch health indicator not available or failed&quot;, e);</span>
<span class="nc" id="L213">            return createFailedConnectionTest(ConnectionTestType.ELASTICSEARCH, &quot;Elasticsearch&quot;,</span>
<span class="nc" id="L214">                    &quot;Elasticsearch not available or not configured: &quot; + e.getMessage());</span>
        }
    }

    /**
     * Test storage provider connection.
     * Implements testStorageProviderConnection query from diagnostics-schema.graphqls.
     */
    @QueryMapping
    public ConnectionTestResult testStorageProviderConnection(@Argument StorageProvider provider) {
<span class="nc" id="L224">        log.info(&quot;GraphQL testStorageProviderConnection called with provider: {}&quot;, provider);</span>

<span class="nc" id="L226">        long startTime = System.currentTimeMillis();</span>
        try {
            // Test the storage provider connection based on the provider type
<span class="nc" id="L229">            String target = provider.name() + &quot; Storage&quot;;</span>
<span class="nc" id="L230">            boolean connectionSuccessful = testStorageProviderInternal(provider);</span>
<span class="nc" id="L231">            long responseTime = System.currentTimeMillis() - startTime;</span>

<span class="nc" id="L233">            return ConnectionTestResult.builder()</span>
<span class="nc" id="L234">                    .testId(UUID.randomUUID().toString())</span>
<span class="nc" id="L235">                    .testType(ConnectionTestType.STORAGE)</span>
<span class="nc" id="L236">                    .target(target)</span>
<span class="nc bnc" id="L237" title="All 2 branches missed.">                    .status(connectionSuccessful ? TestStatus.PASSED : TestStatus.FAILED)</span>
<span class="nc" id="L238">                    .success(connectionSuccessful)</span>
<span class="nc" id="L239">                    .responseTime(responseTime)</span>
<span class="nc bnc" id="L240" title="All 2 branches missed.">                    .message(connectionSuccessful ?</span>
<span class="nc" id="L241">                        provider.name() + &quot; storage connection test passed&quot; :</span>
<span class="nc" id="L242">                        provider.name() + &quot; storage connection test failed&quot;)</span>
<span class="nc" id="L243">                    .timestamp(OffsetDateTime.now())</span>
<span class="nc" id="L244">                    .retryCount(0)</span>
<span class="nc" id="L245">                    .build();</span>

<span class="nc" id="L247">        } catch (Exception e) {</span>
<span class="nc" id="L248">            long responseTime = System.currentTimeMillis() - startTime;</span>
<span class="nc" id="L249">            log.error(&quot;Storage provider connection test failed for {}&quot;, provider, e);</span>

<span class="nc" id="L251">            return ConnectionTestResult.builder()</span>
<span class="nc" id="L252">                    .testId(UUID.randomUUID().toString())</span>
<span class="nc" id="L253">                    .testType(ConnectionTestType.STORAGE)</span>
<span class="nc" id="L254">                    .target(provider.name() + &quot; Storage&quot;)</span>
<span class="nc" id="L255">                    .status(TestStatus.FAILED)</span>
<span class="nc" id="L256">                    .success(false)</span>
<span class="nc" id="L257">                    .responseTime(responseTime)</span>
<span class="nc" id="L258">                    .message(&quot;Storage provider connection test failed: &quot; + e.getMessage())</span>
<span class="nc" id="L259">                    .timestamp(OffsetDateTime.now())</span>
<span class="nc" id="L260">                    .retryCount(0)</span>
<span class="nc" id="L261">                    .build();</span>
        }
    }

    /**
     * Test generic connection with input parameters.
     * Implements testConnection query from diagnostics-schema.graphqls.
     */
    @QueryMapping
    public ConnectionTestResult testConnection(@Argument ConnectionTestInput input) {
<span class="nc" id="L271">        log.info(&quot;GraphQL testConnection called with input: {}&quot;, input);</span>

<span class="nc" id="L273">        long startTime = System.currentTimeMillis();</span>
        try {
            // Perform connection test based on the input type
<span class="nc" id="L276">            boolean connectionSuccessful = performConnectionTest(input);</span>
<span class="nc" id="L277">            long responseTime = System.currentTimeMillis() - startTime;</span>

<span class="nc" id="L279">            return ConnectionTestResult.builder()</span>
<span class="nc" id="L280">                    .testId(UUID.randomUUID().toString())</span>
<span class="nc" id="L281">                    .testType(input.getTestType())</span>
<span class="nc bnc" id="L282" title="All 2 branches missed.">                    .target(input.getTarget() != null ? input.getTarget() : input.getTestType().name())</span>
<span class="nc bnc" id="L283" title="All 2 branches missed.">                    .status(connectionSuccessful ? TestStatus.PASSED : TestStatus.FAILED)</span>
<span class="nc" id="L284">                    .success(connectionSuccessful)</span>
<span class="nc" id="L285">                    .responseTime(responseTime)</span>
<span class="nc bnc" id="L286" title="All 2 branches missed.">                    .message(connectionSuccessful ?</span>
<span class="nc" id="L287">                        &quot;Connection test passed for &quot; + input.getTestType() :</span>
<span class="nc" id="L288">                        &quot;Connection test failed for &quot; + input.getTestType())</span>
<span class="nc" id="L289">                    .details(ConnectionTestDetails.builder()</span>
<span class="nc" id="L290">                            .endpoint(input.getTarget())</span>
<span class="nc" id="L291">                            .method(&quot;GET&quot;)</span>
<span class="nc bnc" id="L292" title="All 2 branches missed.">                            .statusCode(connectionSuccessful ? 200 : 500)</span>
<span class="nc" id="L293">                            .headers(&quot;{}&quot;)</span>
<span class="nc" id="L294">                            .responseSize(1024L)</span>
<span class="nc" id="L295">                            .version(&quot;1.0&quot;)</span>
<span class="nc" id="L296">                            .connectionPool(ConnectionPool.builder()</span>
<span class="nc" id="L297">                                    .active(5)</span>
<span class="nc" id="L298">                                    .idle(10)</span>
<span class="nc" id="L299">                                    .max(15)</span>
<span class="nc" id="L300">                                    .build())</span>
<span class="nc" id="L301">                            .build())</span>
<span class="nc" id="L302">                    .timestamp(OffsetDateTime.now())</span>
<span class="nc bnc" id="L303" title="All 2 branches missed.">                    .retryCount(input.getRetryCount() != null ? input.getRetryCount() : 0)</span>
<span class="nc" id="L304">                    .build();</span>

<span class="nc" id="L306">        } catch (Exception e) {</span>
<span class="nc" id="L307">            long responseTime = System.currentTimeMillis() - startTime;</span>
<span class="nc" id="L308">            log.error(&quot;Connection test failed for {}&quot;, input.getTestType(), e);</span>

<span class="nc" id="L310">            return ConnectionTestResult.builder()</span>
<span class="nc" id="L311">                    .testId(UUID.randomUUID().toString())</span>
<span class="nc" id="L312">                    .testType(input.getTestType())</span>
<span class="nc bnc" id="L313" title="All 2 branches missed.">                    .target(input.getTarget() != null ? input.getTarget() : input.getTestType().name())</span>
<span class="nc" id="L314">                    .status(TestStatus.FAILED)</span>
<span class="nc" id="L315">                    .success(false)</span>
<span class="nc" id="L316">                    .responseTime(responseTime)</span>
<span class="nc" id="L317">                    .message(&quot;Connection test failed: &quot; + e.getMessage())</span>
<span class="nc" id="L318">                    .timestamp(OffsetDateTime.now())</span>
<span class="nc bnc" id="L319" title="All 2 branches missed.">                    .retryCount(input.getRetryCount() != null ? input.getRetryCount() : 0)</span>
<span class="nc" id="L320">                    .build();</span>
        }
    }

    /**
     * Get application metrics.
     * Implements getApplicationMetrics query from diagnostics-schema.graphqls.
     */
    @QueryMapping
    public ApplicationMetrics getApplicationMetrics() {
<span class="nc" id="L330">        log.info(&quot;GraphQL getApplicationMetrics called&quot;);</span>

        try {
            // Get runtime information
<span class="nc" id="L334">            Runtime runtime = Runtime.getRuntime();</span>
<span class="nc" id="L335">            long uptime = ManagementFactory.getRuntimeMXBean().getUptime();</span>

            // Create cache statistics (mock data for now)
<span class="nc" id="L338">            CacheStatistics cacheStats = CacheStatistics.builder()</span>
<span class="nc" id="L339">                    .hitCount(1000L)</span>
<span class="nc" id="L340">                    .missCount(100L)</span>
<span class="nc" id="L341">                    .hitRate(0.9)</span>
<span class="nc" id="L342">                    .evictionCount(10L)</span>
<span class="nc" id="L343">                    .size(500L)</span>
<span class="nc" id="L344">                    .build();</span>

<span class="nc" id="L346">            return ApplicationMetrics.builder()</span>
<span class="nc" id="L347">                    .uptime(uptime)</span>
<span class="nc" id="L348">                    .totalRequests(5000L)</span>
<span class="nc" id="L349">                    .totalErrors(50L)</span>
<span class="nc" id="L350">                    .averageResponseTime(150.0)</span>
<span class="nc" id="L351">                    .peakResponseTime(500.0)</span>
<span class="nc" id="L352">                    .activeUsers(25)</span>
<span class="nc" id="L353">                    .documentsProcessed(1000L)</span>
<span class="nc" id="L354">                    .storageUsed(runtime.totalMemory() - runtime.freeMemory())</span>
<span class="nc" id="L355">                    .cacheStatistics(cacheStats)</span>
<span class="nc" id="L356">                    .build();</span>

<span class="nc" id="L358">        } catch (Exception e) {</span>
<span class="nc" id="L359">            log.error(&quot;Failed to get application metrics&quot;, e);</span>
<span class="nc" id="L360">            throw new RuntimeException(&quot;Failed to get application metrics: &quot; + e.getMessage());</span>
        }
    }

    /**
     * Get health summary.
     * Implements getHealthSummary query from diagnostics-schema.graphqls.
     */
    @QueryMapping
    public HealthSummary getHealthSummary() {
<span class="nc" id="L370">        log.info(&quot;GraphQL getHealthSummary called&quot;);</span>

        try {
            // Get overall health status
<span class="nc" id="L374">            org.springframework.boot.actuate.health.HealthComponent healthComponent = healthEndpoint.health();</span>

            Health overallHealth;
<span class="nc bnc" id="L377" title="All 2 branches missed.">            if (healthComponent instanceof org.springframework.boot.actuate.health.SystemHealth) {</span>
<span class="nc" id="L378">                org.springframework.boot.actuate.health.SystemHealth systemHealth =</span>
                    (org.springframework.boot.actuate.health.SystemHealth) healthComponent;
<span class="nc" id="L380">                overallHealth = Health.up().build(); // Default to UP for system health</span>
<span class="nc bnc" id="L381" title="All 2 branches missed.">            } else if (healthComponent instanceof Health) {</span>
<span class="nc" id="L382">                overallHealth = (Health) healthComponent;</span>
            } else {
<span class="nc" id="L384">                overallHealth = Health.up().build(); // Default fallback</span>
            }

<span class="nc" id="L387">            HealthStatus overallStatus = mapSpringHealthStatus(overallHealth.getStatus());</span>

            // Create component health list
<span class="nc" id="L390">            List&lt;ComponentHealth&gt; components = new ArrayList&lt;&gt;();</span>

            // Try to get components from SystemHealth if available
<span class="nc bnc" id="L393" title="All 2 branches missed.">            if (healthComponent instanceof org.springframework.boot.actuate.health.SystemHealth) {</span>
<span class="nc" id="L394">                org.springframework.boot.actuate.health.SystemHealth systemHealth =</span>
                    (org.springframework.boot.actuate.health.SystemHealth) healthComponent;

                // Create mock components for demonstration
<span class="nc" id="L398">                components.add(createMockComponentHealth(&quot;database&quot;, HealthStatus.UP));</span>
<span class="nc" id="L399">                components.add(createMockComponentHealth(&quot;diskSpace&quot;, HealthStatus.UP));</span>
<span class="nc" id="L400">                components.add(createMockComponentHealth(&quot;ping&quot;, HealthStatus.UP));</span>

<span class="nc bnc" id="L402" title="All 2 branches missed.">            } else if (overallHealth.getDetails() != null) {</span>
<span class="nc bnc" id="L403" title="All 2 branches missed.">                for (Map.Entry&lt;String, Object&gt; entry : overallHealth.getDetails().entrySet()) {</span>
<span class="nc bnc" id="L404" title="All 2 branches missed.">                    if (entry.getValue() instanceof Health) {</span>
<span class="nc" id="L405">                        Health componentHealth = (Health) entry.getValue();</span>
<span class="nc" id="L406">                        components.add(createComponentHealth(entry.getKey(), componentHealth));</span>
                    }
<span class="nc" id="L408">                }</span>
            }

            // Calculate component counts
<span class="nc bnc" id="L412" title="All 2 branches missed.">            int componentsUp = (int) components.stream().filter(c -&gt; c.getStatus() == HealthStatus.UP).count();</span>
<span class="nc bnc" id="L413" title="All 2 branches missed.">            int componentsDown = (int) components.stream().filter(c -&gt; c.getStatus() == HealthStatus.DOWN).count();</span>
<span class="nc bnc" id="L414" title="All 2 branches missed.">            int componentsDegraded = (int) components.stream().filter(c -&gt; c.getStatus() == HealthStatus.DEGRADED).count();</span>
<span class="nc" id="L415">            int totalComponents = components.size();</span>

<span class="nc" id="L417">            return HealthSummary.builder()</span>
<span class="nc" id="L418">                    .overallStatus(overallStatus)</span>
<span class="nc" id="L419">                    .components(components)</span>
<span class="nc" id="L420">                    .lastUpdated(OffsetDateTime.now())</span>
<span class="nc" id="L421">                    .componentsUp(componentsUp)</span>
<span class="nc" id="L422">                    .componentsDown(componentsDown)</span>
<span class="nc" id="L423">                    .componentsDegraded(componentsDegraded)</span>
<span class="nc" id="L424">                    .totalComponents(totalComponents)</span>
<span class="nc" id="L425">                    .criticalIssues(componentsDown)</span>
<span class="nc" id="L426">                    .warnings(componentsDegraded)</span>
<span class="nc" id="L427">                    .systemLoad(25.5) // Mock data</span>
<span class="nc" id="L428">                    .availabilityPercentage(95.0) // Mock data</span>
<span class="nc" id="L429">                    .score(0.95) // Mock data</span>
<span class="nc" id="L430">                    .lastChecked(OffsetDateTime.now())</span>
<span class="nc" id="L431">                    .nextCheckDue(OffsetDateTime.now().plusHours(1))</span>
<span class="nc" id="L432">                    .build();</span>

<span class="nc" id="L434">        } catch (Exception e) {</span>
<span class="nc" id="L435">            log.error(&quot;Failed to get health summary&quot;, e);</span>
<span class="nc" id="L436">            throw new RuntimeException(&quot;Failed to get health summary: &quot; + e.getMessage());</span>
        }
    }

    /**
     * Run diagnostics.
     * Implements runDiagnostics query from diagnostics-schema.graphqls.
     */
    @QueryMapping
    public DiagnosticResult runDiagnostics(@Argument DiagnosticTestInput input) {
<span class="nc" id="L446">        log.info(&quot;GraphQL runDiagnostics called with input: {}&quot;, input);</span>

        try {
<span class="nc" id="L449">            String testId = UUID.randomUUID().toString();</span>
<span class="nc" id="L450">            long startTime = System.currentTimeMillis();</span>

            // Perform diagnostic tests based on input
<span class="nc" id="L453">            List&lt;DiagnosticTest&gt; tests = performDiagnosticTests(input);</span>

<span class="nc" id="L455">            long duration = System.currentTimeMillis() - startTime;</span>
<span class="nc bnc" id="L456" title="All 2 branches missed.">            boolean allPassed = tests.stream().allMatch(test -&gt; test.getStatus() == TestStatus.PASSED);</span>

<span class="nc" id="L458">            OffsetDateTime endTime = OffsetDateTime.now();</span>

            // Convert DiagnosticTest to DiagnosticTestResult
<span class="nc" id="L461">            List&lt;DiagnosticTestResult&gt; results = tests.stream()</span>
<span class="nc" id="L462">                    .map(test -&gt; DiagnosticTestResult.builder()</span>
<span class="nc" id="L463">                            .testName(test.getName())</span>
<span class="nc" id="L464">                            .category(test.getTestType().name())</span>
<span class="nc" id="L465">                            .status(test.getStatus())</span>
<span class="nc bnc" id="L466" title="All 2 branches missed.">                            .success(test.getStatus() == TestStatus.PASSED)</span>
<span class="nc" id="L467">                            .message(test.getMessage())</span>
<span class="nc" id="L468">                            .details(test.getDetails())</span>
<span class="nc" id="L469">                            .duration(test.getDuration())</span>
<span class="nc" id="L470">                            .build())</span>
<span class="nc" id="L471">                    .collect(Collectors.toList());</span>

<span class="nc" id="L473">            return DiagnosticResult.builder()</span>
<span class="nc" id="L474">                    .testId(testId)</span>
<span class="nc" id="L475">                    .testSuite(input.getTestSuite())</span>
<span class="nc bnc" id="L476" title="All 2 branches missed.">                    .status(allPassed ? TestStatus.PASSED : TestStatus.FAILED)</span>
<span class="nc bnc" id="L477" title="All 2 branches missed.">                    .overallResult(allPassed ? TestStatus.PASSED : TestStatus.FAILED)</span>
<span class="nc" id="L478">                    .startTime(OffsetDateTime.now().minusSeconds(duration / 1000))</span>
<span class="nc" id="L479">                    .endTime(endTime)</span>
<span class="nc" id="L480">                    .duration(duration)</span>
<span class="nc" id="L481">                    .testsRun(tests.size())</span>
<span class="nc bnc" id="L482" title="All 2 branches missed.">                    .testsPassed((int) tests.stream().filter(t -&gt; t.getStatus() == TestStatus.PASSED).count())</span>
<span class="nc bnc" id="L483" title="All 2 branches missed.">                    .testsFailed((int) tests.stream().filter(t -&gt; t.getStatus() == TestStatus.FAILED).count())</span>
<span class="nc" id="L484">                    .testsSkipped(0) // No skipped tests in our implementation</span>
<span class="nc" id="L485">                    .results(results) // Add the results field</span>
<span class="nc" id="L486">                    .recommendations(List.of(&quot;Regular system monitoring recommended&quot;, &quot;Review failed tests&quot;))</span>
<span class="nc" id="L487">                    .summary(DiagnosticSummary.builder()</span>
<span class="nc bnc" id="L488" title="All 2 branches missed.">                            .overallHealth(allPassed ? HealthStatus.UP : HealthStatus.DOWN)</span>
<span class="nc bnc" id="L489" title="All 2 branches missed.">                            .criticalIssues(allPassed ? 0 : 1)</span>
<span class="nc" id="L490">                            .warnings(0)</span>
<span class="nc" id="L491">                            .recommendations(List.of(&quot;Regular system monitoring recommended&quot;))</span>
<span class="nc" id="L492">                            .nextCheckRecommended(OffsetDateTime.now().plusHours(24))</span>
<span class="nc" id="L493">                            .build())</span>
<span class="nc" id="L494">                    .build();</span>

<span class="nc" id="L496">        } catch (Exception e) {</span>
<span class="nc" id="L497">            log.error(&quot;Failed to run diagnostics&quot;, e);</span>
<span class="nc" id="L498">            throw new RuntimeException(&quot;Failed to run diagnostics: &quot; + e.getMessage());</span>
        }
    }

    /**
     * Get system performance.
     * Implements getSystemPerformance query from diagnostics-schema.graphqls.
     */
    @QueryMapping
    public SystemPerformance getSystemPerformance() {
<span class="nc" id="L508">        log.info(&quot;GraphQL getSystemPerformance called&quot;);</span>

        try {
<span class="nc" id="L511">            Runtime runtime = Runtime.getRuntime();</span>
<span class="nc" id="L512">            MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();</span>

            // Create memory usage info
<span class="nc" id="L515">            MemoryUsage memoryUsage = MemoryUsage.builder()</span>
<span class="nc" id="L516">                    .used(runtime.totalMemory() - runtime.freeMemory())</span>
<span class="nc" id="L517">                    .total(runtime.totalMemory())</span>
<span class="nc" id="L518">                    .max(runtime.maxMemory())</span>
<span class="nc" id="L519">                    .percentage((double)(runtime.totalMemory() - runtime.freeMemory()) / runtime.totalMemory() * 100)</span>
<span class="nc" id="L520">                    .build();</span>

            // Create CPU usage info (mock data)
<span class="nc" id="L523">            CpuUsage cpuUsage = CpuUsage.builder()</span>
<span class="nc" id="L524">                    .current(25.5)</span>
<span class="nc" id="L525">                    .average(30.0)</span>
<span class="nc" id="L526">                    .peak(85.0)</span>
<span class="nc" id="L527">                    .build();</span>

<span class="nc" id="L529">            return SystemPerformance.builder()</span>
<span class="nc" id="L530">                    .timestamp(OffsetDateTime.now())</span>
<span class="nc" id="L531">                    .cpuUsage(50.5f) // Mock data</span>
<span class="nc" id="L532">                    .memoryUsage(65.2f) // Mock data</span>
<span class="nc" id="L533">                    .diskUsage(45.8f) // Mock data</span>
<span class="nc" id="L534">                    .networkIO(NetworkIO.builder()</span>
<span class="nc" id="L535">                            .bytesIn(1024L * 1024L)</span>
<span class="nc" id="L536">                            .bytesOut(512L * 1024L)</span>
<span class="nc" id="L537">                            .packetsIn(1000L)</span>
<span class="nc" id="L538">                            .packetsOut(800L)</span>
<span class="nc" id="L539">                            .build())</span>
<span class="nc" id="L540">                    .jvmMetrics(JvmMetrics.builder()</span>
<span class="nc" id="L541">                            .heapUsed(256L * 1024L * 1024L)</span>
<span class="nc" id="L542">                            .heapMax(512L * 1024L * 1024L)</span>
<span class="nc" id="L543">                            .nonHeapUsed(64L * 1024L * 1024L)</span>
<span class="nc" id="L544">                            .gcCount(10)</span>
<span class="nc" id="L545">                            .gcTime(150L)</span>
<span class="nc" id="L546">                            .threadCount(25)</span>
<span class="nc" id="L547">                            .build())</span>
<span class="nc" id="L548">                    .databaseMetrics(DatabaseMetrics.builder()</span>
<span class="nc" id="L549">                            .activeConnections(5)</span>
<span class="nc" id="L550">                            .idleConnections(10)</span>
<span class="nc" id="L551">                            .totalConnections(15)</span>
<span class="nc" id="L552">                            .queryCount(1000L)</span>
<span class="nc" id="L553">                            .averageQueryTime(25.5f)</span>
<span class="nc" id="L554">                            .build())</span>
<span class="nc" id="L555">                    .applicationMetrics(ApplicationMetrics.builder()</span>
<span class="nc" id="L556">                            .uptime(ManagementFactory.getRuntimeMXBean().getUptime())</span>
<span class="nc" id="L557">                            .totalRequests(5000L)</span>
<span class="nc" id="L558">                            .totalErrors(25L)</span>
<span class="nc" id="L559">                            .averageResponseTime(120.0)</span>
<span class="nc" id="L560">                            .peakResponseTime(500.0)</span>
<span class="nc" id="L561">                            .activeUsers(50)</span>
<span class="nc" id="L562">                            .documentsProcessed(1000L)</span>
<span class="nc" id="L563">                            .storageUsed(1024L * 1024L * 1024L)</span>
<span class="nc" id="L564">                            .requestCount(5000L) // Same as totalRequests for test compatibility</span>
<span class="nc" id="L565">                            .errorCount(25L) // Same as totalErrors for test compatibility</span>
<span class="nc" id="L566">                            .cacheStatistics(CacheStatistics.builder()</span>
<span class="nc" id="L567">                                    .hitCount(850L)</span>
<span class="nc" id="L568">                                    .missCount(150L)</span>
<span class="nc" id="L569">                                    .hitRate(0.85)</span>
<span class="nc" id="L570">                                    .evictionCount(10L)</span>
<span class="nc" id="L571">                                    .size(1000L)</span>
<span class="nc" id="L572">                                    .build())</span>
<span class="nc" id="L573">                            .build())</span>
<span class="nc" id="L574">                    .build();</span>

<span class="nc" id="L576">        } catch (Exception e) {</span>
<span class="nc" id="L577">            log.error(&quot;Failed to get system performance&quot;, e);</span>
<span class="nc" id="L578">            throw new RuntimeException(&quot;Failed to get system performance: &quot; + e.getMessage());</span>
        }
    }

    /**
     * Get system health.
     * Implements getSystemHealth query from diagnostics-schema.graphqls.
     */
    @QueryMapping
    @PreAuthorize(&quot;hasRole('ADMIN') or hasRole('USER')&quot;)
    public SystemHealth getSystemHealth() {
<span class="nc" id="L589">        log.info(&quot;GraphQL getSystemHealth called&quot;);</span>

        // Check authentication - only block truly anonymous users
<span class="nc" id="L592">        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();</span>
<span class="nc bnc" id="L593" title="All 2 branches missed.">        if (authentication == null ||</span>
<span class="nc bnc" id="L594" title="All 4 branches missed.">            (authentication.getName() != null &amp;&amp; authentication.getName().equals(&quot;anonymousUser&quot;) &amp;&amp;</span>
<span class="nc bnc" id="L595" title="All 2 branches missed.">             authentication.getAuthorities().isEmpty())) {</span>
<span class="nc" id="L596">            throw new RuntimeException(&quot;Authentication required for system health access&quot;);</span>
        }

        try {
            // Get overall health status
<span class="nc" id="L601">            org.springframework.boot.actuate.health.HealthComponent healthComponent = healthEndpoint.health();</span>

            Health overallHealth;
<span class="nc bnc" id="L604" title="All 2 branches missed.">            if (healthComponent instanceof org.springframework.boot.actuate.health.SystemHealth) {</span>
<span class="nc" id="L605">                org.springframework.boot.actuate.health.SystemHealth systemHealth =</span>
                    (org.springframework.boot.actuate.health.SystemHealth) healthComponent;
<span class="nc" id="L607">                overallHealth = Health.up().build(); // Default to UP for system health</span>
<span class="nc bnc" id="L608" title="All 2 branches missed.">            } else if (healthComponent instanceof Health) {</span>
<span class="nc" id="L609">                overallHealth = (Health) healthComponent;</span>
            } else {
<span class="nc" id="L611">                overallHealth = Health.up().build(); // Default fallback</span>
            }

<span class="nc" id="L614">            HealthStatus overallStatus = mapSpringHealthStatus(overallHealth.getStatus());</span>

            // Create component health list
<span class="nc" id="L617">            List&lt;ComponentHealth&gt; components = new ArrayList&lt;&gt;();</span>

            // Create mock components for demonstration
<span class="nc" id="L620">            components.add(createMockComponentHealth(&quot;database&quot;, HealthStatus.UP));</span>
<span class="nc" id="L621">            components.add(createMockComponentHealth(&quot;diskSpace&quot;, HealthStatus.UP));</span>
<span class="nc" id="L622">            components.add(createMockComponentHealth(&quot;ping&quot;, HealthStatus.UP));</span>

            // Create mock issues
<span class="nc" id="L625">            List&lt;HealthIssue&gt; issues = new ArrayList&lt;&gt;();</span>
<span class="nc bnc" id="L626" title="All 2 branches missed.">            if (overallStatus != HealthStatus.UP) {</span>
<span class="nc" id="L627">                issues.add(HealthIssue.builder()</span>
<span class="nc" id="L628">                        .severity(IssueSeverity.WARNING)</span>
<span class="nc" id="L629">                        .component(&quot;system&quot;)</span>
<span class="nc" id="L630">                        .message(&quot;System health check detected minor issues&quot;)</span>
<span class="nc" id="L631">                        .resolution(&quot;Monitor system performance&quot;)</span>
<span class="nc" id="L632">                        .recommendation(&quot;Increase monitoring frequency and check system resources&quot;)</span>
<span class="nc" id="L633">                        .timestamp(OffsetDateTime.now())</span>
<span class="nc" id="L634">                        .build());</span>
            }

<span class="nc" id="L637">            return SystemHealth.builder()</span>
<span class="nc" id="L638">                    .status(overallStatus)</span>
<span class="nc" id="L639">                    .timestamp(OffsetDateTime.now())</span>
<span class="nc" id="L640">                    .version(&quot;1.0.0&quot;)</span>
<span class="nc" id="L641">                    .uptime(ManagementFactory.getRuntimeMXBean().getUptime())</span>
<span class="nc" id="L642">                    .components(components)</span>
<span class="nc" id="L643">                    .overallScore(95.0f) // Mock score</span>
<span class="nc" id="L644">                    .issues(issues)</span>
<span class="nc" id="L645">                    .recommendations(List.of(&quot;Regular system maintenance&quot;, &quot;Monitor disk usage&quot;))</span>
<span class="nc" id="L646">                    .build();</span>

<span class="nc" id="L648">        } catch (Exception e) {</span>
<span class="nc" id="L649">            log.error(&quot;Failed to get system health&quot;, e);</span>
<span class="nc" id="L650">            throw new RuntimeException(&quot;Failed to get system health: &quot; + e.getMessage());</span>
        }
    }

    /**
     * Get diagnostic history.
     * Implements getDiagnosticHistory query from diagnostics-schema.graphqls.
     */
    @QueryMapping
    public List&lt;DiagnosticResult&gt; getDiagnosticHistory(@Argument Integer limit, @Argument OffsetDateTime dateFrom, @Argument OffsetDateTime dateTo) {
<span class="nc" id="L660">        log.info(&quot;GraphQL getDiagnosticHistory called with limit: {}, dateFrom: {}, dateTo: {}&quot;, limit, dateFrom, dateTo);</span>

        try {
<span class="nc" id="L663">            List&lt;DiagnosticResult&gt; history = new ArrayList&lt;&gt;();</span>

            // Create mock diagnostic history
<span class="nc bnc" id="L666" title="All 4 branches missed.">            for (int i = 0; i &lt; (limit != null ? limit : 10); i++) {</span>
<span class="nc" id="L667">                OffsetDateTime timestamp = OffsetDateTime.now().minusHours(i * 6);</span>

<span class="nc" id="L669">                history.add(DiagnosticResult.builder()</span>
<span class="nc" id="L670">                        .testId(&quot;test-&quot; + (i + 1))</span>
<span class="nc" id="L671">                        .testSuite(&quot;system-diagnostics&quot;)</span>
<span class="nc bnc" id="L672" title="All 2 branches missed.">                        .status(i % 3 == 0 ? TestStatus.FAILED : TestStatus.PASSED)</span>
<span class="nc bnc" id="L673" title="All 2 branches missed.">                        .overallResult(i % 3 == 0 ? TestStatus.FAILED : TestStatus.PASSED)</span>
<span class="nc" id="L674">                        .startTime(timestamp.minusMinutes(5))</span>
<span class="nc" id="L675">                        .endTime(timestamp)</span>
<span class="nc" id="L676">                        .duration(300000L) // 5 minutes</span>
<span class="nc" id="L677">                        .testsRun(10)</span>
<span class="nc bnc" id="L678" title="All 2 branches missed.">                        .testsPassed(i % 3 == 0 ? 8 : 10)</span>
<span class="nc bnc" id="L679" title="All 2 branches missed.">                        .testsFailed(i % 3 == 0 ? 2 : 0)</span>
<span class="nc" id="L680">                        .testsSkipped(0) // No skipped tests</span>
<span class="nc" id="L681">                        .recommendations(List.of(&quot;Regular monitoring&quot;, &quot;System maintenance&quot;))</span>
<span class="nc" id="L682">                        .summary(DiagnosticSummary.builder()</span>
<span class="nc bnc" id="L683" title="All 2 branches missed.">                                .overallHealth(i % 3 == 0 ? HealthStatus.DEGRADED : HealthStatus.UP)</span>
<span class="nc bnc" id="L684" title="All 2 branches missed.">                                .criticalIssues(i % 3 == 0 ? 1 : 0)</span>
<span class="nc" id="L685">                                .warnings(0)</span>
<span class="nc" id="L686">                                .recommendations(List.of(&quot;Regular monitoring&quot;))</span>
<span class="nc" id="L687">                                .nextCheckRecommended(timestamp.plusHours(24))</span>
<span class="nc" id="L688">                                .build())</span>
<span class="nc" id="L689">                        .build());</span>
            }

<span class="nc" id="L692">            return history;</span>

<span class="nc" id="L694">        } catch (Exception e) {</span>
<span class="nc" id="L695">            log.error(&quot;Failed to get diagnostic history&quot;, e);</span>
<span class="nc" id="L696">            throw new RuntimeException(&quot;Failed to get diagnostic history: &quot; + e.getMessage());</span>
        }
    }

    /**
     * Get performance history.
     * Implements getPerformanceHistory query from diagnostics-schema.graphqls.
     */
    @QueryMapping
    public List&lt;SystemPerformance&gt; getPerformanceHistory(@Argument String component, @Argument Integer hours) {
<span class="nc" id="L706">        log.info(&quot;GraphQL getPerformanceHistory called with component: {}, hours: {}&quot;, component, hours);</span>

        try {
<span class="nc" id="L709">            List&lt;SystemPerformance&gt; history = new ArrayList&lt;&gt;();</span>

            // Create mock performance history
<span class="nc bnc" id="L712" title="All 4 branches missed.">            for (int i = 0; i &lt; (hours != null ? hours : 10); i++) {</span>
<span class="nc" id="L713">                OffsetDateTime timestamp = OffsetDateTime.now().minusHours(i);</span>

<span class="nc" id="L715">                history.add(SystemPerformance.builder()</span>
<span class="nc" id="L716">                        .timestamp(timestamp)</span>
<span class="nc" id="L717">                        .cpuUsage(40.0f + (float)(Math.random() * 20))</span>
<span class="nc" id="L718">                        .memoryUsage(60.0f + (float)(Math.random() * 20))</span>
<span class="nc" id="L719">                        .diskUsage(45.0f + (float)(Math.random() * 10))</span>
<span class="nc" id="L720">                        .networkIO(NetworkIO.builder()</span>
<span class="nc" id="L721">                                .bytesIn((long)(Math.random() * 1024 * 1024))</span>
<span class="nc" id="L722">                                .bytesOut((long)(Math.random() * 512 * 1024))</span>
<span class="nc" id="L723">                                .packetsIn((long)(Math.random() * 1000))</span>
<span class="nc" id="L724">                                .packetsOut((long)(Math.random() * 800))</span>
<span class="nc" id="L725">                                .build())</span>
<span class="nc" id="L726">                        .jvmMetrics(JvmMetrics.builder()</span>
<span class="nc" id="L727">                                .heapUsed((long)(200 + Math.random() * 100) * 1024 * 1024)</span>
<span class="nc" id="L728">                                .heapMax(512L * 1024L * 1024L)</span>
<span class="nc" id="L729">                                .nonHeapUsed(64L * 1024L * 1024L)</span>
<span class="nc" id="L730">                                .gcCount((int)(Math.random() * 20))</span>
<span class="nc" id="L731">                                .gcTime((long)(Math.random() * 200))</span>
<span class="nc" id="L732">                                .threadCount((int)(20 + Math.random() * 10))</span>
<span class="nc" id="L733">                                .build())</span>
<span class="nc" id="L734">                        .databaseMetrics(DatabaseMetrics.builder()</span>
<span class="nc" id="L735">                                .activeConnections((int)(Math.random() * 10))</span>
<span class="nc" id="L736">                                .idleConnections((int)(5 + Math.random() * 10))</span>
<span class="nc" id="L737">                                .totalConnections(15)</span>
<span class="nc" id="L738">                                .queryCount((long)(Math.random() * 2000))</span>
<span class="nc" id="L739">                                .averageQueryTime((float)(20 + Math.random() * 30))</span>
<span class="nc" id="L740">                                .build())</span>
<span class="nc" id="L741">                        .applicationMetrics(ApplicationMetrics.builder()</span>
<span class="nc" id="L742">                                .uptime(ManagementFactory.getRuntimeMXBean().getUptime())</span>
<span class="nc" id="L743">                                .totalRequests((long)(4000 + Math.random() * 2000))</span>
<span class="nc" id="L744">                                .totalErrors((long)(Math.random() * 50))</span>
<span class="nc" id="L745">                                .averageResponseTime(100.0 + Math.random() * 100)</span>
<span class="nc" id="L746">                                .peakResponseTime(400.0 + Math.random() * 200)</span>
<span class="nc" id="L747">                                .activeUsers((int)(40 + Math.random() * 20))</span>
<span class="nc" id="L748">                                .documentsProcessed((long)(800 + Math.random() * 400))</span>
<span class="nc" id="L749">                                .storageUsed((long)(Math.random() * 2L * 1024L * 1024L * 1024L))</span>
<span class="nc" id="L750">                                .requestCount((long)(4000 + Math.random() * 2000)) // Add missing field</span>
<span class="nc" id="L751">                                .errorCount((long)(Math.random() * 50)) // Add missing field</span>
<span class="nc" id="L752">                                .cacheStatistics(CacheStatistics.builder()</span>
<span class="nc" id="L753">                                        .hitCount((long)(700 + Math.random() * 200))</span>
<span class="nc" id="L754">                                        .missCount((long)(Math.random() * 200))</span>
<span class="nc" id="L755">                                        .hitRate(0.8 + Math.random() * 0.15)</span>
<span class="nc" id="L756">                                        .evictionCount((long)(Math.random() * 20))</span>
<span class="nc" id="L757">                                        .size((long)(900 + Math.random() * 200))</span>
<span class="nc" id="L758">                                        .build())</span>
<span class="nc" id="L759">                                .build())</span>
<span class="nc" id="L760">                        .build());</span>
            }

<span class="nc" id="L763">            return history;</span>

<span class="nc" id="L765">        } catch (Exception e) {</span>
<span class="nc" id="L766">            log.error(&quot;Failed to get performance history&quot;, e);</span>
<span class="nc" id="L767">            throw new RuntimeException(&quot;Failed to get performance history: &quot; + e.getMessage());</span>
        }
    }

    /**
     * Get component health.
     * Implements getComponentHealth query from diagnostics-schema.graphqls.
     */
    @QueryMapping
    public ComponentHealth getComponentHealth(@Argument String componentName) {
<span class="nc" id="L777">        log.info(&quot;GraphQL getComponentHealth called for component: {}&quot;, componentName);</span>

        try {
            // Try to get the actual health indicator
<span class="nc" id="L781">            Map&lt;String, HealthIndicator&gt; healthIndicators = applicationContext.getBeansOfType(HealthIndicator.class);</span>
<span class="nc" id="L782">            HealthIndicator indicator = healthIndicators.get(componentName);</span>

<span class="nc bnc" id="L784" title="All 2 branches missed.">            if (indicator != null) {</span>
<span class="nc" id="L785">                Health health = indicator.health();</span>
<span class="nc" id="L786">                return createComponentHealth(componentName, health);</span>
            } else {
                // Return UNKNOWN status for non-existent components
<span class="nc" id="L789">                return createMockComponentHealth(componentName, HealthStatus.UNKNOWN);</span>
            }

<span class="nc" id="L792">        } catch (Exception e) {</span>
<span class="nc" id="L793">            log.error(&quot;Failed to get component health for: {}&quot;, componentName, e);</span>
<span class="nc" id="L794">            throw new RuntimeException(&quot;Failed to get component health: &quot; + e.getMessage());</span>
        }
    }

    /**
     * Refresh component health.
     * Implements refreshComponentHealth mutation from diagnostics-schema.graphqls.
     */
    @MutationMapping
    public ComponentHealth refreshComponentHealth(@Argument String componentName) {
<span class="nc" id="L804">        log.info(&quot;GraphQL refreshComponentHealth called for component: {}&quot;, componentName);</span>

        try {
            // Force refresh by getting fresh health data
<span class="nc" id="L808">            return getComponentHealth(componentName);</span>

<span class="nc" id="L810">        } catch (Exception e) {</span>
<span class="nc" id="L811">            log.error(&quot;Failed to refresh component health for: {}&quot;, componentName, e);</span>
<span class="nc" id="L812">            throw new RuntimeException(&quot;Failed to refresh component health: &quot; + e.getMessage());</span>
        }
    }

    /**
     * Refresh system health.
     * Implements refreshSystemHealth mutation from diagnostics-schema.graphqls.
     */
    @MutationMapping
    public SystemHealth refreshSystemHealth() {
<span class="nc" id="L822">        log.info(&quot;GraphQL refreshSystemHealth called&quot;);</span>

        try {
            // Force refresh by getting fresh system health data
<span class="nc" id="L826">            return getSystemHealth();</span>

<span class="nc" id="L828">        } catch (Exception e) {</span>
<span class="nc" id="L829">            log.error(&quot;Failed to refresh system health&quot;, e);</span>
<span class="nc" id="L830">            throw new RuntimeException(&quot;Failed to refresh system health: &quot; + e.getMessage());</span>
        }
    }

    /**
     * Run connection test.
     * Implements runConnectionTest mutation from diagnostics-schema.graphqls.
     */
    @MutationMapping
    public ConnectionTestResult runConnectionTest(@Argument ConnectionTestInput input) {
<span class="nc" id="L840">        log.info(&quot;GraphQL runConnectionTest called for: {}&quot;, input.getTarget());</span>

        try {
            // Delegate to existing testConnection method
<span class="nc" id="L844">            return testConnection(input);</span>

<span class="nc" id="L846">        } catch (Exception e) {</span>
<span class="nc" id="L847">            log.error(&quot;Failed to run connection test&quot;, e);</span>
<span class="nc" id="L848">            throw new RuntimeException(&quot;Failed to run connection test: &quot; + e.getMessage());</span>
        }
    }

    /**
     * Run system diagnostics.
     * Implements runSystemDiagnostics mutation from diagnostics-schema.graphqls.
     */
    @MutationMapping
    public DiagnosticResult runSystemDiagnostics(@Argument DiagnosticTestInput input) {
<span class="nc" id="L858">        log.info(&quot;GraphQL runSystemDiagnostics called&quot;);</span>

        try {
            // Delegate to existing runDiagnostics method
<span class="nc" id="L862">            return runDiagnostics(input);</span>

<span class="nc" id="L864">        } catch (Exception e) {</span>
<span class="nc" id="L865">            log.error(&quot;Failed to run system diagnostics&quot;, e);</span>
<span class="nc" id="L866">            throw new RuntimeException(&quot;Failed to run system diagnostics: &quot; + e.getMessage());</span>
        }
    }

    // ===== HELPER METHODS =====

    /**
     * Test storage provider connection internally.
     */
    private boolean testStorageProviderInternal(StorageProvider provider) {
        try {
<span class="nc bnc" id="L877" title="All 2 branches missed.">            if (storageProviderFactory == null) {</span>
<span class="nc" id="L878">                log.warn(&quot;Storage provider factory not available&quot;);</span>
<span class="nc" id="L879">                return false;</span>
            }

            // Check if the provider is available
<span class="nc" id="L883">            boolean isAvailable = storageProviderFactory.isProviderAvailable(provider);</span>
<span class="nc" id="L884">            log.info(&quot;Storage provider {} availability: {}&quot;, provider, isAvailable);</span>

<span class="nc" id="L886">            return isAvailable;</span>
<span class="nc" id="L887">        } catch (Exception e) {</span>
<span class="nc" id="L888">            log.error(&quot;Error testing storage provider {}&quot;, provider, e);</span>
<span class="nc" id="L889">            return false;</span>
        }
    }

    /**
     * Perform connection test based on input parameters.
     */
    private boolean performConnectionTest(ConnectionTestInput input) {
        try {
<span class="nc bnc" id="L898" title="All 5 branches missed.">            switch (input.getTestType()) {</span>
                case DATABASE:
<span class="nc" id="L900">                    return testDatabaseConnectionInternal();</span>
                case SHAREPOINT:
<span class="nc" id="L902">                    return testSharePointConnectionInternal();</span>
                case ELASTICSEARCH:
<span class="nc" id="L904">                    return testElasticsearchConnectionInternal();</span>
                case STORAGE_PROVIDER:
                case STORAGE:
                    // For generic storage provider test, try LOCAL as default
<span class="nc" id="L908">                    return testStorageProviderInternal(StorageProvider.LOCAL);</span>
                default:
<span class="nc" id="L910">                    log.warn(&quot;Unsupported connection test type: {}&quot;, input.getTestType());</span>
<span class="nc" id="L911">                    return false;</span>
            }
<span class="nc" id="L913">        } catch (Exception e) {</span>
<span class="nc" id="L914">            log.error(&quot;Error performing connection test for {}&quot;, input.getTestType(), e);</span>
<span class="nc" id="L915">            return false;</span>
        }
    }

    /**
     * Test database connection internally.
     */
    private boolean testDatabaseConnectionInternal() {
<span class="nc bnc" id="L923" title="All 2 branches missed.">        if (dataSource == null) {</span>
<span class="nc" id="L924">            return false;</span>
        }

<span class="nc" id="L927">        try (Connection connection = dataSource.getConnection()) {</span>
<span class="nc" id="L928">            return connection.isValid(5); // 5 second timeout</span>
<span class="nc" id="L929">        } catch (Exception e) {</span>
<span class="nc" id="L930">            log.error(&quot;Database connection test failed&quot;, e);</span>
<span class="nc" id="L931">            return false;</span>
        }
    }

    /**
     * Test SharePoint connection internally.
     */
    private boolean testSharePointConnectionInternal() {
<span class="nc bnc" id="L939" title="All 2 branches missed.">        if (sharePointTestUtility == null) {</span>
<span class="nc" id="L940">            return false;</span>
        }

        try {
<span class="nc" id="L944">            sharePointTestUtility.testSharePointConnection();</span>
<span class="nc" id="L945">            return true;</span>
<span class="nc" id="L946">        } catch (Exception e) {</span>
<span class="nc" id="L947">            log.error(&quot;SharePoint connection test failed&quot;, e);</span>
<span class="nc" id="L948">            return false;</span>
        }
    }

    /**
     * Test Elasticsearch connection internally.
     */
    private boolean testElasticsearchConnectionInternal() {
        try {
<span class="nc" id="L957">            HealthIndicator esHealthIndicator = applicationContext.getBean(&quot;elasticsearch&quot;, HealthIndicator.class);</span>
<span class="nc" id="L958">            Health health = esHealthIndicator.health();</span>
<span class="nc" id="L959">            return health.getStatus().getCode().equals(&quot;UP&quot;);</span>
<span class="nc" id="L960">        } catch (Exception e) {</span>
<span class="nc" id="L961">            log.error(&quot;Elasticsearch connection test failed&quot;, e);</span>
<span class="nc" id="L962">            return false;</span>
        }
    }

    /**
     * Perform diagnostic tests based on input parameters.
     */
    private List&lt;DiagnosticTest&gt; performDiagnosticTests(DiagnosticTestInput input) {
<span class="nc" id="L970">        List&lt;DiagnosticTest&gt; tests = new ArrayList&lt;&gt;();</span>

<span class="nc bnc" id="L972" title="All 4 branches missed.">        if (input.getTestTypes() == null || input.getTestTypes().isEmpty()) {</span>
            // Default tests if none specified
<span class="nc" id="L974">            tests.add(performSingleDiagnosticTest(DiagnosticTestType.DATABASE_CONNECTIVITY));</span>
<span class="nc" id="L975">            tests.add(performSingleDiagnosticTest(DiagnosticTestType.HEALTH_CHECK));</span>
        } else {
<span class="nc bnc" id="L977" title="All 2 branches missed.">            for (DiagnosticTestType testType : input.getTestTypes()) {</span>
<span class="nc" id="L978">                tests.add(performSingleDiagnosticTest(testType));</span>
<span class="nc" id="L979">            }</span>
        }

<span class="nc" id="L982">        return tests;</span>
    }

    /**
     * Perform a single diagnostic test.
     */
    private DiagnosticTest performSingleDiagnosticTest(DiagnosticTestType testType) {
<span class="nc" id="L989">        long startTime = System.currentTimeMillis();</span>
<span class="nc" id="L990">        String testId = UUID.randomUUID().toString();</span>

        try {
<span class="nc" id="L993">            boolean success = false;</span>
<span class="nc" id="L994">            String message = &quot;&quot;;</span>

<span class="nc bnc" id="L996" title="All 6 branches missed.">            switch (testType) {</span>
                case DATABASE_CONNECTIVITY:
<span class="nc" id="L998">                    success = testDatabaseConnectionInternal();</span>
<span class="nc bnc" id="L999" title="All 2 branches missed.">                    message = success ? &quot;Database connection successful&quot; : &quot;Database connection failed&quot;;</span>
<span class="nc" id="L1000">                    break;</span>
                case SHAREPOINT_CONNECTIVITY:
<span class="nc" id="L1002">                    success = testSharePointConnectionInternal();</span>
<span class="nc bnc" id="L1003" title="All 2 branches missed.">                    message = success ? &quot;SharePoint connection successful&quot; : &quot;SharePoint connection failed&quot;;</span>
<span class="nc" id="L1004">                    break;</span>
                case ELASTICSEARCH_CONNECTIVITY:
<span class="nc" id="L1006">                    success = testElasticsearchConnectionInternal();</span>
<span class="nc bnc" id="L1007" title="All 2 branches missed.">                    message = success ? &quot;Elasticsearch connection successful&quot; : &quot;Elasticsearch connection failed&quot;;</span>
<span class="nc" id="L1008">                    break;</span>
                case STORAGE_PROVIDER_CONNECTIVITY:
<span class="nc" id="L1010">                    success = testStorageProviderInternal(StorageProvider.LOCAL);</span>
<span class="nc bnc" id="L1011" title="All 2 branches missed.">                    message = success ? &quot;Storage provider connection successful&quot; : &quot;Storage provider connection failed&quot;;</span>
<span class="nc" id="L1012">                    break;</span>
                case HEALTH_CHECK:
<span class="nc" id="L1014">                    success = true; // Always pass for basic health check</span>
<span class="nc" id="L1015">                    message = &quot;Health check completed&quot;;</span>
<span class="nc" id="L1016">                    break;</span>
                default:
<span class="nc" id="L1018">                    success = false;</span>
<span class="nc" id="L1019">                    message = &quot;Test type not implemented: &quot; + testType;</span>
                    break;
            }

<span class="nc" id="L1023">            long duration = System.currentTimeMillis() - startTime;</span>

<span class="nc" id="L1025">            return DiagnosticTest.builder()</span>
<span class="nc" id="L1026">                    .testId(testId)</span>
<span class="nc" id="L1027">                    .testType(testType)</span>
<span class="nc" id="L1028">                    .name(testType.name())</span>
<span class="nc bnc" id="L1029" title="All 2 branches missed.">                    .status(success ? TestStatus.PASSED : TestStatus.FAILED)</span>
<span class="nc" id="L1030">                    .message(message)</span>
<span class="nc" id="L1031">                    .duration(duration)</span>
<span class="nc" id="L1032">                    .timestamp(OffsetDateTime.now())</span>
<span class="nc bnc" id="L1033" title="All 2 branches missed.">                    .details(success ? &quot;Test completed successfully&quot; : &quot;Test failed&quot;)</span>
<span class="nc" id="L1034">                    .build();</span>

<span class="nc" id="L1036">        } catch (Exception e) {</span>
<span class="nc" id="L1037">            long duration = System.currentTimeMillis() - startTime;</span>
<span class="nc" id="L1038">            log.error(&quot;Diagnostic test failed for {}&quot;, testType, e);</span>

<span class="nc" id="L1040">            return DiagnosticTest.builder()</span>
<span class="nc" id="L1041">                    .testId(testId)</span>
<span class="nc" id="L1042">                    .testType(testType)</span>
<span class="nc" id="L1043">                    .name(testType.name())</span>
<span class="nc" id="L1044">                    .status(TestStatus.FAILED)</span>
<span class="nc" id="L1045">                    .message(&quot;Test failed: &quot; + e.getMessage())</span>
<span class="nc" id="L1046">                    .duration(duration)</span>
<span class="nc" id="L1047">                    .timestamp(OffsetDateTime.now())</span>
<span class="nc" id="L1048">                    .details(&quot;Exception: &quot; + e.getClass().getSimpleName() + &quot; - &quot; + e.getMessage())</span>
<span class="nc" id="L1049">                    .build();</span>
        }
    }

    /**
     * Create ComponentHealth from Spring Boot Health object.
     */
    private ComponentHealth createComponentHealth(String componentName, Health health) {
<span class="nc" id="L1057">        HealthStatus status = mapSpringHealthStatus(health.getStatus());</span>
<span class="nc" id="L1058">        Map&lt;String, Object&gt; details = health.getDetails();</span>

<span class="nc" id="L1060">        List&lt;HealthMetric&gt; metrics = new ArrayList&lt;&gt;();</span>
<span class="nc bnc" id="L1061" title="All 2 branches missed.">        for (Map.Entry&lt;String, Object&gt; entry : details.entrySet()) {</span>
<span class="nc bnc" id="L1062" title="All 2 branches missed.">            if (entry.getValue() instanceof Number) {</span>
<span class="nc" id="L1063">                metrics.add(HealthMetric.builder()</span>
<span class="nc" id="L1064">                        .name(entry.getKey())</span>
<span class="nc" id="L1065">                        .value(entry.getValue().toString())</span>
<span class="nc" id="L1066">                        .status(MetricStatus.NORMAL)</span>
<span class="nc" id="L1067">                        .build());</span>
            }
<span class="nc" id="L1069">        }</span>

<span class="nc" id="L1071">        return ComponentHealth.builder()</span>
<span class="nc" id="L1072">                .name(componentName)</span>
<span class="nc" id="L1073">                .status(status)</span>
<span class="nc" id="L1074">                .details(ComponentHealthDetails.builder()</span>
<span class="nc bnc" id="L1075" title="All 2 branches missed.">                        .description(&quot;Component health check&quot;)</span>
<span class="nc" id="L1076">                        .connected(status == HealthStatus.UP)</span>
<span class="nc" id="L1077">                        .metrics(metrics)</span>
<span class="nc" id="L1078">                        .build())</span>
<span class="nc" id="L1079">                .lastChecked(OffsetDateTime.now())</span>
<span class="nc" id="L1080">                .responseTime(0L) // TODO: Implement response time tracking</span>
<span class="nc bnc" id="L1081" title="All 2 branches missed.">                .errorCount(status == HealthStatus.UP ? 0 : 1)</span>
<span class="nc bnc" id="L1082" title="All 2 branches missed.">                .warningCount(status == HealthStatus.DEGRADED ? 1 : 0)</span>
<span class="nc" id="L1083">                .build();</span>
    }

    /**
     * Map Spring Boot Health Status to our HealthStatus enum.
     */
    private HealthStatus mapSpringHealthStatus(org.springframework.boot.actuate.health.Status status) {
<span class="nc" id="L1090">        String code = status.getCode();</span>
<span class="nc bnc" id="L1091" title="All 4 branches missed.">        switch (code) {</span>
            case &quot;UP&quot;:
<span class="nc" id="L1093">                return HealthStatus.UP;</span>
            case &quot;DOWN&quot;:
<span class="nc" id="L1095">                return HealthStatus.DOWN;</span>
            case &quot;OUT_OF_SERVICE&quot;:
<span class="nc" id="L1097">                return HealthStatus.MAINTENANCE;</span>
            default:
<span class="nc" id="L1099">                return HealthStatus.UNKNOWN;</span>
        }
    }

    /**
     * Determine overall system health status based on component health.
     */
    private HealthStatus determineOverallStatus(int healthyComponents, int totalComponents) {
<span class="nc bnc" id="L1107" title="All 2 branches missed.">        if (totalComponents == 0) {</span>
<span class="nc" id="L1108">            return HealthStatus.UNKNOWN;</span>
        }

<span class="nc" id="L1111">        float healthRatio = (float) healthyComponents / totalComponents;</span>

<span class="nc bnc" id="L1113" title="All 2 branches missed.">        if (healthRatio == 1.0f) {</span>
<span class="nc" id="L1114">            return HealthStatus.UP;</span>
<span class="nc bnc" id="L1115" title="All 2 branches missed.">        } else if (healthRatio &gt;= 0.7f) {</span>
<span class="nc" id="L1116">            return HealthStatus.DEGRADED;</span>
        } else {
<span class="nc" id="L1118">            return HealthStatus.DOWN;</span>
        }
    }

    /**
     * Create a failed connection test result.
     */
    private ConnectionTestResult createFailedConnectionTest(ConnectionTestType testType, String target, String message) {
<span class="nc" id="L1126">        return ConnectionTestResult.builder()</span>
<span class="nc" id="L1127">                .testId(UUID.randomUUID().toString())</span>
<span class="nc" id="L1128">                .testType(testType)</span>
<span class="nc" id="L1129">                .target(target)</span>
<span class="nc" id="L1130">                .status(TestStatus.FAILED)</span>
<span class="nc" id="L1131">                .success(false)</span>
<span class="nc" id="L1132">                .responseTime(0L)</span>
<span class="nc" id="L1133">                .message(message)</span>
<span class="nc" id="L1134">                .timestamp(OffsetDateTime.now())</span>
<span class="nc" id="L1135">                .retryCount(0)</span>
<span class="nc" id="L1136">                .build();</span>
    }

    /**
     * Create a mock component health for testing purposes.
     */
    private ComponentHealth createMockComponentHealth(String name, HealthStatus status) {
        // Create mock details
<span class="nc" id="L1144">        ComponentHealthDetails details = ComponentHealthDetails.builder()</span>
<span class="nc" id="L1145">                .description(name + &quot; component is &quot; + status.name().toLowerCase())</span>
<span class="nc bnc" id="L1146" title="All 2 branches missed.">                .version(&quot;1.0.0&quot;)</span>
<span class="nc" id="L1147">                .connected(status == HealthStatus.UP)</span>
<span class="nc bnc" id="L1148" title="All 2 branches missed.">                .lastError(status != HealthStatus.UP ? &quot;Mock error for testing&quot; : null)</span>
<span class="nc" id="L1149">                .metrics(List.of()) // Empty metrics for now</span>
<span class="nc" id="L1150">                .configuration(&quot;{\&quot;enabled\&quot;: true}&quot;)</span>
<span class="nc" id="L1151">                .build();</span>

<span class="nc" id="L1153">        return ComponentHealth.builder()</span>
<span class="nc" id="L1154">                .name(name)</span>
<span class="nc" id="L1155">                .status(status)</span>
<span class="nc" id="L1156">                .details(details)</span>
<span class="nc" id="L1157">                .lastChecked(OffsetDateTime.now())</span>
<span class="nc" id="L1158">                .responseTime(50L) // Mock response time</span>
<span class="nc bnc" id="L1159" title="All 2 branches missed.">                .errorCount(status == HealthStatus.UP ? 0 : 1)</span>
<span class="nc bnc" id="L1160" title="All 2 branches missed.">                .warningCount(status == HealthStatus.DEGRADED ? 1 : 0)</span>
<span class="nc" id="L1161">                .build();</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>