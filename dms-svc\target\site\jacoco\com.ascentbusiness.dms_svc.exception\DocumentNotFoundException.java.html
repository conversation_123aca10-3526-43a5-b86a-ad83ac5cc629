<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>DocumentNotFoundException.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.exception</a> &gt; <span class="el_source">DocumentNotFoundException.java</span></div><h1>DocumentNotFoundException.java</h1><pre class="source lang-java linenums">package com.ascentbusiness.dms_svc.exception;

import java.util.Map;

/**
 * Exception thrown when a requested document cannot be found.
 * Extends DmsBusinessException with specific error handling for document lookup failures.
 */
public class DocumentNotFoundException extends DmsBusinessException {

    public DocumentNotFoundException(String message) {
<span class="nc" id="L12">        super(message, &quot;DOCUMENT_NOT_FOUND&quot;);</span>
<span class="nc" id="L13">    }</span>

    public DocumentNotFoundException(String message, Throwable cause) {
<span class="nc" id="L16">        super(message, &quot;DOCUMENT_NOT_FOUND&quot;, Map.of(), cause);</span>
<span class="nc" id="L17">    }</span>

    public DocumentNotFoundException(Long documentId) {
<span class="nc" id="L20">        super(String.format(&quot;Document not found with ID: %d&quot;, documentId),</span>
              &quot;DOCUMENT_NOT_FOUND&quot;,
<span class="nc" id="L22">              Map.of(&quot;documentId&quot;, documentId));</span>
<span class="nc" id="L23">    }</span>

    public DocumentNotFoundException(String identifier, String identifierType) {
<span class="nc" id="L26">        super(String.format(&quot;Document not found with %s: %s&quot;, identifierType, identifier),</span>
              &quot;DOCUMENT_NOT_FOUND&quot;,
<span class="nc" id="L28">              Map.of(&quot;identifier&quot;, identifier, &quot;identifierType&quot;, identifierType));</span>
<span class="nc" id="L29">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>