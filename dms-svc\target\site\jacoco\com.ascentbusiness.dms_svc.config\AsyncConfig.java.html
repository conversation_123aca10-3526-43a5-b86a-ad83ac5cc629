<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>AsyncConfig.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.config</a> &gt; <span class="el_source">AsyncConfig.java</span></div><h1>AsyncConfig.java</h1><pre class="source lang-java linenums">package com.ascentbusiness.dms_svc.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;

@Configuration
@EnableAsync
@Profile(&quot;!test&quot;)
<span class="nc" id="L14">public class AsyncConfig {</span>

    @Bean(name = &quot;taskExecutor&quot;)
    public Executor taskExecutor() {
<span class="nc" id="L18">        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();</span>
<span class="nc" id="L19">        executor.setCorePoolSize(10);</span>
<span class="nc" id="L20">        executor.setMaxPoolSize(50);</span>
<span class="nc" id="L21">        executor.setQueueCapacity(1000);</span>
<span class="nc" id="L22">        executor.setThreadNamePrefix(&quot;Async-&quot;);</span>

        // Set the custom task decorator to propagate MDC context
<span class="nc" id="L25">        executor.setTaskDecorator(new MdcTaskDecorator());</span>

<span class="nc" id="L27">        executor.initialize();</span>
<span class="nc" id="L28">        return executor;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>