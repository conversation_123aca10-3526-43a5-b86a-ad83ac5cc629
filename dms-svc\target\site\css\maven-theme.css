/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

body {
  padding: 0px 0px 10px 0px;
}
body, td, select, input, li{
  font-family: Verdana, Helvetica, Arial, sans-serif;
  font-size: 13px;
}
code{
  font-family: Courier, monospace;
  font-size: 13px;
}
a {
  text-decoration: none;
}
a:link {
  color:#36a;
}
a:visited  {
  color:#47a;
}
a:active, a:hover {
  color:#69c;
}
#legend li.externalLink {
  background: url(../images/external.png) left top no-repeat;
  padding-left: 18px;
}
a.externalLink, a.externalLink:link, a.externalLink:visited, a.externalLink:active, a.externalLink:hover {
  background: url(../images/external.png) right center no-repeat;
  padding-right: 18px;
}
#legend li.newWindow {
  background: url(../images/newwindow.png) left top no-repeat;
  padding-left: 18px;
}
a.newWindow, a.newWindow:link, a.newWindow:visited, a.newWindow:active, a.newWindow:hover {
  background: url(../images/newwindow.png) right center no-repeat;
  padding-right: 18px;
}
h2 {
  padding: 4px 4px 4px 6px;
  border: 1px solid #999;
  color: #900;
  background-color: #ddd;
  font-weight:900;
  font-size: x-large;
}
h3 {
  padding: 4px 4px 4px 6px;
  border: 1px solid #aaa;
  color: #900;
  background-color: #eee;
  font-weight: normal;
  font-size: large;
}
h4 {
  padding: 4px 4px 4px 6px;
  border: 1px solid #bbb;
  color: #900;
  background-color: #fff;
  font-weight: normal;
  font-size: large;
}
h5 {
  padding: 4px 4px 4px 6px;
  color: #900;
  font-size: medium;
}
p {
  line-height: 1.3em;
  font-size: small;
}
#breadcrumbs {
  border-top: 1px solid #aaa;
  border-bottom: 1px solid #aaa;
  background-color: #ccc;
}
#leftColumn {
  margin: 10px 0 0 5px;
  border: 1px solid #999;
  background-color: #eee;
  padding-bottom: 3px; /* IE-9 scrollbar-fix */
}
#navcolumn h5 {
  font-size: smaller;
  border-bottom: 1px solid #aaaaaa;
  padding-top: 2px;
  color: #000;
}

table.bodyTable th {
  color: white;
  background-color: #bbb;
  text-align: left;
  font-weight: bold;
}

table.bodyTable th, table.bodyTable td {
  font-size: 1em;
}

table.bodyTable tr.a {
  background-color: #ddd;
}

table.bodyTable tr.b {
  background-color: #eee;
}

.source {
  border: 1px solid #999;
}
dl {
  padding: 4px 4px 4px 6px;
  border: 1px solid #aaa;
  background-color: #ffc;
}
dt {
  color: #900;
}
#organizationLogo img, #projectLogo img, #projectLogo span{
  margin: 8px;
}
#banner {
  border-bottom: 1px solid #fff;
}
.errormark, .warningmark, .donemark, .infomark {
  background: url(../images/icon_error_sml.gif) no-repeat;
}

.warningmark {
  background-image: url(../images/icon_warning_sml.gif);
}

.donemark {
  background-image: url(../images/icon_success_sml.gif);
}

.infomark {
  background-image: url(../images/icon_info_sml.gif);
}

