<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>CorrelationIdInterceptor.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.interceptor</a> &gt; <span class="el_source">CorrelationIdInterceptor.java</span></div><h1>CorrelationIdInterceptor.java</h1><pre class="source lang-java linenums">package com.ascentbusiness.dms_svc.interceptor;

import graphql.ExecutionResult;
import graphql.execution.instrumentation.InstrumentationContext;
import graphql.execution.instrumentation.InstrumentationState;
import graphql.execution.instrumentation.SimpleInstrumentation;
import graphql.execution.instrumentation.parameters.InstrumentationExecutionParameters;
import graphql.execution.instrumentation.parameters.InstrumentationFieldFetchParameters;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

/**
 * GraphQL instrumentation to handle correlation ID and service metadata in GraphQL responses.
 * 
 * This interceptor ensures that:
 * 1. Correlation ID is available throughout GraphQL execution
 * 2. Additional service metadata is added to GraphQL responses
 * 3. Proper logging context is maintained during async GraphQL operations
 */
@Component
<span class="nc" id="L30">public class CorrelationIdInterceptor extends SimpleInstrumentation {</span>

<span class="nc" id="L32">    private static final Logger logger = LoggerFactory.getLogger(CorrelationIdInterceptor.class);</span>
    
    private static final String RESPONSE_HEADER_GRAPHQL_OPERATION = &quot;X-GraphQL-Operation&quot;;
    private static final String RESPONSE_HEADER_EXECUTION_TIME = &quot;X-Execution-Time-Ms&quot;;

    @Value(&quot;${dms.correlation.header-name:X-Correlation-ID}&quot;)
    private String correlationIdHeaderName;

    @Override
    public InstrumentationContext&lt;ExecutionResult&gt; beginExecution(
            InstrumentationExecutionParameters parameters,
            InstrumentationState state) {

<span class="nc" id="L45">        long startTime = System.currentTimeMillis();</span>
<span class="nc" id="L46">        String correlationId = MDC.get(&quot;correlationId&quot;);</span>
<span class="nc bnc" id="L47" title="All 2 branches missed.">        String operationName = parameters.getQuery() != null ?</span>
<span class="nc" id="L48">                extractOperationName(parameters.getQuery()) : &quot;anonymous&quot;;</span>

<span class="nc" id="L50">        logger.debug(&quot;Starting GraphQL execution - Operation: {}, Correlation ID: {}&quot;,</span>
                operationName, correlationId);

        // Capture the current MDC context to propagate throughout execution
<span class="nc" id="L54">        Map&lt;String, String&gt; mdcContext = MDC.getCopyOfContextMap();</span>

<span class="nc" id="L56">        return new InstrumentationContext&lt;ExecutionResult&gt;() {</span>
            @Override
            public void onCompleted(ExecutionResult result, Throwable t) {
                // Restore MDC context for completion logging
<span class="nc bnc" id="L60" title="All 4 branches missed.">                if (mdcContext != null &amp;&amp; !mdcContext.isEmpty()) {</span>
<span class="nc" id="L61">                    MDC.setContextMap(mdcContext);</span>
                }

<span class="nc" id="L64">                long executionTime = System.currentTimeMillis() - startTime;</span>

                // Add GraphQL-specific headers to response
<span class="nc" id="L67">                addGraphQLResponseHeaders(operationName, executionTime, correlationId);</span>

<span class="nc bnc" id="L69" title="All 2 branches missed.">                if (t != null) {</span>
<span class="nc" id="L70">                    logger.error(&quot;GraphQL execution failed - Operation: {}, Correlation ID: {}, Time: {}ms&quot;,</span>
<span class="nc" id="L71">                            operationName, correlationId, executionTime, t);</span>
                } else {
<span class="nc" id="L73">                    logger.debug(&quot;GraphQL execution completed - Operation: {}, Correlation ID: {}, Time: {}ms&quot;,</span>
<span class="nc" id="L74">                            operationName, correlationId, executionTime);</span>
                }
<span class="nc" id="L76">            }</span>

            @Override
            public void onDispatched() {
                // Restore MDC context for dispatch logging
<span class="nc bnc" id="L81" title="All 4 branches missed.">                if (mdcContext != null &amp;&amp; !mdcContext.isEmpty()) {</span>
<span class="nc" id="L82">                    MDC.setContextMap(mdcContext);</span>
                }
<span class="nc" id="L84">                logger.debug(&quot;GraphQL execution dispatched - Operation: {}, Correlation ID: {}&quot;,</span>
                        operationName, correlationId);
<span class="nc" id="L86">            }</span>
        };
    }

    @Override
    public InstrumentationContext&lt;Object&gt; beginFieldFetch(
            InstrumentationFieldFetchParameters parameters,
            InstrumentationState state) {

        // Capture the current MDC context to ensure it's available throughout field resolution
<span class="nc" id="L96">        Map&lt;String, String&gt; initialMdcContext = MDC.getCopyOfContextMap();</span>
<span class="nc bnc" id="L97" title="All 2 branches missed.">        String initialCorrelationId = initialMdcContext != null ? initialMdcContext.get(&quot;correlationId&quot;) : null;</span>

        // If no correlation ID in MDC, try to get from request context
        final String correlationId;
        final Map&lt;String, String&gt; mdcContext;
<span class="nc bnc" id="L102" title="All 4 branches missed.">        if (initialCorrelationId == null || initialCorrelationId.isEmpty()) {</span>
<span class="nc" id="L103">            String requestCorrelationId = getCorrelationIdFromRequest();</span>
<span class="nc bnc" id="L104" title="All 4 branches missed.">            if (requestCorrelationId != null &amp;&amp; !requestCorrelationId.isEmpty()) {</span>
<span class="nc" id="L105">                MDC.put(&quot;correlationId&quot;, requestCorrelationId);</span>
<span class="nc" id="L106">                correlationId = requestCorrelationId;</span>
                // Update the captured context
<span class="nc bnc" id="L108" title="All 2 branches missed.">                if (initialMdcContext == null) {</span>
<span class="nc" id="L109">                    mdcContext = new HashMap&lt;&gt;();</span>
                } else {
<span class="nc" id="L111">                    mdcContext = new HashMap&lt;&gt;(initialMdcContext);</span>
                }
<span class="nc" id="L113">                mdcContext.put(&quot;correlationId&quot;, requestCorrelationId);</span>
            } else {
<span class="nc" id="L115">                correlationId = initialCorrelationId;</span>
<span class="nc" id="L116">                mdcContext = initialMdcContext;</span>
            }
<span class="nc" id="L118">        } else {</span>
<span class="nc" id="L119">            correlationId = initialCorrelationId;</span>
<span class="nc" id="L120">            mdcContext = initialMdcContext;</span>
        }

<span class="nc" id="L123">        final String fieldName = parameters.getField().getName();</span>
<span class="nc" id="L124">        String tempParentType = &quot;Unknown&quot;;</span>
        try {
            // Try to get parent type name safely
<span class="nc bnc" id="L127" title="All 2 branches missed.">            if (parameters.getExecutionStepInfo() != null &amp;&amp;</span>
<span class="nc bnc" id="L128" title="All 2 branches missed.">                parameters.getExecutionStepInfo().getParent() != null) {</span>
<span class="nc" id="L129">                tempParentType = parameters.getExecutionStepInfo().getParent().getType().toString();</span>
            }
<span class="nc" id="L131">        } catch (Exception e) {</span>
<span class="nc" id="L132">            logger.trace(&quot;Could not determine parent type for field: {}&quot;, fieldName);</span>
<span class="nc" id="L133">        }</span>
<span class="nc" id="L134">        final String parentType = tempParentType;</span>

<span class="nc" id="L136">        logger.trace(&quot;Starting field fetch - Field: {}.{}, Correlation ID: {}&quot;,</span>
                parentType, fieldName, correlationId);

        // CRITICAL: Ensure MDC context is set for the entire field resolution
<span class="nc bnc" id="L140" title="All 4 branches missed.">        if (correlationId != null &amp;&amp; !correlationId.isEmpty()) {</span>
<span class="nc" id="L141">            MDC.put(&quot;correlationId&quot;, correlationId);</span>
        }

<span class="nc" id="L144">        return new InstrumentationContext&lt;Object&gt;() {</span>
            @Override
            public void onCompleted(Object result, Throwable t) {
                // CRITICAL: Restore MDC context for completion logging and ensure it's available for downstream services
<span class="nc bnc" id="L148" title="All 4 branches missed.">                if (correlationId != null &amp;&amp; !correlationId.isEmpty()) {</span>
<span class="nc" id="L149">                    MDC.put(&quot;correlationId&quot;, correlationId);</span>
                }
<span class="nc bnc" id="L151" title="All 4 branches missed.">                if (mdcContext != null &amp;&amp; !mdcContext.isEmpty()) {</span>
<span class="nc" id="L152">                    MDC.setContextMap(mdcContext);</span>
                }

<span class="nc bnc" id="L155" title="All 2 branches missed.">                if (t != null) {</span>
<span class="nc" id="L156">                    logger.trace(&quot;Field fetch failed - Field: {}.{}, Correlation ID: {}, Error: {}&quot;,</span>
<span class="nc" id="L157">                            parentType, fieldName, correlationId, t.getMessage());</span>
                } else {
<span class="nc" id="L159">                    logger.trace(&quot;Field fetch completed - Field: {}.{}, Correlation ID: {}&quot;,</span>
                            parentType, fieldName, correlationId);
                }
<span class="nc" id="L162">            }</span>

            @Override
            public void onDispatched() {
                // CRITICAL: Restore MDC context for dispatch logging and ensure it's available for downstream services
<span class="nc bnc" id="L167" title="All 4 branches missed.">                if (correlationId != null &amp;&amp; !correlationId.isEmpty()) {</span>
<span class="nc" id="L168">                    MDC.put(&quot;correlationId&quot;, correlationId);</span>
                }
<span class="nc bnc" id="L170" title="All 4 branches missed.">                if (mdcContext != null &amp;&amp; !mdcContext.isEmpty()) {</span>
<span class="nc" id="L171">                    MDC.setContextMap(mdcContext);</span>
                }
<span class="nc" id="L173">                logger.trace(&quot;Field fetch dispatched - Field: {}.{}, Correlation ID: {}&quot;,</span>
                        parentType, fieldName, correlationId);
<span class="nc" id="L175">            }</span>
        };
    }

    /**
     * Add GraphQL-specific response headers.
     */
    private void addGraphQLResponseHeaders(String operationName, long executionTime, String correlationId) {
        try {
            ServletRequestAttributes requestAttributes = 
<span class="nc" id="L185">                    (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();</span>
            
<span class="nc bnc" id="L187" title="All 2 branches missed.">            if (requestAttributes != null) {</span>
<span class="nc" id="L188">                HttpServletResponse response = requestAttributes.getResponse();</span>
<span class="nc bnc" id="L189" title="All 2 branches missed.">                if (response != null) {</span>
                    // Add GraphQL operation name
<span class="nc bnc" id="L191" title="All 2 branches missed.">                    if (operationName != null) {</span>
<span class="nc" id="L192">                        response.setHeader(RESPONSE_HEADER_GRAPHQL_OPERATION, operationName);</span>
                    }
                    
                    // Add execution time for performance monitoring
<span class="nc" id="L196">                    response.setHeader(RESPONSE_HEADER_EXECUTION_TIME, String.valueOf(executionTime));</span>
                    
                    // Ensure correlation ID is in response (redundant with filter, but ensures consistency)
<span class="nc bnc" id="L199" title="All 2 branches missed.">                    if (correlationId != null) {</span>
<span class="nc" id="L200">                        response.setHeader(correlationIdHeaderName, correlationId);</span>
                    }

                    // Add service metadata headers (ensure they're present for GraphQL responses)
<span class="nc" id="L204">                    response.setHeader(&quot;X-Service-Name&quot;, &quot;dms-service&quot;);</span>
<span class="nc" id="L205">                    response.setHeader(&quot;X-Service-Version&quot;, &quot;1.0.0&quot;);</span>
                    
<span class="nc" id="L207">                    logger.debug(&quot;Added GraphQL response headers - Operation: {}, Time: {}ms, Correlation ID: {}&quot;, </span>
<span class="nc" id="L208">                            operationName, executionTime, correlationId);</span>
                }
            }
<span class="nc" id="L211">        } catch (Exception e) {</span>
<span class="nc" id="L212">            logger.warn(&quot;Failed to add GraphQL response headers&quot;, e);</span>
<span class="nc" id="L213">        }</span>
<span class="nc" id="L214">    }</span>

    /**
     * Get correlation ID from request header if MDC is empty.
     */
    private String getCorrelationIdFromRequest() {
        try {
            ServletRequestAttributes requestAttributes =
<span class="nc" id="L222">                    (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();</span>

<span class="nc bnc" id="L224" title="All 2 branches missed.">            if (requestAttributes != null) {</span>
<span class="nc" id="L225">                return requestAttributes.getRequest().getHeader(correlationIdHeaderName);</span>
            }
<span class="nc" id="L227">        } catch (Exception e) {</span>
<span class="nc" id="L228">            logger.debug(&quot;Could not get correlation ID from request&quot;, e);</span>
<span class="nc" id="L229">        }</span>
<span class="nc" id="L230">        return null;</span>
    }

    /**
     * Extract operation name from GraphQL query string.
     * This is a simple implementation that looks for common patterns.
     */
    private String extractOperationName(String query) {
<span class="nc bnc" id="L238" title="All 4 branches missed.">        if (query == null || query.trim().isEmpty()) {</span>
<span class="nc" id="L239">            return &quot;anonymous&quot;;</span>
        }

        try {
            // Simple regex to extract operation name from query
            // This handles basic cases like &quot;query GetUser&quot; or &quot;mutation CreateUser&quot;
<span class="nc" id="L245">            String trimmed = query.trim();</span>
<span class="nc bnc" id="L246" title="All 6 branches missed.">            if (trimmed.startsWith(&quot;query &quot;) || trimmed.startsWith(&quot;mutation &quot;) || trimmed.startsWith(&quot;subscription &quot;)) {</span>
<span class="nc" id="L247">                String[] parts = trimmed.split(&quot;\\s+&quot;);</span>
<span class="nc bnc" id="L248" title="All 2 branches missed.">                if (parts.length &gt; 1) {</span>
<span class="nc" id="L249">                    String operationName = parts[1];</span>
                    // Remove any opening braces or parentheses
<span class="nc" id="L251">                    operationName = operationName.replaceAll(&quot;[({].*&quot;, &quot;&quot;);</span>
<span class="nc bnc" id="L252" title="All 2 branches missed.">                    return operationName.isEmpty() ? &quot;anonymous&quot; : operationName;</span>
                }
            }

            // If no explicit operation name, return the operation type
<span class="nc bnc" id="L257" title="All 2 branches missed.">            if (trimmed.startsWith(&quot;query&quot;)) return &quot;query&quot;;</span>
<span class="nc bnc" id="L258" title="All 2 branches missed.">            if (trimmed.startsWith(&quot;mutation&quot;)) return &quot;mutation&quot;;</span>
<span class="nc bnc" id="L259" title="All 2 branches missed.">            if (trimmed.startsWith(&quot;subscription&quot;)) return &quot;subscription&quot;;</span>

<span class="nc" id="L261">            return &quot;anonymous&quot;;</span>
<span class="nc" id="L262">        } catch (Exception e) {</span>
<span class="nc" id="L263">            logger.debug(&quot;Could not extract operation name from query&quot;, e);</span>
<span class="nc" id="L264">            return &quot;anonymous&quot;;</span>
        }
    }
}

</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>