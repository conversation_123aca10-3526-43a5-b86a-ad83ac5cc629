<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>AwsS3Config.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.config</a> &gt; <span class="el_source">AwsS3Config.java</span></div><h1>AwsS3Config.java</h1><pre class="source lang-java linenums">package com.ascentbusiness.dms_svc.config;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.S3ClientBuilder;

import java.net.URI;

<span class="nc" id="L16">@Slf4j</span>
@Configuration
<span class="nc" id="L18">@RequiredArgsConstructor</span>
public class AwsS3Config {

    private final StorageConfigurationProperties storageConfig;

    @Bean
    @ConditionalOnProperty(name = &quot;dms.storage.provider&quot;, havingValue = &quot;S3&quot;)
    public S3Client s3Client() {
<span class="nc" id="L26">        log.info(&quot;Configuring S3 client for bucket: {}&quot;, storageConfig.getS3().getBucketName());</span>
        
<span class="nc" id="L28">        validateS3Configuration();</span>
        
<span class="nc" id="L30">        S3ClientBuilder builder = S3Client.builder()</span>
<span class="nc" id="L31">                .region(Region.of(storageConfig.getS3().getRegion()))</span>
<span class="nc" id="L32">                .credentialsProvider(StaticCredentialsProvider.create(</span>
<span class="nc" id="L33">                        AwsBasicCredentials.create(</span>
<span class="nc" id="L34">                                storageConfig.getS3().getAccessKey(),</span>
<span class="nc" id="L35">                                storageConfig.getS3().getSecretKey()</span>
                        )
                ));

        // Configure custom endpoint if provided (for localstack testing)
<span class="nc bnc" id="L40" title="All 4 branches missed.">        if (storageConfig.getS3().getEndpoint() != null &amp;&amp; !storageConfig.getS3().getEndpoint().isEmpty()) {</span>
<span class="nc" id="L41">            log.info(&quot;Using custom S3 endpoint: {}&quot;, storageConfig.getS3().getEndpoint());</span>
<span class="nc" id="L42">            builder.endpointOverride(URI.create(storageConfig.getS3().getEndpoint()));</span>
        }

<span class="nc" id="L45">        S3Client client = builder.build();</span>
<span class="nc" id="L46">        log.info(&quot;S3 client configured successfully for region: {}&quot;, storageConfig.getS3().getRegion());</span>
        
<span class="nc" id="L48">        return client;</span>
    }

    private void validateS3Configuration() {
<span class="nc" id="L52">        StorageConfigurationProperties.S3 s3Config = storageConfig.getS3();</span>
        
<span class="nc bnc" id="L54" title="All 4 branches missed.">        if (s3Config.getAccessKey() == null || s3Config.getAccessKey().isEmpty()) {</span>
<span class="nc" id="L55">            throw new IllegalArgumentException(&quot;AWS access key is required when using S3 storage provider&quot;);</span>
        }
        
<span class="nc bnc" id="L58" title="All 4 branches missed.">        if (s3Config.getSecretKey() == null || s3Config.getSecretKey().isEmpty()) {</span>
<span class="nc" id="L59">            throw new IllegalArgumentException(&quot;AWS secret key is required when using S3 storage provider&quot;);</span>
        }
        
<span class="nc bnc" id="L62" title="All 4 branches missed.">        if (s3Config.getBucketName() == null || s3Config.getBucketName().isEmpty()) {</span>
<span class="nc" id="L63">            throw new IllegalArgumentException(&quot;S3 bucket name is required when using S3 storage provider&quot;);</span>
        }
        
<span class="nc bnc" id="L66" title="All 4 branches missed.">        if (s3Config.getRegion() == null || s3Config.getRegion().isEmpty()) {</span>
<span class="nc" id="L67">            throw new IllegalArgumentException(&quot;AWS region is required when using S3 storage provider&quot;);</span>
        }
        
<span class="nc" id="L70">        log.info(&quot;S3 configuration validation passed&quot;);</span>
<span class="nc" id="L71">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>