<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>DmsValidationException.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.exception</a> &gt; <span class="el_source">DmsValidationException.java</span></div><h1>DmsValidationException.java</h1><pre class="source lang-java linenums">package com.ascentbusiness.dms_svc.exception;

import java.util.Map;

/**
 * Exception for validation errors in the DMS system.
 * Used for input validation, business rule validation, and data validation errors.
 */
public class DmsValidationException extends DmsException {

    public DmsValidationException(String message) {
<span class="nc" id="L12">        super(message, &quot;VALIDATION_ERROR&quot;, ErrorCategory.VALIDATION, ErrorSeverity.MEDIUM);</span>
<span class="nc" id="L13">    }</span>

    public DmsValidationException(String message, String errorCode) {
<span class="nc" id="L16">        super(message, errorCode, ErrorCategory.VALIDATION, ErrorSeverity.MEDIUM);</span>
<span class="nc" id="L17">    }</span>

    public DmsValidationException(String message, String errorCode, Map&lt;String, Object&gt; errorDetails) {
<span class="nc" id="L20">        super(message, errorCode, ErrorCategory.VALIDATION, ErrorSeverity.MEDIUM, errorDetails);</span>
<span class="nc" id="L21">    }</span>

    public DmsValidationException(String message, String errorCode, Throwable cause) {
<span class="nc" id="L24">        super(message, errorCode, ErrorCategory.VALIDATION, ErrorSeverity.MEDIUM, cause);</span>
<span class="nc" id="L25">    }</span>

    public DmsValidationException(String message, String errorCode, Map&lt;String, Object&gt; errorDetails, Throwable cause) {
<span class="nc" id="L28">        super(message, errorCode, ErrorCategory.VALIDATION, ErrorSeverity.MEDIUM, errorDetails, cause);</span>
<span class="nc" id="L29">    }</span>

    // Specific validation error types
    public static DmsValidationException invalidInput(String fieldName, Object value) {
<span class="nc" id="L33">        return new DmsValidationException(</span>
<span class="nc" id="L34">            String.format(&quot;Invalid input for field '%s': %s&quot;, fieldName, value),</span>
            &quot;INVALID_INPUT&quot;,
<span class="nc bnc" id="L36" title="All 2 branches missed.">            Map.of(&quot;field&quot;, fieldName, &quot;value&quot;, value != null ? value.toString() : &quot;null&quot;)</span>
        );
    }

    public static DmsValidationException requiredField(String fieldName) {
<span class="nc" id="L41">        return new DmsValidationException(</span>
<span class="nc" id="L42">            String.format(&quot;Required field '%s' is missing or empty&quot;, fieldName),</span>
            &quot;REQUIRED_FIELD_MISSING&quot;,
<span class="nc" id="L44">            Map.of(&quot;field&quot;, fieldName)</span>
        );
    }

    public static DmsValidationException invalidFormat(String fieldName, String expectedFormat) {
<span class="nc" id="L49">        return new DmsValidationException(</span>
<span class="nc" id="L50">            String.format(&quot;Field '%s' has invalid format. Expected: %s&quot;, fieldName, expectedFormat),</span>
            &quot;INVALID_FORMAT&quot;,
<span class="nc" id="L52">            Map.of(&quot;field&quot;, fieldName, &quot;expectedFormat&quot;, expectedFormat)</span>
        );
    }

    public static DmsValidationException valueOutOfRange(String fieldName, Object value, Object min, Object max) {
<span class="nc" id="L57">        return new DmsValidationException(</span>
<span class="nc" id="L58">            String.format(&quot;Field '%s' value %s is out of range [%s, %s]&quot;, fieldName, value, min, max),</span>
            &quot;VALUE_OUT_OF_RANGE&quot;,
<span class="nc" id="L60">            Map.of(&quot;field&quot;, fieldName, &quot;value&quot;, value, &quot;min&quot;, min, &quot;max&quot;, max)</span>
        );
    }

    public static DmsValidationException duplicateValue(String fieldName, Object value) {
<span class="nc" id="L65">        return new DmsValidationException(</span>
<span class="nc" id="L66">            String.format(&quot;Duplicate value for field '%s': %s&quot;, fieldName, value),</span>
            &quot;DUPLICATE_VALUE&quot;,
<span class="nc" id="L68">            Map.of(&quot;field&quot;, fieldName, &quot;value&quot;, value)</span>
        );
    }

    public static DmsValidationException invalidFileType(String fileName, String actualType, String[] allowedTypes) {
<span class="nc" id="L73">        return new DmsValidationException(</span>
<span class="nc" id="L74">            String.format(&quot;File '%s' has invalid type '%s'. Allowed types: %s&quot;, </span>
<span class="nc" id="L75">                         fileName, actualType, String.join(&quot;, &quot;, allowedTypes)),</span>
            &quot;INVALID_FILE_TYPE&quot;,
<span class="nc" id="L77">            Map.of(&quot;fileName&quot;, fileName, &quot;actualType&quot;, actualType, &quot;allowedTypes&quot;, allowedTypes)</span>
        );
    }

    public static DmsValidationException fileSizeExceeded(String fileName, long actualSize, long maxSize) {
<span class="nc" id="L82">        return new DmsValidationException(</span>
<span class="nc" id="L83">            String.format(&quot;File '%s' size %d bytes exceeds maximum allowed size %d bytes&quot;, </span>
<span class="nc" id="L84">                         fileName, actualSize, maxSize),</span>
            &quot;FILE_SIZE_EXCEEDED&quot;,
<span class="nc" id="L86">            Map.of(&quot;fileName&quot;, fileName, &quot;actualSize&quot;, actualSize, &quot;maxSize&quot;, maxSize)</span>
        );
    }

    public static DmsValidationException invalidState(String entityType, String currentState, String requiredState) {
<span class="nc" id="L91">        return new DmsValidationException(</span>
<span class="nc" id="L92">            String.format(&quot;%s is in state '%s' but operation requires state '%s'&quot;, </span>
                         entityType, currentState, requiredState),
            &quot;INVALID_STATE&quot;,
<span class="nc" id="L95">            Map.of(&quot;entityType&quot;, entityType, &quot;currentState&quot;, currentState, &quot;requiredState&quot;, requiredState)</span>
        );
    }

    public static DmsValidationException businessRuleViolation(String ruleName, String description) {
<span class="nc" id="L100">        return new DmsValidationException(</span>
<span class="nc" id="L101">            String.format(&quot;Business rule violation: %s - %s&quot;, ruleName, description),</span>
            &quot;BUSINESS_RULE_VIOLATION&quot;,
<span class="nc" id="L103">            Map.of(&quot;ruleName&quot;, ruleName, &quot;description&quot;, description)</span>
        );
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>