<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>JwtAuthenticationFilter.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.security</a> &gt; <span class="el_source">JwtAuthenticationFilter.java</span></div><h1>JwtAuthenticationFilter.java</h1><pre class="source lang-java linenums">package com.ascentbusiness.dms_svc.security;

import com.ascentbusiness.dms_svc.enums.SecurityViolationType;
import com.ascentbusiness.dms_svc.enums.ViolationSeverity;
import com.ascentbusiness.dms_svc.exception.InvalidTokenException;
import com.ascentbusiness.dms_svc.service.SecurityService;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.lang.NonNull;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.util.Collections;
import java.util.List;

<span class="nc" id="L27">public class JwtAuthenticationFilter extends OncePerRequestFilter {</span>

    @Autowired
    private JwtTokenProvider tokenProvider;

    @Autowired
    @Lazy
    private SecurityService securityService;

    @Value(&quot;${dms.jwt.header}&quot;)
    private String tokenHeader;

    @Value(&quot;${dms.jwt.prefix}&quot;)
    private String tokenPrefix;

<span class="nc" id="L42">    private static final Logger logger = LoggerFactory.getLogger(JwtAuthenticationFilter.class);</span>

    @Override
    protected void doFilterInternal(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response, @NonNull FilterChain filterChain)
            throws ServletException, IOException {
<span class="nc" id="L47">        String requestURI = request.getRequestURI();</span>
<span class="nc" id="L48">        logger.info(&quot;JWT Authentication Filter - Processing request: {}&quot;, requestURI);</span>

        // Skip JWT processing for publicly accessible paths
<span class="nc bnc" id="L51" title="All 2 branches missed.">        if (shouldSkipJwtProcessing(requestURI)) {</span>
<span class="nc" id="L52">            logger.info(&quot;JWT Authentication Filter - Skipping JWT processing for public path: {}&quot;, requestURI);</span>
<span class="nc" id="L53">            filterChain.doFilter(request, response);</span>
<span class="nc" id="L54">            return;</span>
        }

        try {
<span class="nc" id="L58">            String jwt = getJwtFromRequest(request);</span>
<span class="nc" id="L59">            logger.info(&quot;JWT Authentication Filter - Request URI: {}, JWT present: {}&quot;, requestURI, StringUtils.hasText(jwt));</span>

<span class="nc bnc" id="L61" title="All 4 branches missed.">            if (StringUtils.hasText(jwt) &amp;&amp; tokenProvider.validateToken(jwt)) {</span>
<span class="nc" id="L62">                logger.info(&quot;JWT Authentication Filter - Valid JWT token found&quot;);</span>
<span class="nc" id="L63">                String username = tokenProvider.getUsernameFromToken(jwt);</span>
<span class="nc" id="L64">                List&lt;String&gt; roles = tokenProvider.getRolesFromToken(jwt);</span>
<span class="nc" id="L65">                List&lt;String&gt; permissions = tokenProvider.getPermissionsFromToken(jwt);</span>

                // Ensure roles and permissions are not null
<span class="nc bnc" id="L68" title="All 2 branches missed.">                if (roles == null) {</span>
<span class="nc" id="L69">                    roles = Collections.emptyList();</span>
                }
<span class="nc bnc" id="L71" title="All 2 branches missed.">                if (permissions == null) {</span>
<span class="nc" id="L72">                    permissions = Collections.emptyList();</span>
                }

                // Create UserPrincipal with username, roles, and permissions
<span class="nc" id="L76">                UserPrincipal userPrincipal = UserPrincipal.create(username, roles, permissions);</span>
<span class="nc" id="L77">                UsernamePasswordAuthenticationToken authentication = </span>
<span class="nc" id="L78">                    new UsernamePasswordAuthenticationToken(userPrincipal, null, userPrincipal.getAuthorities());</span>
<span class="nc" id="L79">                authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));</span>

<span class="nc" id="L81">                SecurityContextHolder.getContext().setAuthentication(authentication);</span>
<span class="nc" id="L82">                logger.info(&quot;JWT Authentication Filter - Authentication set in SecurityContext for user: {}&quot;, username);</span>
<span class="nc" id="L83">                logger.debug(&quot;Set authentication for user: {} with roles: {} and permissions: {}&quot;, username, roles, permissions);</span>
<span class="nc" id="L84">            } else {</span>
<span class="nc" id="L85">                logger.info(&quot;JWT Authentication Filter - No valid JWT token found&quot;);</span>
            }
<span class="nc" id="L87">        } catch (InvalidTokenException ex) {</span>
            // Log security violation for invalid/expired tokens
<span class="nc" id="L89">            String jwt = getJwtFromRequest(request);</span>
<span class="nc" id="L90">            String username = null;</span>
            try {
<span class="nc bnc" id="L92" title="All 2 branches missed.">                if (StringUtils.hasText(jwt)) {</span>
<span class="nc" id="L93">                    username = tokenProvider.getUsernameFromToken(jwt);</span>
                }
<span class="nc" id="L95">            } catch (Exception ignored) {</span>
                // Username extraction may fail for invalid tokens
<span class="nc" id="L97">            }</span>
            
<span class="nc" id="L99">            securityService.logSecurityViolation(</span>
<span class="nc bnc" id="L100" title="All 2 branches missed.">                username != null ? username : &quot;unknown&quot;,</span>
                SecurityViolationType.TOKEN_EXPIRED,
                ViolationSeverity.HIGH,
                null,
                &quot;JWT_AUTHENTICATION&quot;,
<span class="nc" id="L105">                String.format(&quot;JWT token validation failed: %s&quot;, ex.getMessage())</span>
            );
            
            // Re-throw InvalidTokenException to allow proper error handling at GraphQL layer
<span class="nc" id="L109">            logger.warn(&quot;JWT token validation failed: {}&quot;, ex.getMessage());</span>
            // Store the exception in request attribute so it can be accessed by GraphQL resolver
<span class="nc" id="L111">            request.setAttribute(&quot;JWT_VALIDATION_ERROR&quot;, ex);</span>
<span class="nc" id="L112">        } catch (Exception ex) {</span>
            // Log security violation for authentication errors
<span class="nc" id="L114">            securityService.logSecurityViolation(</span>
                &quot;unknown&quot;,
                SecurityViolationType.INVALID_ACCESS,
                ViolationSeverity.MEDIUM,
                null,
                &quot;JWT_AUTHENTICATION&quot;,
<span class="nc" id="L120">                String.format(&quot;Authentication error: %s&quot;, ex.getMessage())</span>
            );
            
<span class="nc" id="L123">            logger.error(&quot;Could not set user authentication in security context&quot;, ex);</span>
<span class="nc" id="L124">        }</span>

<span class="nc" id="L126">        filterChain.doFilter(request, response);</span>
<span class="nc" id="L127">    }</span>

    /**
     * Check if JWT processing should be skipped for the given request URI.
     * This allows certain paths to be publicly accessible without JWT authentication.
     */
    private boolean shouldSkipJwtProcessing(String requestURI) {
        // Skip JWT processing for GraphiQL interface
<span class="nc bnc" id="L135" title="All 2 branches missed.">        if (requestURI.startsWith(&quot;/graphiql&quot;)) {</span>
<span class="nc" id="L136">            return true;</span>
        }

        // Skip JWT processing for actuator endpoints
<span class="nc bnc" id="L140" title="All 2 branches missed.">        if (requestURI.startsWith(&quot;/actuator&quot;)) {</span>
<span class="nc" id="L141">            return true;</span>
        }

        // Skip JWT processing for H2 console (development only)
<span class="nc bnc" id="L145" title="All 2 branches missed.">        if (requestURI.startsWith(&quot;/h2-console&quot;)) {</span>
<span class="nc" id="L146">            return true;</span>
        }

        // Skip JWT processing for static resources and common browser requests
<span class="nc bnc" id="L150" title="All 2 branches missed.">        if (requestURI.startsWith(&quot;/static&quot;) ||</span>
<span class="nc bnc" id="L151" title="All 2 branches missed.">            requestURI.startsWith(&quot;/css&quot;) ||</span>
<span class="nc bnc" id="L152" title="All 2 branches missed.">            requestURI.startsWith(&quot;/js&quot;) ||</span>
<span class="nc bnc" id="L153" title="All 2 branches missed.">            requestURI.startsWith(&quot;/images&quot;) ||</span>
<span class="nc bnc" id="L154" title="All 2 branches missed.">            requestURI.equals(&quot;/favicon.ico&quot;)) {</span>
<span class="nc" id="L155">            return true;</span>
        }

        // Skip JWT processing only for specific API endpoints that are configured as permitAll
        // Note: Download endpoints require authentication, so they should NOT be skipped
        // Add specific public API endpoints here if needed in the future
        // if (requestURI.startsWith(&quot;/api/public&quot;)) {
        //     return true;
        // }

<span class="nc" id="L165">        return false;</span>
    }

    private String getJwtFromRequest(HttpServletRequest request) {
<span class="nc" id="L169">        String bearerToken = request.getHeader(tokenHeader);</span>
<span class="nc bnc" id="L170" title="All 4 branches missed.">        if (StringUtils.hasText(bearerToken) &amp;&amp; bearerToken.startsWith(tokenPrefix)) {</span>
<span class="nc" id="L171">            return bearerToken.substring(tokenPrefix.length()).trim();</span>
        }
<span class="nc" id="L173">        return null;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>