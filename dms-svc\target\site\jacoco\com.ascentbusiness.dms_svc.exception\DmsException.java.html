<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>DmsException.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.exception</a> &gt; <span class="el_source">DmsException.java</span></div><h1>DmsException.java</h1><pre class="source lang-java linenums">package com.ascentbusiness.dms_svc.exception;

import lombok.Getter;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.UUID;

/**
 * Base exception class for all DMS-specific exceptions.
 * Provides common functionality including correlation IDs, error codes,
 * and structured error information for better debugging and monitoring.
 */
@Getter
public abstract class DmsException extends RuntimeException {

<span class="fc" id="L17">    private final String errorCode;</span>
<span class="nc" id="L18">    private final String correlationId;</span>
<span class="nc" id="L19">    private final LocalDateTime timestamp;</span>
<span class="nc" id="L20">    private final Map&lt;String, Object&gt; errorDetails;</span>
<span class="nc" id="L21">    private final ErrorCategory category;</span>
<span class="nc" id="L22">    private final ErrorSeverity severity;</span>

    /**
     * Error categories for classification
     */
<span class="fc" id="L27">    public enum ErrorCategory {</span>
<span class="fc" id="L28">        VALIDATION,</span>
<span class="fc" id="L29">        AUTHENTICATION,</span>
<span class="fc" id="L30">        AUTHORIZATION,</span>
<span class="fc" id="L31">        BUSINESS_RULE,</span>
<span class="fc" id="L32">        SYSTEM,</span>
<span class="fc" id="L33">        EXTERNAL_SERVICE,</span>
<span class="fc" id="L34">        DATA_INTEGRITY</span>
    }

    /**
     * Error severity levels
     */
<span class="fc" id="L40">    public enum ErrorSeverity {</span>
<span class="fc" id="L41">        LOW,</span>
<span class="fc" id="L42">        MEDIUM,</span>
<span class="fc" id="L43">        HIGH,</span>
<span class="fc" id="L44">        CRITICAL</span>
    }

    protected DmsException(String message, String errorCode, ErrorCategory category, ErrorSeverity severity) {
<span class="fc" id="L48">        super(message);</span>
<span class="fc" id="L49">        this.errorCode = errorCode;</span>
<span class="fc" id="L50">        this.category = category;</span>
<span class="fc" id="L51">        this.severity = severity;</span>
<span class="fc" id="L52">        this.correlationId = UUID.randomUUID().toString();</span>
<span class="fc" id="L53">        this.timestamp = LocalDateTime.now();</span>
<span class="fc" id="L54">        this.errorDetails = Map.of();</span>
<span class="fc" id="L55">    }</span>

    protected DmsException(String message, String errorCode, ErrorCategory category, ErrorSeverity severity, 
                          Throwable cause) {
<span class="nc" id="L59">        super(message, cause);</span>
<span class="nc" id="L60">        this.errorCode = errorCode;</span>
<span class="nc" id="L61">        this.category = category;</span>
<span class="nc" id="L62">        this.severity = severity;</span>
<span class="nc" id="L63">        this.correlationId = UUID.randomUUID().toString();</span>
<span class="nc" id="L64">        this.timestamp = LocalDateTime.now();</span>
<span class="nc" id="L65">        this.errorDetails = Map.of();</span>
<span class="nc" id="L66">    }</span>

    protected DmsException(String message, String errorCode, ErrorCategory category, ErrorSeverity severity,
                          Map&lt;String, Object&gt; errorDetails) {
<span class="fc" id="L70">        super(message);</span>
<span class="fc" id="L71">        this.errorCode = errorCode;</span>
<span class="fc" id="L72">        this.category = category;</span>
<span class="fc" id="L73">        this.severity = severity;</span>
<span class="fc" id="L74">        this.correlationId = UUID.randomUUID().toString();</span>
<span class="fc" id="L75">        this.timestamp = LocalDateTime.now();</span>
<span class="pc bpc" id="L76" title="1 of 2 branches missed.">        this.errorDetails = errorDetails != null ? Map.copyOf(errorDetails) : Map.of();</span>
<span class="fc" id="L77">    }</span>

    protected DmsException(String message, String errorCode, ErrorCategory category, ErrorSeverity severity,
                          Map&lt;String, Object&gt; errorDetails, Throwable cause) {
<span class="nc" id="L81">        super(message, cause);</span>
<span class="nc" id="L82">        this.errorCode = errorCode;</span>
<span class="nc" id="L83">        this.category = category;</span>
<span class="nc" id="L84">        this.severity = severity;</span>
<span class="nc" id="L85">        this.correlationId = UUID.randomUUID().toString();</span>
<span class="nc" id="L86">        this.timestamp = LocalDateTime.now();</span>
<span class="nc bnc" id="L87" title="All 2 branches missed.">        this.errorDetails = errorDetails != null ? Map.copyOf(errorDetails) : Map.of();</span>
<span class="nc" id="L88">    }</span>

    protected DmsException(String message, String errorCode, ErrorCategory category, ErrorSeverity severity,
                          String correlationId) {
<span class="nc" id="L92">        super(message);</span>
<span class="nc" id="L93">        this.errorCode = errorCode;</span>
<span class="nc" id="L94">        this.category = category;</span>
<span class="nc" id="L95">        this.severity = severity;</span>
<span class="nc bnc" id="L96" title="All 2 branches missed.">        this.correlationId = correlationId != null ? correlationId : UUID.randomUUID().toString();</span>
<span class="nc" id="L97">        this.timestamp = LocalDateTime.now();</span>
<span class="nc" id="L98">        this.errorDetails = Map.of();</span>
<span class="nc" id="L99">    }</span>

    /**
     * Get a formatted error message including correlation ID
     */
    public String getFormattedMessage() {
<span class="nc" id="L105">        return String.format(&quot;[%s] %s (Error Code: %s, Correlation ID: %s)&quot;, </span>
<span class="nc" id="L106">                           category, getMessage(), errorCode, correlationId);</span>
    }

    /**
     * Get error details as a formatted string for logging
     */
    public String getErrorDetailsString() {
<span class="nc bnc" id="L113" title="All 2 branches missed.">        if (errorDetails.isEmpty()) {</span>
<span class="nc" id="L114">            return &quot;No additional details&quot;;</span>
        }
        
<span class="nc" id="L117">        StringBuilder sb = new StringBuilder();</span>
<span class="nc" id="L118">        errorDetails.forEach((key, value) -&gt; </span>
<span class="nc" id="L119">            sb.append(key).append(&quot;=&quot;).append(value).append(&quot;, &quot;));</span>
        
        // Remove trailing comma and space
<span class="nc bnc" id="L122" title="All 2 branches missed.">        if (sb.length() &gt; 2) {</span>
<span class="nc" id="L123">            sb.setLength(sb.length() - 2);</span>
        }
        
<span class="nc" id="L126">        return sb.toString();</span>
    }

    /**
     * Check if this exception should be logged with stack trace
     */
    public boolean shouldLogStackTrace() {
<span class="nc bnc" id="L133" title="All 8 branches missed.">        return severity == ErrorSeverity.HIGH || severity == ErrorSeverity.CRITICAL ||</span>
               category == ErrorCategory.SYSTEM || category == ErrorCategory.EXTERNAL_SERVICE;
    }

    /**
     * Get the appropriate log level for this exception
     */
    public String getLogLevel() {
<span class="nc bnc" id="L141" title="All 4 branches missed.">        return switch (severity) {</span>
<span class="nc" id="L142">            case LOW -&gt; &quot;INFO&quot;;</span>
<span class="nc" id="L143">            case MEDIUM -&gt; &quot;WARN&quot;;</span>
<span class="nc" id="L144">            case HIGH, CRITICAL -&gt; &quot;ERROR&quot;;</span>
        };
    }

    /**
     * Create a map representation of this exception for structured logging
     */
    public Map&lt;String, Object&gt; toLogMap() {
<span class="nc" id="L152">        return Map.of(</span>
            &quot;errorCode&quot;, errorCode,
            &quot;correlationId&quot;, correlationId,
<span class="nc" id="L155">            &quot;timestamp&quot;, timestamp.toString(),</span>
<span class="nc" id="L156">            &quot;category&quot;, category.toString(),</span>
<span class="nc" id="L157">            &quot;severity&quot;, severity.toString(),</span>
<span class="nc" id="L158">            &quot;message&quot;, getMessage(),</span>
            &quot;errorDetails&quot;, errorDetails
        );
    }

    @Override
    public String toString() {
<span class="fc" id="L165">        return String.format(&quot;%s{errorCode='%s', correlationId='%s', category=%s, severity=%s, message='%s'}&quot;,</span>
<span class="fc" id="L166">                           getClass().getSimpleName(), errorCode, correlationId, category, severity, getMessage());</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>