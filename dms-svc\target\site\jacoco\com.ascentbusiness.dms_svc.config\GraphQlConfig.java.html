<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>GraphQlConfig.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.config</a> &gt; <span class="el_source">GraphQlConfig.java</span></div><h1>GraphQlConfig.java</h1><pre class="source lang-java linenums">package com.ascentbusiness.dms_svc.config;

import com.ascentbusiness.dms_svc.interceptor.CorrelationIdInterceptor;
import com.ascentbusiness.dms_svc.resolver.*;
import com.ascentbusiness.dms_svc.service.*;
import graphql.ExecutionResult;
import graphql.execution.instrumentation.ChainedInstrumentation;
import graphql.execution.instrumentation.Instrumentation;
import graphql.execution.instrumentation.InstrumentationContext;
import graphql.execution.instrumentation.parameters.InstrumentationExecutionParameters;
import graphql.execution.instrumentation.InstrumentationState;
// import graphql.execution.instrumentation.dataloader.DataLoaderDispatcherInstrumentation; // Not available in current GraphQL version
import graphql.scalars.ExtendedScalars;
import graphql.schema.GraphQLScalarType;
import graphql.schema.Coercing;
import graphql.schema.CoercingParseLiteralException;
import graphql.schema.CoercingParseValueException;
import graphql.schema.CoercingSerializeException;
import lombok.extern.slf4j.Slf4j;
// import org.dataloader.DataLoader; // DataLoader dependency not available
// import org.dataloader.DataLoaderRegistry; // DataLoader dependency not available
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.graphql.execution.RuntimeWiringConfigurer;
import org.springframework.web.multipart.MultipartFile;

import java.util.Arrays;
import java.util.concurrent.CompletableFuture;

/**
 * GraphQL Configuration for DMS Service.
 *
 * This configuration class sets up:
 * - Custom scalar types (Upload, DateTime, Long)
 * - Performance monitoring instrumentation
 * - Security instrumentation
 * - Correlation ID tracking
 *
 * New GraphQL Resolvers (automatically registered by Spring Boot):
 * - DocumentUploadGraphQLResolver: Replaces DocumentRestController
 * - ConversionGraphQLResolver: Replaces MarkdownConversionController
 * - DiagnosticsGraphQLResolver: Replaces SharePointTestController
 * - TestCaseGraphQLResolver: Replaces TestCaseController
 * - TracingGraphQLResolver: Replaces TracingTestController
 *
 * Note: DataLoader patterns are planned for future implementation to prevent N+1 queries.
 */
@Configuration
<span class="nc" id="L50">@Slf4j</span>
<span class="nc" id="L51">public class GraphQlConfig {</span>

    @Autowired
    private CorrelationIdInterceptor correlationIdInterceptor;

    // @Autowired
    // private GraphQLRateLimitInterceptor rateLimitInterceptor;

    // @Autowired
    // private GraphQLVersioningInterceptor versioningInterceptor;

    @Bean
    public GraphQLScalarType uploadScalar() {
<span class="nc" id="L64">        return GraphQLScalarType.newScalar()</span>
<span class="nc" id="L65">                .name(&quot;Upload&quot;)</span>
<span class="nc" id="L66">                .description(&quot;A file upload scalar&quot;)</span>
<span class="nc" id="L67">                .coercing(new Coercing&lt;MultipartFile, String&gt;() {</span>
                    @Override
                    public String serialize(Object dataFetcherResult) throws CoercingSerializeException {
<span class="nc bnc" id="L70" title="All 2 branches missed.">                        if (dataFetcherResult instanceof MultipartFile) {</span>
<span class="nc" id="L71">                            return ((MultipartFile) dataFetcherResult).getOriginalFilename();</span>
                        }
<span class="nc" id="L73">                        throw new CoercingSerializeException(&quot;Unable to serialize &quot; + dataFetcherResult + &quot; as an Upload&quot;);</span>
                    }

                    @Override
                    public MultipartFile parseValue(Object input) throws CoercingParseValueException {
<span class="nc bnc" id="L78" title="All 2 branches missed.">                        if (input instanceof MultipartFile) {</span>
<span class="nc" id="L79">                            return (MultipartFile) input;</span>
                        }
<span class="nc" id="L81">                        throw new CoercingParseValueException(&quot;Unable to parse value &quot; + input + &quot; as an Upload&quot;);</span>
                    }

                    @Override
                    public MultipartFile parseLiteral(Object input) throws CoercingParseLiteralException {
<span class="nc" id="L86">                        throw new CoercingParseLiteralException(&quot;Upload scalar cannot be parsed from literal&quot;);</span>
                    }
                })
<span class="nc" id="L89">                .build();</span>
    }

    @Bean
    public RuntimeWiringConfigurer runtimeWiringConfigurer() {
<span class="nc" id="L94">        return wiringBuilder -&gt; {</span>
            try {
<span class="nc" id="L96">                wiringBuilder</span>
<span class="nc" id="L97">                    .scalar(ExtendedScalars.GraphQLLong)</span>
<span class="nc" id="L98">                    .scalar(ExtendedScalars.DateTime)</span>
<span class="nc" id="L99">                    .scalar(ExtendedScalars.Json)</span>
<span class="nc" id="L100">                    .scalar(uploadScalar());</span>
<span class="nc" id="L101">            } catch (Exception e) {</span>
                // Fallback: try to register scalars individually
                try {
<span class="nc" id="L104">                    wiringBuilder.scalar(ExtendedScalars.GraphQLLong);</span>
<span class="nc" id="L105">                } catch (Exception ignored) {}</span>

                try {
<span class="nc" id="L108">                    wiringBuilder.scalar(ExtendedScalars.DateTime);</span>
<span class="nc" id="L109">                } catch (Exception ignored) {}</span>

                try {
<span class="nc" id="L112">                    wiringBuilder.scalar(ExtendedScalars.Json);</span>
<span class="nc" id="L113">                } catch (Exception ignored) {}</span>

                try {
<span class="nc" id="L116">                    wiringBuilder.scalar(uploadScalar());</span>
<span class="nc" id="L117">                } catch (Exception ignored) {</span>
                    // Upload scalar registration failed
<span class="nc" id="L119">                }</span>
<span class="nc" id="L120">            }</span>
<span class="nc" id="L121">        };</span>
    }





    // ===== RESOLVER REGISTRATION AND PERFORMANCE MONITORING =====

    /**
     * Register all GraphQL resolvers and configure performance monitoring.
     * This ensures all new resolvers are properly registered with the GraphQL engine.
     *
     * Note: Resolvers are automatically discovered by Spring Boot's @Controller annotation,
     * but we can add explicit registration here if needed for custom configuration.
     */

    /**
     * Performance monitoring instrumentation for GraphQL operations.
     * Integrates with GraphQLMonitoringConfig to collect metrics automatically.
     */
    @Bean
    public Instrumentation performanceMonitoringInstrumentation(GraphQLMonitoringConfig monitoringConfig) {
<span class="nc" id="L144">        log.info(&quot;Configuring GraphQL performance monitoring instrumentation with automatic metrics collection&quot;);</span>

<span class="nc" id="L146">        return new Instrumentation() {</span>
            @Override
            public InstrumentationContext&lt;ExecutionResult&gt; beginExecution(
                    InstrumentationExecutionParameters parameters,
                    InstrumentationState state) {

<span class="nc" id="L152">                long startTime = System.currentTimeMillis();</span>
<span class="nc" id="L153">                String operationName = extractOperationName(parameters.getQuery());</span>

                // Record query start
<span class="nc" id="L156">                monitoringConfig.recordQueryStart();</span>

<span class="nc" id="L158">                return new InstrumentationContext&lt;ExecutionResult&gt;() {</span>
                    @Override
                    public void onCompleted(ExecutionResult result, Throwable t) {
<span class="nc" id="L161">                        long executionTime = System.currentTimeMillis() - startTime;</span>
<span class="nc bnc" id="L162" title="All 4 branches missed.">                        boolean hasErrors = result.getErrors() != null &amp;&amp; !result.getErrors().isEmpty();</span>

                        // Record query completion and execution time
<span class="nc" id="L165">                        monitoringConfig.recordQueryEnd(hasErrors);</span>
<span class="nc" id="L166">                        monitoringConfig.recordExecutionTime(operationName, executionTime, hasErrors);</span>

<span class="nc" id="L168">                        log.debug(&quot;GraphQL monitoring recorded - Operation: {}, Duration: {}ms, HasErrors: {}&quot;,</span>
<span class="nc" id="L169">                                operationName, executionTime, hasErrors);</span>
<span class="nc" id="L170">                    }</span>

                    @Override
                    public void onDispatched() {
                        // Called when execution is dispatched to another thread
<span class="nc" id="L175">                    }</span>
                };
            }

            private String extractOperationName(String query) {
<span class="nc bnc" id="L180" title="All 4 branches missed.">                if (query == null || query.trim().isEmpty()) {</span>
<span class="nc" id="L181">                    return &quot;anonymous&quot;;</span>
                }

                // Simple operation name extraction
<span class="nc" id="L185">                String trimmed = query.trim();</span>
<span class="nc bnc" id="L186" title="All 2 branches missed.">                if (trimmed.startsWith(&quot;query&quot;)) {</span>
                    // Extract operation name from query
<span class="nc" id="L188">                    String[] parts = trimmed.split(&quot;\\s+&quot;);</span>
<span class="nc bnc" id="L189" title="All 4 branches missed.">                    if (parts.length &gt; 1 &amp;&amp; !parts[1].equals(&quot;{&quot;)) {</span>
<span class="nc" id="L190">                        return parts[1].replaceAll(&quot;[{}]&quot;, &quot;&quot;);</span>
                    }
<span class="nc" id="L192">                    return &quot;query&quot;;</span>
<span class="nc bnc" id="L193" title="All 2 branches missed.">                } else if (trimmed.startsWith(&quot;mutation&quot;)) {</span>
                    // Extract operation name from mutation
<span class="nc" id="L195">                    String[] parts = trimmed.split(&quot;\\s+&quot;);</span>
<span class="nc bnc" id="L196" title="All 4 branches missed.">                    if (parts.length &gt; 1 &amp;&amp; !parts[1].equals(&quot;{&quot;)) {</span>
<span class="nc" id="L197">                        return parts[1].replaceAll(&quot;[{}]&quot;, &quot;&quot;);</span>
                    }
<span class="nc" id="L199">                    return &quot;mutation&quot;;</span>
<span class="nc bnc" id="L200" title="All 2 branches missed.">                } else if (trimmed.startsWith(&quot;subscription&quot;)) {</span>
<span class="nc" id="L201">                    return &quot;subscription&quot;;</span>
                } else {
<span class="nc" id="L203">                    return &quot;anonymous&quot;;</span>
                }
            }
        };
    }

    /**
     * Security and validation instrumentation for GraphQL operations.
     * Can be extended to add resolver-level security annotations.
     */
    @Bean
    public Instrumentation securityInstrumentation() {
<span class="nc" id="L215">        log.info(&quot;Configuring GraphQL security instrumentation&quot;);</span>

<span class="nc" id="L217">        return new Instrumentation() {</span>
            // Security instrumentation can be added here
            // Example: JWT validation, role-based access control, etc.
        };
    }

    @Bean
    public Instrumentation correlationIdInstrumentation() {
<span class="nc" id="L225">        return correlationIdInterceptor;</span>
    }

    // @Bean
    // public Instrumentation rateLimitInstrumentation() {
    //     return rateLimitInterceptor;
    // }

    // @Bean
    // public Instrumentation versioningInstrumentation() {
    //     return versioningInterceptor;
    // }

    @Bean
    public Instrumentation chainedInstrumentation(GraphQLMonitoringConfig monitoringConfig) {
<span class="nc" id="L240">        log.info(&quot;Configuring chained GraphQL instrumentation with {} instrumentations&quot;, 3);</span>

<span class="nc" id="L242">        return new ChainedInstrumentation(Arrays.asList(</span>
            correlationIdInterceptor,
<span class="nc" id="L244">            performanceMonitoringInstrumentation(monitoringConfig),</span>
<span class="nc" id="L245">            securityInstrumentation()</span>
            // rateLimitInterceptor - can be added when implemented
            // versioningInterceptor - can be added when implemented
        ));
    }


}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>