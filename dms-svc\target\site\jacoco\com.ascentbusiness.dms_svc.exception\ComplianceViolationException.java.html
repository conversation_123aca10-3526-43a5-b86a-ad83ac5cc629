<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>ComplianceViolationException.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.exception</a> &gt; <span class="el_source">ComplianceViolationException.java</span></div><h1>ComplianceViolationException.java</h1><pre class="source lang-java linenums">package com.ascentbusiness.dms_svc.exception;

/**
 * Exception thrown when a compliance violation is detected
 */
public class ComplianceViolationException extends RuntimeException {

    private String violationType;
    private String regulationViolated;
    private String controlFailed;

    public ComplianceViolationException(String message) {
<span class="nc" id="L13">        super(message);</span>
<span class="nc" id="L14">    }</span>

    public ComplianceViolationException(String message, Throwable cause) {
<span class="nc" id="L17">        super(message, cause);</span>
<span class="nc" id="L18">    }</span>

    public ComplianceViolationException(String message, String violationType, String regulationViolated) {
<span class="nc" id="L21">        super(message);</span>
<span class="nc" id="L22">        this.violationType = violationType;</span>
<span class="nc" id="L23">        this.regulationViolated = regulationViolated;</span>
<span class="nc" id="L24">    }</span>

    public ComplianceViolationException(String message, String violationType, String regulationViolated, String controlFailed) {
<span class="nc" id="L27">        super(message);</span>
<span class="nc" id="L28">        this.violationType = violationType;</span>
<span class="nc" id="L29">        this.regulationViolated = regulationViolated;</span>
<span class="nc" id="L30">        this.controlFailed = controlFailed;</span>
<span class="nc" id="L31">    }</span>

    public String getViolationType() {
<span class="nc" id="L34">        return violationType;</span>
    }

    public void setViolationType(String violationType) {
<span class="nc" id="L38">        this.violationType = violationType;</span>
<span class="nc" id="L39">    }</span>

    public String getRegulationViolated() {
<span class="nc" id="L42">        return regulationViolated;</span>
    }

    public void setRegulationViolated(String regulationViolated) {
<span class="nc" id="L46">        this.regulationViolated = regulationViolated;</span>
<span class="nc" id="L47">    }</span>

    public String getControlFailed() {
<span class="nc" id="L50">        return controlFailed;</span>
    }

    public void setControlFailed(String controlFailed) {
<span class="nc" id="L54">        this.controlFailed = controlFailed;</span>
<span class="nc" id="L55">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>