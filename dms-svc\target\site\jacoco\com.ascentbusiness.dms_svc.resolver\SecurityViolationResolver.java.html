<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>SecurityViolationResolver.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.resolver</a> &gt; <span class="el_source">SecurityViolationResolver.java</span></div><h1>SecurityViolationResolver.java</h1><pre class="source lang-java linenums">package com.ascentbusiness.dms_svc.resolver;

import com.ascentbusiness.dms_svc.entity.SecurityViolation;
import com.ascentbusiness.dms_svc.enums.SecurityViolationType;
import com.ascentbusiness.dms_svc.enums.ViolationSeverity;
import com.ascentbusiness.dms_svc.repository.SecurityViolationRepository;
import com.ascentbusiness.dms_svc.service.SecurityService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.graphql.data.method.annotation.Argument;
import org.springframework.graphql.data.method.annotation.MutationMapping;
import org.springframework.graphql.data.method.annotation.QueryMapping;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;

import java.time.LocalDateTime;
import java.util.List;

/**
 * GraphQL resolver for security violation operations.
 * Provides queries and mutations for managing security violations with correlation ID support.
 */
@Controller
<span class="nc" id="L25">public class SecurityViolationResolver {</span>

<span class="nc" id="L27">    private static final Logger logger = LoggerFactory.getLogger(SecurityViolationResolver.class);</span>

    @Autowired
    private SecurityViolationRepository securityViolationRepository;

    @Autowired
    private SecurityService securityService;

    /**
     * Get security violations by correlation ID for request tracing.
     * This allows clients to trace all security violations related to a specific request.
     */
    @QueryMapping
    @PreAuthorize(&quot;hasRole('ADMIN')&quot;)
    public List&lt;SecurityViolation&gt; getSecurityViolationsByCorrelationId(@Argument String correlationId) {
<span class="nc" id="L42">        logger.info(&quot;Querying security violations for correlation ID: {}&quot;, correlationId);</span>
<span class="nc" id="L43">        return securityViolationRepository.findByCorrelationIdOrderByCreatedDateDesc(correlationId);</span>
    }

    /**
     * Get unresolved security violations by correlation ID.
     */
    @QueryMapping
    @PreAuthorize(&quot;hasRole('ADMIN')&quot;)
    public List&lt;SecurityViolation&gt; getUnresolvedSecurityViolationsByCorrelationId(@Argument String correlationId) {
<span class="nc" id="L52">        logger.info(&quot;Querying unresolved security violations for correlation ID: {}&quot;, correlationId);</span>
<span class="nc" id="L53">        return securityViolationRepository.findUnresolvedByCorrelationId(correlationId);</span>
    }

    /**
     * Get security violations by user ID.
     */
    @QueryMapping
    @PreAuthorize(&quot;hasRole('ADMIN') or #userId == authentication.name&quot;)
    public List&lt;SecurityViolation&gt; getSecurityViolationsByUserId(@Argument String userId,
                                                               @Argument Boolean isResolved) {
<span class="nc" id="L63">        logger.info(&quot;Querying security violations for user: {}&quot;, userId);</span>
<span class="nc bnc" id="L64" title="All 2 branches missed.">        if (isResolved != null) {</span>
<span class="nc" id="L65">            return securityViolationRepository.findByUserIdAndIsResolved(userId, isResolved);</span>
        } else {
<span class="nc" id="L67">            return securityViolationRepository.findByUserIdOrderByCreatedDateDesc(userId);</span>
        }
    }

    /**
     * Get security violations by violation type and date range.
     */
    @QueryMapping
    @PreAuthorize(&quot;hasRole('ADMIN')&quot;)
    public List&lt;SecurityViolation&gt; getSecurityViolationsByType(@Argument SecurityViolationType violationType,
                                                             @Argument LocalDateTime afterDate) {
<span class="nc" id="L78">        logger.info(&quot;Querying security violations by type: {}&quot;, violationType);</span>
<span class="nc bnc" id="L79" title="All 2 branches missed.">        if (afterDate != null) {</span>
<span class="nc" id="L80">            return securityViolationRepository.findByViolationTypeAndCreatedDateAfter(violationType, afterDate);</span>
        } else {
<span class="nc" id="L82">            return securityViolationRepository.findByViolationTypeAndCreatedDateAfter(</span>
<span class="nc" id="L83">                    violationType, LocalDateTime.now().minusDays(30)); // Default to last 30 days</span>
        }
    }

    /**
     * Get security violations by severity.
     */
    @QueryMapping
    @PreAuthorize(&quot;hasRole('ADMIN')&quot;)
    public List&lt;SecurityViolation&gt; getSecurityViolationsBySeverity(@Argument ViolationSeverity severity,
                                                                 @Argument Boolean isResolved) {
<span class="nc" id="L94">        logger.info(&quot;Querying security violations by severity: {}&quot;, severity);</span>
<span class="nc bnc" id="L95" title="All 2 branches missed.">        if (isResolved != null) {</span>
<span class="nc" id="L96">            return securityViolationRepository.findBySeverityAndIsResolved(severity, isResolved);</span>
        } else {
<span class="nc" id="L98">            return securityViolationRepository.findUnresolvedBySeverity(severity);</span>
        }
    }

    /**
     * Get recent unresolved security violations.
     */
    @QueryMapping
    @PreAuthorize(&quot;hasRole('ADMIN')&quot;)
    public List&lt;SecurityViolation&gt; getRecentUnresolvedSecurityViolations(@Argument Integer hours) {
<span class="nc bnc" id="L108" title="All 2 branches missed.">        int hoursBack = hours != null ? hours : 24; // Default to last 24 hours</span>
<span class="nc" id="L109">        LocalDateTime sinceDate = LocalDateTime.now().minusHours(hoursBack);</span>
<span class="nc" id="L110">        logger.info(&quot;Querying recent unresolved security violations since: {}&quot;, sinceDate);</span>
<span class="nc" id="L111">        return securityViolationRepository.findRecentUnresolvedViolations(sinceDate);</span>
    }

    /**
     * Resolve a security violation.
     */
    @MutationMapping
    @PreAuthorize(&quot;hasRole('ADMIN')&quot;)
    public Boolean resolveSecurityViolation(@Argument Long violationId, @Argument String resolvedBy) {
        try {
<span class="nc" id="L121">            logger.info(&quot;Resolving security violation {} by {}&quot;, violationId, resolvedBy);</span>
<span class="nc" id="L122">            securityService.resolveViolation(violationId, resolvedBy);</span>
<span class="nc" id="L123">            return true;</span>
<span class="nc" id="L124">        } catch (Exception e) {</span>
<span class="nc" id="L125">            logger.error(&quot;Failed to resolve security violation {}: {}&quot;, violationId, e.getMessage());</span>
<span class="nc" id="L126">            return false;</span>
        }
    }

    /**
     * Get security violation statistics by correlation ID.
     * Useful for understanding the scope of issues related to a specific request.
     */
    @QueryMapping
    @PreAuthorize(&quot;hasRole('ADMIN')&quot;)
    public SecurityViolationStats getSecurityViolationStatsByCorrelationId(@Argument String correlationId) {
<span class="nc" id="L137">        logger.info(&quot;Getting security violation statistics for correlation ID: {}&quot;, correlationId);</span>
        
<span class="nc" id="L139">        List&lt;SecurityViolation&gt; violations = securityViolationRepository.findByCorrelationId(correlationId);</span>
        
<span class="nc" id="L141">        long totalCount = violations.size();</span>
<span class="nc bnc" id="L142" title="All 2 branches missed.">        long unresolvedCount = violations.stream().filter(v -&gt; !v.getIsResolved()).count();</span>
<span class="nc bnc" id="L143" title="All 2 branches missed.">        long criticalCount = violations.stream().filter(v -&gt; v.getSeverity() == ViolationSeverity.CRITICAL).count();</span>
<span class="nc bnc" id="L144" title="All 2 branches missed.">        long highCount = violations.stream().filter(v -&gt; v.getSeverity() == ViolationSeverity.HIGH).count();</span>
        
<span class="nc" id="L146">        return SecurityViolationStats.builder()</span>
<span class="nc" id="L147">                .correlationId(correlationId)</span>
<span class="nc" id="L148">                .totalCount(totalCount)</span>
<span class="nc" id="L149">                .unresolvedCount(unresolvedCount)</span>
<span class="nc" id="L150">                .criticalCount(criticalCount)</span>
<span class="nc" id="L151">                .highCount(highCount)</span>
<span class="nc" id="L152">                .build();</span>
    }

    /**
     * Inner class for security violation statistics.
     */
<span class="nc" id="L158">    public static class SecurityViolationStats {</span>
        private String correlationId;
        private long totalCount;
        private long unresolvedCount;
        private long criticalCount;
        private long highCount;

        public static SecurityViolationStatsBuilder builder() {
<span class="nc" id="L166">            return new SecurityViolationStatsBuilder();</span>
        }

        // Getters
<span class="nc" id="L170">        public String getCorrelationId() { return correlationId; }</span>
<span class="nc" id="L171">        public long getTotalCount() { return totalCount; }</span>
<span class="nc" id="L172">        public long getUnresolvedCount() { return unresolvedCount; }</span>
<span class="nc" id="L173">        public long getCriticalCount() { return criticalCount; }</span>
<span class="nc" id="L174">        public long getHighCount() { return highCount; }</span>

        // Builder class
<span class="nc" id="L177">        public static class SecurityViolationStatsBuilder {</span>
            private String correlationId;
            private long totalCount;
            private long unresolvedCount;
            private long criticalCount;
            private long highCount;

            public SecurityViolationStatsBuilder correlationId(String correlationId) {
<span class="nc" id="L185">                this.correlationId = correlationId;</span>
<span class="nc" id="L186">                return this;</span>
            }

            public SecurityViolationStatsBuilder totalCount(long totalCount) {
<span class="nc" id="L190">                this.totalCount = totalCount;</span>
<span class="nc" id="L191">                return this;</span>
            }

            public SecurityViolationStatsBuilder unresolvedCount(long unresolvedCount) {
<span class="nc" id="L195">                this.unresolvedCount = unresolvedCount;</span>
<span class="nc" id="L196">                return this;</span>
            }

            public SecurityViolationStatsBuilder criticalCount(long criticalCount) {
<span class="nc" id="L200">                this.criticalCount = criticalCount;</span>
<span class="nc" id="L201">                return this;</span>
            }

            public SecurityViolationStatsBuilder highCount(long highCount) {
<span class="nc" id="L205">                this.highCount = highCount;</span>
<span class="nc" id="L206">                return this;</span>
            }

            public SecurityViolationStats build() {
<span class="nc" id="L210">                SecurityViolationStats stats = new SecurityViolationStats();</span>
<span class="nc" id="L211">                stats.correlationId = this.correlationId;</span>
<span class="nc" id="L212">                stats.totalCount = this.totalCount;</span>
<span class="nc" id="L213">                stats.unresolvedCount = this.unresolvedCount;</span>
<span class="nc" id="L214">                stats.criticalCount = this.criticalCount;</span>
<span class="nc" id="L215">                stats.highCount = this.highCount;</span>
<span class="nc" id="L216">                return stats;</span>
            }
        }
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>