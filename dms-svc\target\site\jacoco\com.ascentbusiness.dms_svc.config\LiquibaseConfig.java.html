<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>LiquibaseConfig.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.config</a> &gt; <span class="el_source">LiquibaseConfig.java</span></div><h1>LiquibaseConfig.java</h1><pre class="source lang-java linenums">package com.ascentbusiness.dms_svc.config;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;
import jakarta.annotation.PostConstruct;
import java.util.logging.Logger;

/**
 * Liquibase Configuration to handle Java 21 compatibility issues
 * Specifically addresses JsonChangeLogParser ServiceConfigurationError
 */
@Configuration
@ConditionalOnProperty(name = &quot;spring.liquibase.enabled&quot;, havingValue = &quot;true&quot;)
<span class="nc" id="L14">public class LiquibaseConfig {</span>

<span class="nc" id="L16">    private static final Logger logger = Logger.getLogger(LiquibaseConfig.class.getName());</span>

    @PostConstruct
    public void configureLiquibase() {
<span class="nc" id="L20">        logger.info(&quot;Configuring Liquibase with Java 21 compatibility fixes&quot;);</span>
        
        // Suppress service locator errors to prevent JsonChangeLogParser issues
<span class="nc" id="L23">        System.setProperty(&quot;liquibase.servicelocator.supress.errors&quot;, &quot;true&quot;);</span>
<span class="nc" id="L24">        System.setProperty(&quot;liquibase.parser.json.enabled&quot;, &quot;false&quot;);</span>
        
        // Additional properties to handle Java 21 module system
<span class="nc" id="L27">        System.setProperty(&quot;liquibase.servicelocator.class.name&quot;, &quot;liquibase.servicelocator.StandardServiceLocator&quot;);</span>
        
<span class="nc" id="L29">        logger.info(&quot;Liquibase Java 21 compatibility properties set successfully&quot;);</span>
<span class="nc" id="L30">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>