<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>UnauthorizedException.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.exception</a> &gt; <span class="el_source">UnauthorizedException.java</span></div><h1>UnauthorizedException.java</h1><pre class="source lang-java linenums">package com.ascentbusiness.dms_svc.exception;

import java.util.Map;

/**
 * Exception thrown when a user lacks sufficient authorization to perform an operation.
 * Extends DmsSecurityException with specific error handling for authorization failures.
 */
public class UnauthorizedException extends DmsSecurityException {

    public UnauthorizedException(String message) {
<span class="nc" id="L12">        super(message, &quot;ACCESS_DENIED&quot;, ErrorCategory.AUTHORIZATION);</span>
<span class="nc" id="L13">    }</span>

    public UnauthorizedException(String message, Throwable cause) {
<span class="nc" id="L16">        super(message, &quot;ACCESS_DENIED&quot;, ErrorCategory.AUTHORIZATION, Map.of(), cause);</span>
<span class="nc" id="L17">    }</span>

    public UnauthorizedException(String resource, String action) {
<span class="nc" id="L20">        super(String.format(&quot;Access denied: insufficient permissions to %s on %s&quot;, action, resource),</span>
              &quot;ACCESS_DENIED&quot;,
              ErrorCategory.AUTHORIZATION,
<span class="nc" id="L23">              Map.of(&quot;resource&quot;, resource, &quot;action&quot;, action));</span>
<span class="nc" id="L24">    }</span>

    public UnauthorizedException(String message, String requiredPermission, String currentPermissions) {
<span class="nc" id="L27">        super(message,</span>
              &quot;INSUFFICIENT_PERMISSIONS&quot;,
              ErrorCategory.AUTHORIZATION,
<span class="nc" id="L30">              Map.of(&quot;requiredPermission&quot;, requiredPermission, &quot;currentPermissions&quot;, currentPermissions));</span>
<span class="nc" id="L31">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>