<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>UserPrincipal.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.security</a> &gt; <span class="el_source">UserPrincipal.java</span></div><h1>UserPrincipal.java</h1><pre class="source lang-java linenums">package com.ascentbusiness.dms_svc.security;

import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

public class UserPrincipal implements UserDetails {
    private String username;
    private List&lt;String&gt; roles;
    private List&lt;String&gt; permissions;
    private Collection&lt;? extends GrantedAuthority&gt; authorities;

<span class="nc" id="L18">    public UserPrincipal(String username, List&lt;String&gt; roles, List&lt;String&gt; permissions) {</span>
<span class="nc" id="L19">        this.username = username;</span>
<span class="nc bnc" id="L20" title="All 2 branches missed.">        this.roles = roles != null ? roles : Collections.emptyList();</span>
<span class="nc bnc" id="L21" title="All 2 branches missed.">        this.permissions = permissions != null ? permissions : Collections.emptyList();</span>
<span class="nc" id="L22">        this.authorities = createAuthorities(this.roles);</span>
<span class="nc" id="L23">    }</span>

<span class="nc" id="L25">    public UserPrincipal(String username, Collection&lt;? extends GrantedAuthority&gt; authorities) {</span>
<span class="nc" id="L26">        this.username = username;</span>
<span class="nc" id="L27">        this.roles = Collections.emptyList();</span>
<span class="nc" id="L28">        this.permissions = Collections.emptyList();</span>
<span class="nc" id="L29">        this.authorities = authorities;</span>
<span class="nc" id="L30">    }</span>

    public static UserPrincipal create(String username) {
<span class="nc" id="L33">        return new UserPrincipal(username, Collections.emptyList());</span>
    }

    public static UserPrincipal create(String username, List&lt;String&gt; roles, List&lt;String&gt; permissions) {
<span class="nc" id="L37">        return new UserPrincipal(username, roles, permissions);</span>
    }

    private Collection&lt;? extends GrantedAuthority&gt; createAuthorities(List&lt;String&gt; roles) {
<span class="nc" id="L41">        return roles.stream()</span>
<span class="nc" id="L42">                .map(role -&gt; new SimpleGrantedAuthority(&quot;ROLE_&quot; + role))</span>
<span class="nc" id="L43">                .collect(Collectors.toList());</span>
    }

    public List&lt;String&gt; getRoles() {
<span class="nc" id="L47">        return roles;</span>
    }

    public List&lt;String&gt; getPermissions() {
<span class="nc" id="L51">        return permissions;</span>
    }

    @Override
    public String getUsername() {
<span class="nc" id="L56">        return username;</span>
    }

    @Override
    public String getPassword() {
<span class="nc" id="L61">        return null; // No password needed for JWT-based auth</span>
    }

    @Override
    public Collection&lt;? extends GrantedAuthority&gt; getAuthorities() {
<span class="nc" id="L66">        return authorities;</span>
    }

    @Override
    public boolean isAccountNonExpired() {
<span class="nc" id="L71">        return true;</span>
    }

    @Override
    public boolean isAccountNonLocked() {
<span class="nc" id="L76">        return true;</span>
    }

    @Override
    public boolean isCredentialsNonExpired() {
<span class="nc" id="L81">        return true;</span>
    }

    @Override
    public boolean isEnabled() {
<span class="nc" id="L86">        return true;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.************</span></div></body></html>