<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>DmsValidationException</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.html" class="el_package">com.ascentbusiness.dms_svc.exception</a> &gt; <span class="el_class">DmsValidationException</span></div><h1>DmsValidationException</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">272 of 272</td><td class="ctr2">0%</td><td class="bar">2 of 2</td><td class="ctr2">0%</td><td class="ctr1">15</td><td class="ctr2">15</td><td class="ctr1">39</td><td class="ctr2">39</td><td class="ctr1">14</td><td class="ctr2">14</td></tr></tfoot><tbody><tr><td id="a13"><a href="DmsValidationException.java.html#L57" class="el_method">valueOutOfRange(String, Object, Object, Object)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="34" alt="34"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d1"/><td class="ctr2" id="e1">n/a</td><td class="ctr1" id="f1">1</td><td class="ctr2" id="g1">1</td><td class="ctr1" id="h2">3</td><td class="ctr2" id="i2">3</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a7"><a href="DmsValidationException.java.html#L82" class="el_method">fileSizeExceeded(String, long, long)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="112" height="10" title="32" alt="32"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d2"/><td class="ctr2" id="e2">n/a</td><td class="ctr1" id="f2">1</td><td class="ctr2" id="g2">1</td><td class="ctr1" id="h0">4</td><td class="ctr2" id="i0">4</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a8"><a href="DmsValidationException.java.html#L73" class="el_method">invalidFileType(String, String, String[])</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="105" height="10" title="30" alt="30"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d3"/><td class="ctr2" id="e3">n/a</td><td class="ctr1" id="f3">1</td><td class="ctr2" id="g3">1</td><td class="ctr1" id="h1">4</td><td class="ctr2" id="i1">4</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a11"><a href="DmsValidationException.java.html#L91" class="el_method">invalidState(String, String, String)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="98" height="10" title="28" alt="28"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d4"/><td class="ctr2" id="e4">n/a</td><td class="ctr1" id="f4">1</td><td class="ctr2" id="g4">1</td><td class="ctr1" id="h3">3</td><td class="ctr2" id="i3">3</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a10"><a href="DmsValidationException.java.html#L33" class="el_method">invalidInput(String, Object)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="95" height="10" title="27" alt="27"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="2" alt="2"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f0">2</td><td class="ctr2" id="g0">2</td><td class="ctr1" id="h4">3</td><td class="ctr2" id="i4">3</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a9"><a href="DmsValidationException.java.html#L49" class="el_method">invalidFormat(String, String)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="77" height="10" title="22" alt="22"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d5"/><td class="ctr2" id="e5">n/a</td><td class="ctr1" id="f5">1</td><td class="ctr2" id="g5">1</td><td class="ctr1" id="h5">3</td><td class="ctr2" id="i5">3</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a6"><a href="DmsValidationException.java.html#L65" class="el_method">duplicateValue(String, Object)</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="77" height="10" title="22" alt="22"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d6"/><td class="ctr2" id="e6">n/a</td><td class="ctr1" id="f6">1</td><td class="ctr2" id="g6">1</td><td class="ctr1" id="h6">3</td><td class="ctr2" id="i6">3</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a0"><a href="DmsValidationException.java.html#L100" class="el_method">businessRuleViolation(String, String)</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="77" height="10" title="22" alt="22"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d7"/><td class="ctr2" id="e7">n/a</td><td class="ctr1" id="f7">1</td><td class="ctr2" id="g7">1</td><td class="ctr1" id="h7">3</td><td class="ctr2" id="i7">3</td><td class="ctr1" id="j7">1</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a12"><a href="DmsValidationException.java.html#L41" class="el_method">requiredField(String)</a></td><td class="bar" id="b8"><img src="../jacoco-resources/redbar.gif" width="56" height="10" title="16" alt="16"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d8"/><td class="ctr2" id="e8">n/a</td><td class="ctr1" id="f8">1</td><td class="ctr2" id="g8">1</td><td class="ctr1" id="h8">3</td><td class="ctr2" id="i8">3</td><td class="ctr1" id="j8">1</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a4"><a href="DmsValidationException.java.html#L28" class="el_method">DmsValidationException(String, String, Map, Throwable)</a></td><td class="bar" id="b9"><img src="../jacoco-resources/redbar.gif" width="31" height="10" title="9" alt="9"/></td><td class="ctr2" id="c9">0%</td><td class="bar" id="d9"/><td class="ctr2" id="e9">n/a</td><td class="ctr1" id="f9">1</td><td class="ctr2" id="g9">1</td><td class="ctr1" id="h9">2</td><td class="ctr2" id="i9">2</td><td class="ctr1" id="j9">1</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a3"><a href="DmsValidationException.java.html#L20" class="el_method">DmsValidationException(String, String, Map)</a></td><td class="bar" id="b10"><img src="../jacoco-resources/redbar.gif" width="28" height="10" title="8" alt="8"/></td><td class="ctr2" id="c10">0%</td><td class="bar" id="d10"/><td class="ctr2" id="e10">n/a</td><td class="ctr1" id="f10">1</td><td class="ctr2" id="g10">1</td><td class="ctr1" id="h10">2</td><td class="ctr2" id="i10">2</td><td class="ctr1" id="j10">1</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a5"><a href="DmsValidationException.java.html#L24" class="el_method">DmsValidationException(String, String, Throwable)</a></td><td class="bar" id="b11"><img src="../jacoco-resources/redbar.gif" width="28" height="10" title="8" alt="8"/></td><td class="ctr2" id="c11">0%</td><td class="bar" id="d11"/><td class="ctr2" id="e11">n/a</td><td class="ctr1" id="f11">1</td><td class="ctr2" id="g11">1</td><td class="ctr1" id="h11">2</td><td class="ctr2" id="i11">2</td><td class="ctr1" id="j11">1</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a1"><a href="DmsValidationException.java.html#L12" class="el_method">DmsValidationException(String)</a></td><td class="bar" id="b12"><img src="../jacoco-resources/redbar.gif" width="24" height="10" title="7" alt="7"/></td><td class="ctr2" id="c12">0%</td><td class="bar" id="d12"/><td class="ctr2" id="e12">n/a</td><td class="ctr1" id="f12">1</td><td class="ctr2" id="g12">1</td><td class="ctr1" id="h12">2</td><td class="ctr2" id="i12">2</td><td class="ctr1" id="j12">1</td><td class="ctr2" id="k12">1</td></tr><tr><td id="a2"><a href="DmsValidationException.java.html#L16" class="el_method">DmsValidationException(String, String)</a></td><td class="bar" id="b13"><img src="../jacoco-resources/redbar.gif" width="24" height="10" title="7" alt="7"/></td><td class="ctr2" id="c13">0%</td><td class="bar" id="d13"/><td class="ctr2" id="e13">n/a</td><td class="ctr1" id="f13">1</td><td class="ctr2" id="g13">1</td><td class="ctr1" id="h13">2</td><td class="ctr2" id="i13">2</td><td class="ctr1" id="j13">1</td><td class="ctr2" id="k13">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>