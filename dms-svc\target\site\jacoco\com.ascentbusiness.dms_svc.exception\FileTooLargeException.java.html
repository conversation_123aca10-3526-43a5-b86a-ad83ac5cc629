<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>FileTooLargeException.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.exception</a> &gt; <span class="el_source">FileTooLargeException.java</span></div><h1>FileTooLargeException.java</h1><pre class="source lang-java linenums">package com.ascentbusiness.dms_svc.exception;

/**
 * Exception thrown when a file exceeds the maximum allowed size for direct processing.
 * 
 * &lt;p&gt;This exception indicates that the file is too large for direct or asynchronous
 * processing and requires chunked upload handling. It provides guidance to clients
 * on the recommended chunk size and upload strategy.
 */
public class FileTooLargeException extends RuntimeException {
    
    private final long fileSize;
    private final long maxAllowedSize;
    private final int recommendedChunkSize;
    
    /**
     * Constructs a new FileTooLargeException with the specified detail message.
     * 
     * @param message the detail message
     */
    public FileTooLargeException(String message) {
<span class="nc" id="L22">        super(message);</span>
<span class="nc" id="L23">        this.fileSize = 0;</span>
<span class="nc" id="L24">        this.maxAllowedSize = 0;</span>
<span class="nc" id="L25">        this.recommendedChunkSize = 0;</span>
<span class="nc" id="L26">    }</span>
    
    /**
     * Constructs a new FileTooLargeException with detailed size information.
     * 
     * @param message the detail message
     * @param fileSize the actual file size
     * @param maxAllowedSize the maximum allowed size for direct processing
     * @param recommendedChunkSize the recommended chunk size for chunked upload
     */
    public FileTooLargeException(String message, long fileSize, long maxAllowedSize, int recommendedChunkSize) {
<span class="nc" id="L37">        super(message);</span>
<span class="nc" id="L38">        this.fileSize = fileSize;</span>
<span class="nc" id="L39">        this.maxAllowedSize = maxAllowedSize;</span>
<span class="nc" id="L40">        this.recommendedChunkSize = recommendedChunkSize;</span>
<span class="nc" id="L41">    }</span>
    
    /**
     * Constructs a new FileTooLargeException with the specified detail message and cause.
     * 
     * @param message the detail message
     * @param cause the cause
     */
    public FileTooLargeException(String message, Throwable cause) {
<span class="nc" id="L50">        super(message, cause);</span>
<span class="nc" id="L51">        this.fileSize = 0;</span>
<span class="nc" id="L52">        this.maxAllowedSize = 0;</span>
<span class="nc" id="L53">        this.recommendedChunkSize = 0;</span>
<span class="nc" id="L54">    }</span>
    
    /**
     * Get the actual file size that caused the exception.
     * 
     * @return file size in bytes
     */
    public long getFileSize() {
<span class="nc" id="L62">        return fileSize;</span>
    }
    
    /**
     * Get the maximum allowed size for direct processing.
     * 
     * @return maximum allowed size in bytes
     */
    public long getMaxAllowedSize() {
<span class="nc" id="L71">        return maxAllowedSize;</span>
    }
    
    /**
     * Get the recommended chunk size for chunked upload.
     * 
     * @return recommended chunk size in bytes
     */
    public int getRecommendedChunkSize() {
<span class="nc" id="L80">        return recommendedChunkSize;</span>
    }
    
    /**
     * Check if size information is available.
     * 
     * @return true if size information is available
     */
    public boolean hasSizeInformation() {
<span class="nc bnc" id="L89" title="All 4 branches missed.">        return fileSize &gt; 0 &amp;&amp; maxAllowedSize &gt; 0;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>