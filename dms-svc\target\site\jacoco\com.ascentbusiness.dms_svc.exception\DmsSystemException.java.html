<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>DmsSystemException.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.exception</a> &gt; <span class="el_source">DmsSystemException.java</span></div><h1>DmsSystemException.java</h1><pre class="source lang-java linenums">package com.ascentbusiness.dms_svc.exception;

import java.util.Map;

/**
 * Exception for system-level errors in the DMS system.
 * Used for infrastructure failures, external service errors, and system configuration issues.
 */
public class DmsSystemException extends DmsException {

    public DmsSystemException(String message) {
<span class="nc" id="L12">        super(message, &quot;SYSTEM_ERROR&quot;, DmsException.ErrorCategory.SYSTEM, DmsException.ErrorSeverity.HIGH);</span>
<span class="nc" id="L13">    }</span>

    public DmsSystemException(String message, String errorCode) {
<span class="nc" id="L16">        super(message, errorCode, DmsException.ErrorCategory.SYSTEM, DmsException.ErrorSeverity.HIGH);</span>
<span class="nc" id="L17">    }</span>

    public DmsSystemException(String message, String errorCode, DmsException.ErrorSeverity severity) {
<span class="nc" id="L20">        super(message, errorCode, DmsException.ErrorCategory.SYSTEM, severity);</span>
<span class="nc" id="L21">    }</span>

    public DmsSystemException(String message, String errorCode, Throwable cause) {
<span class="nc" id="L24">        super(message, errorCode, DmsException.ErrorCategory.SYSTEM, DmsException.ErrorSeverity.HIGH, cause);</span>
<span class="nc" id="L25">    }</span>

    public DmsSystemException(String message, String errorCode, Map&lt;String, Object&gt; errorDetails) {
<span class="nc" id="L28">        super(message, errorCode, DmsException.ErrorCategory.SYSTEM, DmsException.ErrorSeverity.HIGH, errorDetails);</span>
<span class="nc" id="L29">    }</span>

    public DmsSystemException(String message, String errorCode, Map&lt;String, Object&gt; errorDetails, Throwable cause) {
<span class="nc" id="L32">        super(message, errorCode, DmsException.ErrorCategory.SYSTEM, DmsException.ErrorSeverity.HIGH, errorDetails, cause);</span>
<span class="nc" id="L33">    }</span>

    public DmsSystemException(String message, String errorCode, DmsException.ErrorSeverity severity, Map&lt;String, Object&gt; errorDetails) {
<span class="nc" id="L36">        super(message, errorCode, DmsException.ErrorCategory.SYSTEM, severity, errorDetails);</span>
<span class="nc" id="L37">    }</span>

    // Database errors
    public static DmsSystemException databaseError(String operation, Throwable cause) {
<span class="nc" id="L41">        return new DmsSystemException(</span>
<span class="nc" id="L42">            String.format(&quot;Database error during %s: %s&quot;, operation, cause.getMessage()),</span>
            &quot;DATABASE_ERROR&quot;,
<span class="nc" id="L44">            Map.of(&quot;operation&quot;, operation, &quot;cause&quot;, cause.getClass().getSimpleName()),</span>
            cause
        );
    }

    public static DmsSystemException connectionTimeout(String serviceName, int timeoutSeconds) {
<span class="nc" id="L50">        return new DmsSystemException(</span>
<span class="nc" id="L51">            String.format(&quot;Connection timeout to %s after %d seconds&quot;, serviceName, timeoutSeconds),</span>
            &quot;CONNECTION_TIMEOUT&quot;,
<span class="nc" id="L53">            Map.of(&quot;serviceName&quot;, serviceName, &quot;timeoutSeconds&quot;, timeoutSeconds)</span>
        );
    }

    public static DmsSystemException configurationError(String configKey, String reason) {
<span class="nc" id="L58">        return new DmsSystemException(</span>
<span class="nc" id="L59">            String.format(&quot;Configuration error for key '%s': %s&quot;, configKey, reason),</span>
            &quot;CONFIGURATION_ERROR&quot;,
<span class="nc" id="L61">            Map.of(&quot;configKey&quot;, configKey, &quot;reason&quot;, reason)</span>
        );
    }

    // File system errors
    public static DmsSystemException fileSystemError(String operation, String path, Throwable cause) {
<span class="nc" id="L67">        return new DmsSystemException(</span>
<span class="nc" id="L68">            String.format(&quot;File system error during %s on path '%s': %s&quot;, operation, path, cause.getMessage()),</span>
            &quot;FILE_SYSTEM_ERROR&quot;,
<span class="nc" id="L70">            Map.of(&quot;operation&quot;, operation, &quot;path&quot;, path, &quot;cause&quot;, cause.getClass().getSimpleName()),</span>
            cause
        );
    }

    public static DmsSystemException storageError(String storageProvider, String operation, Throwable cause) {
<span class="nc" id="L76">        return new DmsSystemException(</span>
<span class="nc" id="L77">            String.format(&quot;Storage error with %s during %s: %s&quot;, storageProvider, operation, cause.getMessage()),</span>
            &quot;STORAGE_ERROR&quot;,
<span class="nc" id="L79">            Map.of(&quot;storageProvider&quot;, storageProvider, &quot;operation&quot;, operation, &quot;cause&quot;, cause.getClass().getSimpleName()),</span>
            cause
        );
    }

    public static DmsSystemException diskSpaceError(String path, long availableBytes, long requiredBytes) {
<span class="nc" id="L85">        return new DmsSystemException(</span>
<span class="nc" id="L86">            String.format(&quot;Insufficient disk space at '%s': available=%d bytes, required=%d bytes&quot;,</span>
<span class="nc" id="L87">                         path, availableBytes, requiredBytes),</span>
            &quot;DISK_SPACE_ERROR&quot;,
            DmsException.ErrorSeverity.CRITICAL,
<span class="nc" id="L90">            Map.of(&quot;path&quot;, path, &quot;availableBytes&quot;, availableBytes, &quot;requiredBytes&quot;, requiredBytes)</span>
        );
    }

    // External service errors
    public static DmsSystemException externalServiceError(String serviceName, String operation, int statusCode, String response) {
<span class="nc" id="L96">        return new DmsSystemException(</span>
<span class="nc" id="L97">            String.format(&quot;External service %s error during %s: HTTP %d - %s&quot;, </span>
<span class="nc" id="L98">                         serviceName, operation, statusCode, response),</span>
            &quot;EXTERNAL_SERVICE_ERROR&quot;,
<span class="nc" id="L100">            Map.of(&quot;serviceName&quot;, serviceName, &quot;operation&quot;, operation, &quot;statusCode&quot;, statusCode, &quot;response&quot;, response)</span>
        );
    }

    public static DmsSystemException externalServiceUnavailable(String serviceName) {
<span class="nc" id="L105">        return new DmsSystemException(</span>
<span class="nc" id="L106">            String.format(&quot;External service %s is unavailable&quot;, serviceName),</span>
            &quot;EXTERNAL_SERVICE_UNAVAILABLE&quot;,
            DmsException.ErrorSeverity.CRITICAL,
<span class="nc" id="L109">            Map.of(&quot;serviceName&quot;, serviceName)</span>
        );
    }

    public static DmsSystemException apiRateLimitExceeded(String serviceName, int retryAfterSeconds) {
<span class="nc" id="L114">        return new DmsSystemException(</span>
<span class="nc" id="L115">            String.format(&quot;API rate limit exceeded for %s. Retry after %d seconds&quot;, serviceName, retryAfterSeconds),</span>
            &quot;API_RATE_LIMIT_EXCEEDED&quot;,
            DmsException.ErrorSeverity.MEDIUM,
<span class="nc" id="L118">            Map.of(&quot;serviceName&quot;, serviceName, &quot;retryAfterSeconds&quot;, retryAfterSeconds)</span>
        );
    }

    // Search service errors
    public static DmsSystemException searchServiceError(String operation, Throwable cause) {
<span class="nc" id="L124">        return new DmsSystemException(</span>
<span class="nc" id="L125">            String.format(&quot;Search service error during %s: %s&quot;, operation, cause.getMessage()),</span>
            &quot;SEARCH_SERVICE_ERROR&quot;,
<span class="nc" id="L127">            Map.of(&quot;operation&quot;, operation, &quot;cause&quot;, cause.getClass().getSimpleName()),</span>
            cause
        );
    }

    public static DmsSystemException indexingError(String documentId, String reason) {
<span class="nc" id="L133">        return new DmsSystemException(</span>
<span class="nc" id="L134">            String.format(&quot;Document indexing failed for ID %s: %s&quot;, documentId, reason),</span>
            &quot;INDEXING_ERROR&quot;,
<span class="nc" id="L136">            Map.of(&quot;documentId&quot;, documentId, &quot;reason&quot;, reason)</span>
        );
    }

    // Memory and resource errors
    public static DmsSystemException memoryError(String operation, long usedMemory, long maxMemory) {
<span class="nc" id="L142">        return new DmsSystemException(</span>
<span class="nc" id="L143">            String.format(&quot;Memory error during %s: used=%d MB, max=%d MB&quot;,</span>
<span class="nc" id="L144">                         operation, usedMemory / 1024 / 1024, maxMemory / 1024 / 1024),</span>
            &quot;MEMORY_ERROR&quot;,
            DmsException.ErrorSeverity.CRITICAL,
<span class="nc" id="L147">            Map.of(&quot;operation&quot;, operation, &quot;usedMemoryMB&quot;, usedMemory / 1024 / 1024, &quot;maxMemoryMB&quot;, maxMemory / 1024 / 1024)</span>
        );
    }

    public static DmsSystemException threadPoolExhausted(String poolName, int activeThreads, int maxThreads) {
<span class="nc" id="L152">        return new DmsSystemException(</span>
<span class="nc" id="L153">            String.format(&quot;Thread pool %s exhausted: active=%d, max=%d&quot;, poolName, activeThreads, maxThreads),</span>
            &quot;THREAD_POOL_EXHAUSTED&quot;,
            DmsException.ErrorSeverity.HIGH,
<span class="nc" id="L156">            Map.of(&quot;poolName&quot;, poolName, &quot;activeThreads&quot;, activeThreads, &quot;maxThreads&quot;, maxThreads)</span>
        );
    }

    // Serialization errors
    public static DmsSystemException serializationError(String objectType, String operation, Throwable cause) {
<span class="nc" id="L162">        return new DmsSystemException(</span>
<span class="nc" id="L163">            String.format(&quot;Serialization error for %s during %s: %s&quot;, objectType, operation, cause.getMessage()),</span>
            &quot;SERIALIZATION_ERROR&quot;,
<span class="nc" id="L165">            Map.of(&quot;objectType&quot;, objectType, &quot;operation&quot;, operation, &quot;cause&quot;, cause.getClass().getSimpleName()),</span>
            cause
        );
    }

    // Migration errors
    public static DmsSystemException migrationError(String migrationType, String stage, Throwable cause) {
<span class="nc" id="L172">        return new DmsSystemException(</span>
<span class="nc" id="L173">            String.format(&quot;Migration error during %s at stage %s: %s&quot;, migrationType, stage, cause.getMessage()),</span>
            &quot;MIGRATION_ERROR&quot;,
<span class="nc" id="L175">            Map.of(&quot;migrationType&quot;, migrationType, &quot;stage&quot;, stage, &quot;cause&quot;, cause.getClass().getSimpleName()),</span>
            cause
        );
    }

    // Backup and recovery errors
    public static DmsSystemException backupError(String backupType, String reason) {
<span class="nc" id="L182">        return new DmsSystemException(</span>
<span class="nc" id="L183">            String.format(&quot;Backup error for %s: %s&quot;, backupType, reason),</span>
            &quot;BACKUP_ERROR&quot;,
            DmsException.ErrorSeverity.HIGH,
<span class="nc" id="L186">            Map.of(&quot;backupType&quot;, backupType, &quot;reason&quot;, reason)</span>
        );
    }

    public static DmsSystemException recoveryError(String recoveryType, String reason) {
<span class="nc" id="L191">        return new DmsSystemException(</span>
<span class="nc" id="L192">            String.format(&quot;Recovery error for %s: %s&quot;, recoveryType, reason),</span>
            &quot;RECOVERY_ERROR&quot;,
            DmsException.ErrorSeverity.CRITICAL,
<span class="nc" id="L195">            Map.of(&quot;recoveryType&quot;, recoveryType, &quot;reason&quot;, reason)</span>
        );
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>