<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>VirusScanResult.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.dto</a> &gt; <span class="el_source">VirusScanResult.java</span></div><h1>VirusScanResult.java</h1><pre class="source lang-java linenums">package com.ascentbusiness.dms_svc.dto;

import com.ascentbusiness.dms_svc.enums.VirusScannerType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.OffsetDateTime;

/**
 * DTO for virus scan result.
 * Corresponds to VirusScanResult GraphQL type.
 */
<span class="nc bnc" id="L15" title="All 46 branches missed.">@Data</span>
<span class="nc" id="L16">@Builder</span>
<span class="nc" id="L17">@NoArgsConstructor</span>
<span class="nc" id="L18">@AllArgsConstructor</span>
public class VirusScanResult {

    /**
     * Whether the file is clean (no threats detected).
     */
<span class="nc" id="L24">    private Boolean isClean;</span>

    /**
     * The type of scanner that performed the scan.
     */
<span class="nc" id="L29">    private VirusScannerType scannerUsed;</span>

    /**
     * When the scan was performed.
     */
<span class="nc" id="L34">    private OffsetDateTime scanDate;</span>

    /**
     * Details about any threats found (optional).
     */
<span class="nc" id="L39">    private String threatDetails;</span>

    /**
     * Whether the file was quarantined.
     */
<span class="nc" id="L44">    private Boolean quarantined;</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>