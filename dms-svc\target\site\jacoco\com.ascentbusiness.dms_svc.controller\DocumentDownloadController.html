<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>DocumentDownloadController</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.html" class="el_package">com.ascentbusiness.dms_svc.controller</a> &gt; <span class="el_class">DocumentDownloadController</span></div><h1>DocumentDownloadController</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">609 of 609</td><td class="ctr2">0%</td><td class="bar">14 of 14</td><td class="ctr2">0%</td><td class="ctr1">18</td><td class="ctr2">18</td><td class="ctr1">134</td><td class="ctr2">134</td><td class="ctr1">11</td><td class="ctr2">11</td></tr></tfoot><tbody><tr><td id="a3"><a href="DocumentDownloadController.java.html#L146" class="el_method">downloadDocumentV2(Long, boolean)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="180" alt="180"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="4" alt="4"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f0">3</td><td class="ctr2" id="g0">3</td><td class="ctr1" id="h0">38</td><td class="ctr2" id="i0">38</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a5"><a href="DocumentDownloadController.java.html#L222" class="el_method">getDownloadInfo(Long)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="92" height="10" title="138" alt="138"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d4"/><td class="ctr2" id="e4">n/a</td><td class="ctr1" id="f4">1</td><td class="ctr2" id="g4">1</td><td class="ctr1" id="h1">30</td><td class="ctr2" id="i1">30</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a2"><a href="DocumentDownloadController.java.html#L81" class="el_method">downloadDocumentV1(Long)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="76" height="10" title="115" alt="115"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="60" height="10" title="2" alt="2"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f3">2</td><td class="ctr2" id="g3">2</td><td class="ctr1" id="h2">27</td><td class="ctr2" id="i2">27</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a6"><a href="DocumentDownloadController.java.html#L336" class="el_method">handleDocumentNotFound(DocumentNotFoundException)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="24" height="10" title="37" alt="37"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d5"/><td class="ctr2" id="e5">n/a</td><td class="ctr1" id="f5">1</td><td class="ctr2" id="g5">1</td><td class="ctr1" id="h3">7</td><td class="ctr2" id="i3">7</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a8"><a href="DocumentDownloadController.java.html#L352" class="el_method">handleUnauthorized(UnauthorizedException)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="24" height="10" title="37" alt="37"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d6"/><td class="ctr2" id="e6">n/a</td><td class="ctr1" id="f6">1</td><td class="ctr2" id="g6">1</td><td class="ctr1" id="h4">7</td><td class="ctr2" id="i4">7</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a7"><a href="DocumentDownloadController.java.html#L368" class="el_method">handleGeneralException(Exception)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="23" height="10" title="35" alt="35"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d7"/><td class="ctr2" id="e7">n/a</td><td class="ctr1" id="f7">1</td><td class="ctr2" id="g7">1</td><td class="ctr1" id="h5">7</td><td class="ctr2" id="i5">7</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a9"><a href="DocumentDownloadController.java.html#L297" class="el_method">sanitizeFilename(String)</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="17" height="10" title="26" alt="26"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="4" alt="4"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f1">3</td><td class="ctr2" id="g1">3</td><td class="ctr1" id="h6">7</td><td class="ctr2" id="i6">7</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a4"><a href="DocumentDownloadController.java.html#L319" class="el_method">getCurrentUserId()</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="12" height="10" title="19" alt="19"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="4" alt="4"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f2">3</td><td class="ctr2" id="g2">3</td><td class="ctr1" id="h7">7</td><td class="ctr2" id="i7">7</td><td class="ctr1" id="j7">1</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a0"><a href="DocumentDownloadController.java.html#L276" class="el_method">bulkDownload(String)</a></td><td class="bar" id="b8"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="9" alt="9"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d8"/><td class="ctr2" id="e8">n/a</td><td class="ctr1" id="f8">1</td><td class="ctr2" id="g8">1</td><td class="ctr1" id="h8">2</td><td class="ctr2" id="i8">2</td><td class="ctr1" id="j8">1</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a1"><a href="DocumentDownloadController.java.html#L57" class="el_method">DocumentDownloadController(DocumentService, AuditService)</a></td><td class="bar" id="b9"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="9" alt="9"/></td><td class="ctr2" id="c9">0%</td><td class="bar" id="d9"/><td class="ctr2" id="e9">n/a</td><td class="ctr1" id="f9">1</td><td class="ctr2" id="g9">1</td><td class="ctr1" id="h9">1</td><td class="ctr2" id="i9">1</td><td class="ctr1" id="j9">1</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a10"><a href="DocumentDownloadController.java.html#L58" class="el_method">static {...}</a></td><td class="bar" id="b10"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="4" alt="4"/></td><td class="ctr2" id="c10">0%</td><td class="bar" id="d10"/><td class="ctr2" id="e10">n/a</td><td class="ctr1" id="f10">1</td><td class="ctr2" id="g10">1</td><td class="ctr1" id="h10">1</td><td class="ctr2" id="i10">1</td><td class="ctr1" id="j10">1</td><td class="ctr2" id="k10">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>