<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>SystemEventRepository.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">dms-svc</a> &gt; <a href="index.source.html" class="el_package">com.ascentbusiness.dms_svc.repository</a> &gt; <span class="el_source">SystemEventRepository.java</span></div><h1>SystemEventRepository.java</h1><pre class="source lang-java linenums">package com.ascentbusiness.dms_svc.repository;

import com.ascentbusiness.dms_svc.entity.SystemEvent;
import com.ascentbusiness.dms_svc.enums.EventType;
import com.ascentbusiness.dms_svc.enums.EventCategory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Repository interface for SystemEvent entities
 */
@Repository
public interface SystemEventRepository extends JpaRepository&lt;SystemEvent, Long&gt; {

    /**
     * Find events by type
     */
    List&lt;SystemEvent&gt; findByEventType(EventType eventType);

    /**
     * Find events by category
     */
    List&lt;SystemEvent&gt; findByEventCategory(EventCategory eventCategory);

    /**
     * Find events by actor
     */
    List&lt;SystemEvent&gt; findByActorUserId(String actorUserId);

    /**
     * Find events by actor with pagination
     */
    Page&lt;SystemEvent&gt; findByActorUserId(String actorUserId, Pageable pageable);

    /**
     * Find events by correlation ID
     */
    List&lt;SystemEvent&gt; findByCorrelationId(String correlationId);

    /**
     * Find events by session ID
     */
    List&lt;SystemEvent&gt; findBySessionId(String sessionId);

    /**
     * Find events by processing status
     */
    List&lt;SystemEvent&gt; findByProcessingStatus(String processingStatus);

    /**
     * Find pending events
     */
    default List&lt;SystemEvent&gt; findPendingEvents() {
<span class="nc" id="L61">        return findByProcessingStatus(&quot;PENDING&quot;);</span>
    }

    /**
     * Find events within date range
     */
    List&lt;SystemEvent&gt; findByEventTimestampBetween(LocalDateTime startDate, LocalDateTime endDate);

    /**
     * Find events within date range with pagination
     */
    Page&lt;SystemEvent&gt; findByEventTimestampBetween(LocalDateTime startDate, LocalDateTime endDate, Pageable pageable);

    /**
     * Find events by source entity
     */
    List&lt;SystemEvent&gt; findBySourceEntityTypeAndSourceEntityId(String sourceEntityType, Long sourceEntityId);

    /**
     * Find recent events
     */
    List&lt;SystemEvent&gt; findByEventTimestampAfterOrderByEventTimestampDesc(LocalDateTime cutoffDate);

    /**
     * Find events by type and date range
     */
    List&lt;SystemEvent&gt; findByEventTypeAndEventTimestampBetween(EventType eventType, 
                                                             LocalDateTime startDate, 
                                                             LocalDateTime endDate);

    /**
     * Find events by category and date range
     */
    List&lt;SystemEvent&gt; findByEventCategoryAndEventTimestampBetween(EventCategory eventCategory,
                                                                 LocalDateTime startDate,
                                                                 LocalDateTime endDate);

    /**
     * Find events by actor and date range
     */
    List&lt;SystemEvent&gt; findByActorUserIdAndEventTimestampBetween(String actorUserId,
                                                               LocalDateTime startDate,
                                                               LocalDateTime endDate);

    /**
     * Find failed events
     */
    default List&lt;SystemEvent&gt; findFailedEvents() {
<span class="nc" id="L109">        return findByProcessingStatus(&quot;FAILED&quot;);</span>
    }

    /**
     * Find events with webhook deliveries
     */
    List&lt;SystemEvent&gt; findByWebhookDeliveryCountGreaterThan(Integer count);

    /**
     * Count events by type
     */
    long countByEventType(EventType eventType);

    /**
     * Count events by category
     */
    long countByEventCategory(EventCategory eventCategory);

    /**
     * Count events by actor
     */
    long countByActorUserId(String actorUserId);

    /**
     * Count events by processing status
     */
    long countByProcessingStatus(String processingStatus);

    /**
     * Count events within date range
     */
    long countByEventTimestampBetween(LocalDateTime startDate, LocalDateTime endDate);

    /**
     * Find most recent events
     */
    List&lt;SystemEvent&gt; findTop10ByOrderByEventTimestampDesc();

    /**
     * Find most active actors
     */
    @Query(&quot;SELECT se.actorUserId, COUNT(se) FROM SystemEvent se WHERE se.actorUserId IS NOT NULL GROUP BY se.actorUserId ORDER BY COUNT(se) DESC&quot;)
    List&lt;Object[]&gt; findMostActiveActors(Pageable pageable);

    /**
     * Get event statistics by type
     */
    @Query(&quot;SELECT se.eventType, COUNT(se) FROM SystemEvent se GROUP BY se.eventType ORDER BY COUNT(se) DESC&quot;)
    List&lt;Object[]&gt; getEventStatisticsByType();

    /**
     * Get event statistics by category
     */
    @Query(&quot;SELECT se.eventCategory, COUNT(se) FROM SystemEvent se GROUP BY se.eventCategory ORDER BY COUNT(se) DESC&quot;)
    List&lt;Object[]&gt; getEventStatisticsByCategory();

    /**
     * Get event statistics by processing status
     */
    @Query(&quot;SELECT se.processingStatus, COUNT(se) FROM SystemEvent se GROUP BY se.processingStatus&quot;)
    List&lt;Object[]&gt; getEventStatisticsByProcessingStatus();

    /**
     * Get hourly event counts for the last 24 hours
     */
    @Query(&quot;SELECT HOUR(se.eventTimestamp), COUNT(se) FROM SystemEvent se WHERE se.eventTimestamp &gt;= :cutoffTime GROUP BY HOUR(se.eventTimestamp) ORDER BY HOUR(se.eventTimestamp)&quot;)
    List&lt;Object[]&gt; getHourlyEventCounts(@Param(&quot;cutoffTime&quot;) LocalDateTime cutoffTime);

    /**
     * Get daily event counts for the last 30 days
     */
    @Query(&quot;SELECT DATE(se.eventTimestamp), COUNT(se) FROM SystemEvent se WHERE se.eventTimestamp &gt;= :cutoffTime GROUP BY DATE(se.eventTimestamp) ORDER BY DATE(se.eventTimestamp)&quot;)
    List&lt;Object[]&gt; getDailyEventCounts(@Param(&quot;cutoffTime&quot;) LocalDateTime cutoffTime);

    /**
     * Find events by actor type
     */
    List&lt;SystemEvent&gt; findByActorType(String actorType);

    /**
     * Find system events (no actor)
     */
    List&lt;SystemEvent&gt; findByActorUserIdIsNull();

    /**
     * Find events with errors (failed processing)
     */
    default List&lt;SystemEvent&gt; findEventsWithErrors() {
<span class="nc" id="L197">        return findByProcessingStatus(&quot;FAILED&quot;);</span>
    }

    /**
     * Find old events for cleanup
     */
    List&lt;SystemEvent&gt; findByEventTimestampBefore(LocalDateTime cutoffDate);
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>